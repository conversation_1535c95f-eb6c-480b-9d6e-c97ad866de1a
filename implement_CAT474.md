# CAT474 - User Invite System Implementation Plan

## Overview

This document provides a comprehensive step-by-step implementation plan for the User Invite System (CAT474) based on the requirements in `/workspace/requirement.txt` and following the existing codebase patterns and architecture.

## Architecture Overview

The invite system follows the established patterns in the Synapse ITS Go microservices:

- **Shared Libraries**: Core functionality in `/shared/rest/onramp/invites/`
- **API Endpoints**: RESTful endpoints in onramp microservice
- **Database**: PostgreSQL for invite storage with BigQuery for event logging
- **Security**: Secure token generation and SHA256 hashing
- **Messaging**: PubSub for email notifications
- **Testing**: Comprehensive unit tests with dependency injection

## Implementation Phases

### Phase 1: Core Data Models and Database Operations

#### 1.1 Create Directory Structure

```bash
mkdir -p /workspace/shared/rest/onramp/invites
```

#### 1.2 Error Definitions

**File**: `shared/rest/onramp/invites/errors.go`

```go
package invites

import "errors"

var (
    ErrInvalidOrganizationId = errors.New("invalid organization ID")
    ErrInvalidUserID         = errors.New("invalid user ID")
    ErrInvalidInviteID       = errors.New("invalid invite ID")
    ErrInvalidEmail          = errors.New("invalid email address")
    ErrInvalidRequestBody    = errors.New("invalid request body")
    ErrUnexpectedFields      = errors.New("request contains unexpected fields")
    ErrDatabaseOperation     = errors.New("database operation failed")
    ErrInviteNotFound        = errors.New("invite not found")
    ErrInviteExpired         = errors.New("invite has expired")
    ErrInviteAlreadyRedeemed = errors.New("invite has already been redeemed")
    ErrInviteRevoked         = errors.New("invite has been revoked")
    ErrResendCooldown        = errors.New("must wait before resending invite")
    ErrTokenGeneration       = errors.New("failed to generate secure token")
    ErrTokenValidation       = errors.New("invalid token format")
    ErrOrganizationNotFound  = errors.New("organization not found")
    ErrCustomRoleNotFound    = errors.New("custom role not found")
    ErrInviterNotFound       = errors.New("inviter user not found")
)
```

#### 1.3 Data Models

**File**: `shared/rest/onramp/invites/models.go`

```go
package invites

import (
    "context"
    "database/sql"
    "fmt"
    "time"
    "strings"
    "encoding/json"

    "github.com/google/uuid"
    "synapse-its.com/shared/connect"
    "synapse-its.com/shared/logger"
)

// UserInvite represents the database model
type UserInvite struct {
    ID                     string     `json:"id" db:"id"`
    OrganizationIdentifier string     `json:"organizationidentifier" db:"organizationidentifier"`
    TokenHash              string     `json:"tokenhash" db:"tokenhash"`
    Email                  string     `json:"email" db:"email"`
    InviterID              string     `json:"inviterid" db:"inviterid"`
    CustomRoleID           string     `json:"customroleid" db:"customroleid"`
    Status                 string     `json:"status" db:"status"`
    Message                *string    `json:"message" db:"message"`
    RequireSSO             bool       `json:"requiresso" db:"requiresso"`
    RetryCount             int        `json:"retrycount" db:"retrycount"`
    Retried                *time.Time `json:"retried" db:"retried"`
    Expired                *time.Time `json:"expired" db:"expired"`
    Created                time.Time  `json:"created" db:"created"`
    Sent                   *time.Time `json:"sent" db:"sent"`
    Updated                time.Time  `json:"updated" db:"updated"`
}

// CreateInviteRequest represents the API request for creating invites
type CreateInviteRequest struct {
    Email            string `json:"email"`
    InviterID        string `json:"inviterid"`
    Message          string `json:"message"`
    OrganizationRole string `json:"organizationrole"`
    ExpiredDays      string `json:"expireddays"`
}

// ResendInviteRequest represents the API request for resending invites
type ResendInviteRequest struct {
    Actor       string `json:"actor"`
    Message     string `json:"message"`
    ExpiredDays string `json:"expireddays"`
}

// RevokeInviteRequest represents the API request for revoking invites
type RevokeInviteRequest struct {
    Actor string `json:"actor"`
}

// InviteStatus constants
const (
    StatusPending  = "pending"
    StatusRedeemed = "redeemed"
    StatusRevoked  = "revoked"
    StatusExpired  = "expired"
    StatusRejected = "rejected"
)

// Database Operations

// createInvite inserts a new invite into the database
var createInvite = func(pg connect.DatabaseExecutor, req *CreateInviteRequest, orgID string) (*UserInvite, string, error) {
    // Generate secure token
    token, err := generateSecureToken()
    if err != nil {
        return nil, "", fmt.Errorf("%w: %v", ErrTokenGeneration, err)
    }
    
    tokenHash := hashToken(token)
    
    // Parse expiration
    var expired *time.Time
    if req.ExpiredDays != "" {
        // Implementation for parsing expireddays
    }
    
    // Validate organization exists
    if err := validateOrganizationExists(pg, orgID); err != nil {
        return nil, "", err
    }
    
    // Validate custom role exists
    if err := validateCustomRoleExists(pg, req.OrganizationRole, orgID); err != nil {
        return nil, "", err
    }
    
    // Validate inviter exists
    if err := validateInviterExists(pg, req.InviterID); err != nil {
        return nil, "", err
    }
    
    query := `
        INSERT INTO {{UserInvites}} (
            organizationidentifier,
            tokenhash,
            email,
            inviterid,
            customroleid,
            status,
            message,
            requiresso,
            expired,
            created,
            updated
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, NOW(), NOW())
        RETURNING id, created, updated`
    
    var message *string
    if req.Message != "" {
        message = &req.Message
    }
    
    row, err := pg.QueryRow(query,
        orgID,
        tokenHash,
        req.Email,
        req.InviterID,
        req.OrganizationRole,
        StatusPending,
        message,
        false, // requiresso - default to false for now
        expired,
    )
    if err != nil {
        logger.Errorf("failed to create invite: %v", err)
        return nil, "", fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
    }
    
    // Extract the ID and timestamps
    idInterface, ok := row["id"]
    if !ok {
        return nil, "", fmt.Errorf("ID column not returned from invite insert")
    }
    
    id, ok := idInterface.(string)
    if !ok {
        return nil, "", fmt.Errorf("ID column is not a string: %T", idInterface)
    }
    
    created, _ := row["created"].(time.Time)
    updated, _ := row["updated"].(time.Time)
    
    invite := &UserInvite{
        ID:                     id,
        OrganizationIdentifier: orgID,
        TokenHash:              tokenHash,
        Email:                  req.Email,
        InviterID:              req.InviterID,
        CustomRoleID:           req.OrganizationRole,
        Status:                 StatusPending,
        Message:                message,
        RequireSSO:             false,
        RetryCount:             0,
        Retried:                nil,
        Expired:                expired,
        Created:                created,
        Sent:                   nil,
        Updated:                updated,
    }
    
    return invite, token, nil
}
```

#### 1.4 Additional Database Operations

**Continue in `shared/rest/onramp/invites/models.go`:**

```go
// getInvitesByOrganization retrieves all invites for an organization
var getInvitesByOrganization = func(pg connect.DatabaseExecutor, orgID string) ([]UserInvite, error) {
    query := `
        SELECT
            id,
            organizationidentifier,
            tokenhash,
            email,
            inviterid,
            customroleid,
            status,
            message,
            requiresso,
            retrycount,
            retried,
            expired,
            created,
            sent,
            updated
        FROM {{UserInvites}}
        WHERE organizationidentifier = $1
        ORDER BY created DESC`

    var invites []UserInvite
    err := pg.QueryGenericSlice(&invites, query, orgID)
    if err != nil {
        logger.Errorf("failed to get invites for organization %s: %v", orgID, err)
        return nil, fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
    }

    return invites, nil
}

// getInvitesByUserEmail retrieves invites for a user by matching email addresses
var getInvitesByUserEmail = func(pg connect.DatabaseExecutor, userID string) ([]UserInvite, error) {
    // First get all email addresses for the user from AuthMethod table
    emailQuery := `
        SELECT DISTINCT email
        FROM {{AuthMethod}}
        WHERE userid = $1 AND email IS NOT NULL`

    emailRows, err := pg.QueryGeneric(emailQuery, userID)
    if err != nil {
        logger.Errorf("failed to get emails for user %s: %v", userID, err)
        return nil, fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
    }

    if len(emailRows) == 0 {
        return []UserInvite{}, nil
    }

    // Build IN clause for emails
    emails := make([]string, len(emailRows))
    for i, row := range emailRows {
        emails[i] = row["email"].(string)
    }

    // Create placeholders for IN clause
    placeholders := make([]string, len(emails))
    args := make([]interface{}, len(emails))
    for i, email := range emails {
        placeholders[i] = fmt.Sprintf("$%d", i+1)
        args[i] = email
    }

    query := fmt.Sprintf(`
        SELECT
            id,
            organizationidentifier,
            tokenhash,
            email,
            inviterid,
            customroleid,
            status,
            message,
            requiresso,
            retrycount,
            retried,
            expired,
            created,
            sent,
            updated
        FROM {{UserInvites}}
        WHERE email IN (%s)
        ORDER BY created DESC`, strings.Join(placeholders, ","))

    var invites []UserInvite
    err = pg.QueryGenericSlice(&invites, query, args...)
    if err != nil {
        logger.Errorf("failed to get invites for user %s: %v", userID, err)
        return nil, fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
    }

    return invites, nil
}

// getInviteByID retrieves a single invite by ID
var getInviteByID = func(pg connect.DatabaseExecutor, inviteID string) (*UserInvite, error) {
    query := `
        SELECT
            id,
            organizationidentifier,
            tokenhash,
            email,
            inviterid,
            customroleid,
            status,
            message,
            requiresso,
            retrycount,
            retried,
            expired,
            created,
            sent,
            updated
        FROM {{UserInvites}}
        WHERE id = $1`

    invite := &UserInvite{}
    err := pg.QueryRowStruct(invite, query, inviteID)
    if err != nil {
        if err == sql.ErrNoRows {
            return nil, ErrInviteNotFound
        }
        logger.Errorf("failed to get invite by ID %s: %v", inviteID, err)
        return nil, fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
    }

    return invite, nil
}

// updateInviteStatus updates the status and timestamps of an invite
var updateInviteStatus = func(pg connect.DatabaseExecutor, inviteID, status, actor string) error {
    query := `
        UPDATE {{UserInvites}}
        SET status = $1, updated = NOW()
        WHERE id = $2`

    result, err := pg.Exec(query, status, inviteID)
    if err != nil {
        logger.Errorf("failed to update invite status: %v", err)
        return fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
    }

    rowsAffected, err := result.RowsAffected()
    if err != nil {
        return fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
    }

    if rowsAffected == 0 {
        return ErrInviteNotFound
    }

    return nil
}

// validateInviteToken checks if a token is valid and returns the invite
var validateInviteToken = func(pg connect.DatabaseExecutor, token string) (*UserInvite, error) {
    if !validateTokenFormat(token) {
        return nil, ErrTokenValidation
    }

    tokenHash := hashToken(token)

    query := `
        SELECT
            id,
            organizationidentifier,
            tokenhash,
            email,
            inviterid,
            customroleid,
            status,
            message,
            requiresso,
            retrycount,
            retried,
            expired,
            created,
            sent,
            updated
        FROM {{UserInvites}}
        WHERE tokenhash = $1`

    invite := &UserInvite{}
    err := pg.QueryRowStruct(invite, query, tokenHash)
    if err != nil {
        if err == sql.ErrNoRows {
            return nil, ErrInviteNotFound
        }
        logger.Errorf("failed to validate token: %v", err)
        return nil, fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
    }

    // Check if invite is expired
    if invite.Expired != nil && time.Now().UTC().After(*invite.Expired) {
        return nil, ErrInviteExpired
    }

    // Check if invite is in valid status
    if invite.Status != StatusPending {
        switch invite.Status {
        case StatusRedeemed:
            return nil, ErrInviteAlreadyRedeemed
        case StatusRevoked:
            return nil, ErrInviteRevoked
        case StatusExpired:
            return nil, ErrInviteExpired
        default:
            return nil, ErrInviteNotFound
        }
    }

    return &invite, nil
}
```

### Phase 2: Security Functions

#### 2.1 Token Generation and Validation

**File**: `shared/rest/onramp/invites/security.go`

```go
package invites

import (
    "crypto/rand"
    "encoding/hex"
    "regexp"

    "synapse-its.com/shared/api/security"
)

// Package-level variables for dependency injection in tests
var (
    randRead = rand.Read
)

// generateSecureToken creates a cryptographically secure random token
func generateSecureToken() (string, error) {
    // Generate 32 random bytes (will become 64 hex characters)
    bytes := make([]byte, 32)
    if _, err := randRead(bytes); err != nil {
        return "", err
    }
    return hex.EncodeToString(bytes), nil
}

// hashToken creates a SHA256 hash of the token for storage
func hashToken(token string) string {
    return security.CalculateSHA256(token)
}

// validateTokenFormat checks if a token has the correct format (64 hex characters)
func validateTokenFormat(token string) bool {
    if len(token) != 64 {
        return false
    }

    // Check if it's valid hexadecimal
    matched, _ := regexp.MatchString("^[a-fA-F0-9]{64}$", token)
    return matched
}
```

### Phase 3: BigQuery Event Logging

#### 3.1 Event Models and Logging

**File**: `shared/rest/onramp/invites/events.go`

```go
package invites

import (
    "time"

    "synapse-its.com/shared/bqbatch"
    "synapse-its.com/shared/logger"
)

// InviteEvent represents the BigQuery event structure
type InviteEvent struct {
    UserInviteID           string     `json:"userinviteid"`
    EventType              string     `json:"eventtype"`
    Actor                  string     `json:"actor"`
    EventTime              time.Time  `json:"eventtime"`
    OrganizationIdentifier string     `json:"organizationidentifier"`
    TokenHash              string     `json:"tokenhash"`
    Email                  string     `json:"email"`
    InviterID              string     `json:"inviterid"`
    OrganizationRole       string     `json:"organizationrole"`
    Status                 string     `json:"status"`
    Message                *string    `json:"message"`
    RequireSSO             bool       `json:"requiresso"`
    RetryCount             int64      `json:"retrycount"`
    Retried                *time.Time `json:"retried"`
    Expired                *time.Time `json:"expired"`
    Created                time.Time  `json:"created"`
    Sent                   *time.Time `json:"sent"`
    Updated                time.Time  `json:"updated"`
}

// Event type constants
const (
    EventTypeCreate   = "create"
    EventTypeRetry    = "retry"
    EventTypeRevoke   = "revoke"
    EventTypeRedeem   = "redeem"
    EventTypeRejected = "rejected"
)

// logInviteEvent logs an invite event to BigQuery
var logInviteEvent = func(batcher bqbatch.Batcher, eventType string, invite *UserInvite, actor string) error {
    event := &InviteEvent{
        UserInviteID:           invite.ID,
        EventType:              eventType,
        Actor:                  actor,
        EventTime:              time.Now().UTC(),
        OrganizationIdentifier: invite.OrganizationIdentifier,
        TokenHash:              invite.TokenHash,
        Email:                  invite.Email,
        InviterID:              invite.InviterID,
        OrganizationRole:       invite.CustomRoleID,
        Status:                 invite.Status,
        Message:                invite.Message,
        RequireSSO:             invite.RequireSSO,
        RetryCount:             int64(invite.RetryCount),
        Retried:                invite.Retried,
        Expired:                invite.Expired,
        Created:                invite.Created,
        Sent:                   invite.Sent,
        Updated:                invite.Updated,
    }

    if err := batcher.Add(event); err != nil {
        logger.Errorf("failed to log invite event: %v", err)
        return err
    }

    return nil
}
```

### Phase 4: Email Template System

#### 4.1 Email Templates and PubSub Publishing

**File**: `shared/rest/onramp/invites/email.go`

```go
package invites

import (
    "bytes"
    "context"
    "database/sql"
    "encoding/json"
    "fmt"
    "html/template"

    "cloud.google.com/go/pubsub"
    "synapse-its.com/shared/connect"
    "synapse-its.com/shared/logger"
)

// EmailTemplateData represents the data for email template rendering
type EmailTemplateData struct {
    AppName          string
    Message          string
    InviteLink       string
    OrganizationName string
}

// PubSubEmailMessage represents the message sent to etl-notifications topic
type PubSubEmailMessage struct {
    Type    string `json:"type"`
    To      string `json:"to"`
    Message string `json:"message"`
}

// Default email template HTML
const defaultEmailTemplate = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>You're Invited to {{.AppName}}!</title>
</head>
<body style="margin:0;padding:0;background-color:#f4f4f4;">
  <table width="100%" cellpadding="0" cellspacing="0" role="presentation">
    <tr>
      <td align="center" style="padding:20px 0;">
        <table width="600" cellpadding="0" cellspacing="0" style="background-color:#ffffff;border-radius:8px;overflow:hidden;">
          <tr>
            <td style="padding:40px;text-align:center;font-family:Arial,sans-serif;">
              <h1 style="margin:0;color:#333333;font-size:24px;">You're Invited!</h1>
            </td>
          </tr>
          <tr>
            <td style="padding:0 40px 20px;font-family:Arial,sans-serif;color:#555555;font-size:16px;line-height:1.5;">
              {{if .Message}}<p>{{.Message}}</p>{{end}}
              <p style="text-align:center;padding:20px 0;">
                <a href="{{.InviteLink}}" style="background-color:#007BFF;color:#ffffff;padding:14px 28px;text-decoration:none;border-radius:4px;display:inline-block;font-size:16px;">
                  Accept Invitation
                </a>
              </p>
              <p>If the button above doesn't work, copy and paste this link into your browser:</p>
              <p style="word-break:break-all;"><a href="{{.InviteLink}}" style="color:#007BFF;">{{.InviteLink}}</a></p>
            </td>
          </tr>
          <tr>
            <td style="padding:20px 40px;background-color:#f9f9f9;font-family:Arial,sans-serif;color:#999999;font-size:12px;">
              <p style="margin:0;">Sent by {{.AppName}} on behalf of {{.OrganizationName}}</p>
            </td>
          </tr>
        </table>
      </td>
    </tr>
  </table>
</body>
</html>`

// renderEmailTemplate renders the email template with the provided data
var renderEmailTemplate = func(data EmailTemplateData) (string, error) {
    tmpl, err := template.New("invite").Parse(defaultEmailTemplate)
    if err != nil {
        return "", fmt.Errorf("failed to parse email template: %v", err)
    }

    var buf bytes.Buffer
    if err := tmpl.Execute(&buf, data); err != nil {
        return "", fmt.Errorf("failed to execute email template: %v", err)
    }

    return buf.String(), nil
}

// getOrganizationName retrieves the organization name from the database
var getOrganizationName = func(pg connect.DatabaseExecutor, orgID string) (string, error) {
    query := `SELECT name FROM {{Organization}} WHERE id = $1 AND isdeleted = false`

    row, err := pg.QueryRow(query, orgID)
    if err != nil {
        if err == sql.ErrNoRows {
            return "", ErrOrganizationNotFound
        }
        logger.Errorf("failed to get organization name: %v", err)
        return "", fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
    }

    nameInterface, ok := row["name"]
    if !ok {
        return "", fmt.Errorf("name column not returned")
    }

    name, ok := nameInterface.(string)
    if !ok {
        return "", fmt.Errorf("name column is not a string: %T", nameInterface)
    }

    return name, nil
}

// publishEmailNotification publishes an email notification to the etl-notifications topic
var publishEmailNotification = func(psClient connect.PsClient, to, htmlContent, orgID, token string) error {
    message := PubSubEmailMessage{
        Type:    "email",
        To:      to,
        Message: htmlContent,
    }

    messageBytes, err := json.Marshal(message)
    if err != nil {
        return fmt.Errorf("failed to marshal email message: %v", err)
    }

    topic := psClient.Topic("etl-notifications")
    pubsubMsg := &pubsub.Message{
        Data: messageBytes,
    }

    result := topic.Publish(context.Background(), pubsubMsg)
    if _, err := result.Get(context.Background()); err != nil {
        logger.Errorf("failed to publish email notification: %v", err)
        return fmt.Errorf("failed to publish email notification: %v", err)
    }

    logger.Infof("Email notification published for invite to %s", to)
    return nil
}

// buildInviteLink constructs the invite validation link
func buildInviteLink(orgID, token string) string {
    return fmt.Sprintf("/api/organization/%s/invites/validate?token=%s", orgID, token)
}
```

### Phase 5: Validation and Helper Functions

#### 5.1 Validation Functions

**Continue in `shared/rest/onramp/invites/models.go`:**

```go
// Validation helper functions

// validateOrganizationExists checks if an organization exists
var validateOrganizationExists = func(pg connect.DatabaseExecutor, orgID string) error {
    query := `SELECT 1 FROM {{Organization}} WHERE id = $1 AND isdeleted = false`

    _, err := pg.QueryRow(query, orgID)
    if err != nil {
        if err == sql.ErrNoRows {
            return ErrOrganizationNotFound
        }
        return fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
    }

    return nil
}

// validateCustomRoleExists checks if a custom role exists for the organization
var validateCustomRoleExists = func(pg connect.DatabaseExecutor, roleID, orgID string) error {
    query := `SELECT 1 FROM {{CustomRole}} WHERE id = $1 AND organizationid = $2 AND isdeleted = false`

    _, err := pg.QueryRow(query, roleID, orgID)
    if err != nil {
        if err == sql.ErrNoRows {
            return ErrCustomRoleNotFound
        }
        return fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
    }

    return nil
}

// validateInviterExists checks if the inviter user exists
var validateInviterExists = func(pg connect.DatabaseExecutor, inviterID string) error {
    query := `SELECT 1 FROM {{User}} WHERE id = $1`

    _, err := pg.QueryRow(query, inviterID)
    if err != nil {
        if err == sql.ErrNoRows {
            return ErrInviterNotFound
        }
        return fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
    }

    return nil
}

// checkResendCooldown validates if enough time has passed since last retry
var checkResendCooldown = func(invite *UserInvite) error {
    if invite.Retried == nil {
        return nil // No previous retry
    }

    cooldownPeriod := time.Minute // 1 minute cooldown
    timeSinceRetry := time.Now().UTC().Sub(*invite.Retried)

    if timeSinceRetry < cooldownPeriod {
        return ErrResendCooldown
    }

    return nil
}

// updateInviteForResend updates an invite for resending
var updateInviteForResend = func(pg connect.DatabaseExecutor, inviteID, newTokenHash string, message *string, expired *time.Time) error {
    query := `
        UPDATE {{UserInvites}}
        SET tokenhash = $1,
            retrycount = retrycount + 1,
            retried = NOW(),
            message = $2,
            expired = $3,
            updated = NOW()
        WHERE id = $4`

    result, err := pg.Exec(query, newTokenHash, message, expired, inviteID)
    if err != nil {
        logger.Errorf("failed to update invite for resend: %v", err)
        return fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
    }

    rowsAffected, err := result.RowsAffected()
    if err != nil {
        return fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
    }

    if rowsAffected == 0 {
        return ErrInviteNotFound
    }

    return nil
}

// parseExpiredDays converts expireddays string to time.Time
func parseExpiredDays(expiredDays string) (*time.Time, error) {
    if expiredDays == "" {
        return nil, nil // No expiration
    }

    // Implementation depends on the expected format
    // For now, assume it's a number of days
    // This would need to be implemented based on requirements
    return nil, nil
}
```

### Phase 6: API Handlers

#### 6.1 Handler Dependencies and Structure

**File**: `shared/rest/onramp/invites/invites.go`

```go
package invites

import (
    "context"
    "encoding/json"
    "fmt"
    "net/http"
    "net/mail"

    "github.com/google/uuid"
    "github.com/gorilla/mux"
    "synapse-its.com/shared/api/response"
    "synapse-its.com/shared/bqbatch"
    "synapse-its.com/shared/connect"
    "synapse-its.com/shared/logger"
)

// HandlerDeps defines dependencies for handlers to enable dependency injection
type HandlerDeps struct {
    GetConnections              func(context.Context, ...bool) (*connect.Connections, error)
    GetBQBatch                  func(context.Context) (bqbatch.Batcher, error)
    CreateInvite                func(connect.DatabaseExecutor, *CreateInviteRequest, string) (*UserInvite, string, error)
    GetInvitesByOrganization    func(connect.DatabaseExecutor, string) ([]UserInvite, error)
    GetInvitesByUserEmail       func(connect.DatabaseExecutor, string) ([]UserInvite, error)
    GetInviteByID               func(connect.DatabaseExecutor, string) (*UserInvite, error)
    UpdateInviteStatus          func(connect.DatabaseExecutor, string, string, string) error
    UpdateInviteForResend       func(connect.DatabaseExecutor, string, string, *string, *time.Time) error
    ValidateInviteToken         func(connect.DatabaseExecutor, string) (*UserInvite, error)
    CheckResendCooldown         func(*UserInvite) error
    LogInviteEvent              func(bqbatch.Batcher, string, *UserInvite, string) error
    PublishEmailNotification    func(connect.PsClient, string, string, string, string) error
    GetOrganizationName         func(connect.DatabaseExecutor, string) (string, error)
    RenderEmailTemplate         func(EmailTemplateData) (string, error)
    GenerateSecureToken         func() (string, error)
    HashToken                   func(string) string
}

// CreateInviteHandlerWithDeps creates a new invite with dependency injection
func CreateInviteHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
    return func(w http.ResponseWriter, r *http.Request) {
        ctx := r.Context()

        // Get connections
        connections, err := deps.GetConnections(ctx)
        if err != nil {
            logger.Errorf("Error getting connections: %v", err)
            response.CreateInternalErrorResponse(w)
            return
        }
        pg := connections.Postgres

        // Get BigQuery batcher
        batcher, err := deps.GetBQBatch(ctx)
        if err != nil {
            logger.Errorf("Error getting BigQuery batcher: %v", err)
            response.CreateInternalErrorResponse(w)
            return
        }

        // Validate organization ID from path
        orgID, err := validateOrganizationIDFromPath(r)
        if err != nil {
            logger.Errorf("invalid organization ID: %v", err)
            response.CreateBadRequestResponse(w)
            return
        }

        // Parse and validate request body
        var req CreateInviteRequest
        if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
            logger.Errorf("Error parsing request body: %v", err)
            response.CreateBadRequestResponse(w)
            return
        }

        // Validate required fields
        if err := validateCreateInviteRequest(&req); err != nil {
            logger.Errorf("Invalid request: %v", err)
            response.CreateBadRequestResponse(w)
            return
        }

        // Create the invite
        invite, token, err := deps.CreateInvite(pg, &req, orgID)
        if err != nil {
            logger.Errorf("failed to create invite: %v", err)
            if err == ErrOrganizationNotFound || err == ErrCustomRoleNotFound || err == ErrInviterNotFound {
                response.CreateBadRequestResponse(w)
            } else {
                response.CreateInternalErrorResponse(w)
            }
            return
        }

        // Log event to BigQuery
        if err := deps.LogInviteEvent(batcher, EventTypeCreate, invite, req.InviterID); err != nil {
            logger.Errorf("failed to log invite event: %v", err)
            // Don't fail the request for logging errors
        }

        // Get organization name for email
        orgName, err := deps.GetOrganizationName(pg, orgID)
        if err != nil {
            logger.Errorf("failed to get organization name: %v", err)
            orgName = "Unknown Organization" // Fallback
        }

        // Render email template
        templateData := EmailTemplateData{
            AppName:          "Onramp",
            Message:          req.Message,
            InviteLink:       buildInviteLink(orgID, token),
            OrganizationName: orgName,
        }

        htmlContent, err := deps.RenderEmailTemplate(templateData)
        if err != nil {
            logger.Errorf("failed to render email template: %v", err)
            response.CreateInternalErrorResponse(w)
            return
        }

        // Publish email notification
        if err := deps.PublishEmailNotification(connections.Pubsub, req.Email, htmlContent, orgID, token); err != nil {
            logger.Errorf("failed to publish email notification: %v", err)
            // Don't fail the request for email errors
        }

        // Return success response (without sensitive token data)
        responseData := map[string]interface{}{
            "id":     invite.ID,
            "email":  invite.Email,
            "status": invite.Status,
        }

        response.CreateSuccessResponse(responseData, w)
    }
}
```

#### 6.2 Additional API Handlers

**Continue in `shared/rest/onramp/invites/invites.go`:**

```go
// ListOrgInvitesHandlerWithDeps lists all invites for an organization
func ListOrgInvitesHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
    return func(w http.ResponseWriter, r *http.Request) {
        ctx := r.Context()

        // Get connections
        connections, err := deps.GetConnections(ctx)
        if err != nil {
            logger.Errorf("Error getting connections: %v", err)
            response.CreateInternalErrorResponse(w)
            return
        }
        pg := connections.Postgres

        // Validate organization ID from path
        orgID, err := validateOrganizationIDFromPath(r)
        if err != nil {
            logger.Errorf("invalid organization ID: %v", err)
            response.CreateBadRequestResponse(w)
            return
        }

        // Get invites for organization
        invites, err := deps.GetInvitesByOrganization(pg, orgID)
        if err != nil {
            logger.Errorf("failed to get invites for organization: %v", err)
            response.CreateInternalErrorResponse(w)
            return
        }

        response.CreateSuccessResponse(invites, w)
    }
}

// ListUserInvitesHandlerWithDeps lists all invites for a user
func ListUserInvitesHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
    return func(w http.ResponseWriter, r *http.Request) {
        ctx := r.Context()

        // Get connections
        connections, err := deps.GetConnections(ctx)
        if err != nil {
            logger.Errorf("Error getting connections: %v", err)
            response.CreateInternalErrorResponse(w)
            return
        }
        pg := connections.Postgres

        // Validate user ID from path
        userID, err := validateUserIDFromPath(r)
        if err != nil {
            logger.Errorf("invalid user ID: %v", err)
            response.CreateBadRequestResponse(w)
            return
        }

        // Get invites for user
        invites, err := deps.GetInvitesByUserEmail(pg, userID)
        if err != nil {
            logger.Errorf("failed to get invites for user: %v", err)
            response.CreateInternalErrorResponse(w)
            return
        }

        response.CreateSuccessResponse(invites, w)
    }
}

// RejectInviteHandlerWithDeps rejects an invite (user action)
func RejectInviteHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
    return func(w http.ResponseWriter, r *http.Request) {
        ctx := r.Context()

        // Get connections
        connections, err := deps.GetConnections(ctx)
        if err != nil {
            logger.Errorf("Error getting connections: %v", err)
            response.CreateInternalErrorResponse(w)
            return
        }
        pg := connections.Postgres

        // Get BigQuery batcher
        batcher, err := deps.GetBQBatch(ctx)
        if err != nil {
            logger.Errorf("Error getting BigQuery batcher: %v", err)
            response.CreateInternalErrorResponse(w)
            return
        }

        // Validate user ID and invite ID from path
        userID, err := validateUserIDFromPath(r)
        if err != nil {
            logger.Errorf("invalid user ID: %v", err)
            response.CreateBadRequestResponse(w)
            return
        }

        inviteID, err := validateInviteIDFromPath(r)
        if err != nil {
            logger.Errorf("invalid invite ID: %v", err)
            response.CreateBadRequestResponse(w)
            return
        }

        // Get the invite to verify it exists and get details for logging
        invite, err := deps.GetInviteByID(pg, inviteID)
        if err != nil {
            if err == ErrInviteNotFound {
                response.CreateNotFoundResponse(w)
            } else {
                logger.Errorf("failed to get invite: %v", err)
                response.CreateInternalErrorResponse(w)
            }
            return
        }

        // Update invite status to rejected
        if err := deps.UpdateInviteStatus(pg, inviteID, StatusRejected, userID); err != nil {
            if err == ErrInviteNotFound {
                response.CreateNotFoundResponse(w)
            } else {
                logger.Errorf("failed to reject invite: %v", err)
                response.CreateInternalErrorResponse(w)
            }
            return
        }

        // Update invite status for logging
        invite.Status = StatusRejected

        // Log event to BigQuery
        if err := deps.LogInviteEvent(batcher, EventTypeRejected, invite, userID); err != nil {
            logger.Errorf("failed to log invite event: %v", err)
            // Don't fail the request for logging errors
        }

        response.CreateSuccessResponse(nil, w)
    }
}

// RevokeInviteHandlerWithDeps revokes an invite (organization action)
func RevokeInviteHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
    return func(w http.ResponseWriter, r *http.Request) {
        ctx := r.Context()

        // Get connections
        connections, err := deps.GetConnections(ctx)
        if err != nil {
            logger.Errorf("Error getting connections: %v", err)
            response.CreateInternalErrorResponse(w)
            return
        }
        pg := connections.Postgres

        // Get BigQuery batcher
        batcher, err := deps.GetBQBatch(ctx)
        if err != nil {
            logger.Errorf("Error getting BigQuery batcher: %v", err)
            response.CreateInternalErrorResponse(w)
            return
        }

        // Validate organization ID and invite ID from path
        orgID, err := validateOrganizationIDFromPath(r)
        if err != nil {
            logger.Errorf("invalid organization ID: %v", err)
            response.CreateBadRequestResponse(w)
            return
        }

        inviteID, err := validateInviteIDFromPath(r)
        if err != nil {
            logger.Errorf("invalid invite ID: %v", err)
            response.CreateBadRequestResponse(w)
            return
        }

        // Parse request body for actor
        var req RevokeInviteRequest
        if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
            logger.Errorf("Error parsing request body: %v", err)
            response.CreateBadRequestResponse(w)
            return
        }

        if req.Actor == "" {
            logger.Error("Actor is required")
            response.CreateBadRequestResponse(w)
            return
        }

        // Get the invite to verify it exists and belongs to the organization
        invite, err := deps.GetInviteByID(pg, inviteID)
        if err != nil {
            if err == ErrInviteNotFound {
                response.CreateNotFoundResponse(w)
            } else {
                logger.Errorf("failed to get invite: %v", err)
                response.CreateInternalErrorResponse(w)
            }
            return
        }

        // Verify invite belongs to the organization
        if invite.OrganizationIdentifier != orgID {
            response.CreateNotFoundResponse(w)
            return
        }

        // Update invite status to revoked
        if err := deps.UpdateInviteStatus(pg, inviteID, StatusRevoked, req.Actor); err != nil {
            if err == ErrInviteNotFound {
                response.CreateNotFoundResponse(w)
            } else {
                logger.Errorf("failed to revoke invite: %v", err)
                response.CreateInternalErrorResponse(w)
            }
            return
        }

        // Update invite status for logging
        invite.Status = StatusRevoked

        // Log event to BigQuery
        if err := deps.LogInviteEvent(batcher, EventTypeRevoke, invite, req.Actor); err != nil {
            logger.Errorf("failed to log invite event: %v", err)
            // Don't fail the request for logging errors
        }

        response.CreateSuccessResponse(nil, w)
    }
}
```

#### 6.3 Remaining API Handlers

**Continue in `shared/rest/onramp/invites/invites.go`:**

```go
// ValidateInviteHandlerWithDeps validates an invite token
func ValidateInviteHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
    return func(w http.ResponseWriter, r *http.Request) {
        ctx := r.Context()

        // Get connections
        connections, err := deps.GetConnections(ctx)
        if err != nil {
            logger.Errorf("Error getting connections: %v", err)
            response.CreateInternalErrorResponse(w)
            return
        }
        pg := connections.Postgres

        // Get token from query parameter
        token := r.URL.Query().Get("token")
        if token == "" {
            logger.Error("Token parameter is required")
            response.CreateBadRequestResponse(w)
            return
        }

        // Validate the token
        invite, err := deps.ValidateInviteToken(pg, token)
        if err != nil {
            switch err {
            case ErrInviteNotFound, ErrTokenValidation:
                response.CreateUnauthorizedResponse(w)
            case ErrInviteExpired, ErrInviteAlreadyRedeemed, ErrInviteRevoked:
                response.CreateUnauthorizedResponse(w)
            default:
                logger.Errorf("failed to validate invite token: %v", err)
                response.CreateInternalErrorResponse(w)
            }
            return
        }

        // Return success with invite details (excluding sensitive data)
        responseData := map[string]interface{}{
            "valid":        true,
            "email":        invite.Email,
            "organization": invite.OrganizationIdentifier,
            "message":      invite.Message,
        }

        response.CreateSuccessResponse(responseData, w)
    }
}

// RedeemInviteHandlerWithDeps redeems an invite
func RedeemInviteHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
    return func(w http.ResponseWriter, r *http.Request) {
        ctx := r.Context()

        // Get connections
        connections, err := deps.GetConnections(ctx)
        if err != nil {
            logger.Errorf("Error getting connections: %v", err)
            response.CreateInternalErrorResponse(w)
            return
        }
        pg := connections.Postgres

        // Get BigQuery batcher
        batcher, err := deps.GetBQBatch(ctx)
        if err != nil {
            logger.Errorf("Error getting BigQuery batcher: %v", err)
            response.CreateInternalErrorResponse(w)
            return
        }

        // Validate user ID and invite ID from path
        userID, err := validateUserIDFromPath(r)
        if err != nil {
            logger.Errorf("invalid user ID: %v", err)
            response.CreateBadRequestResponse(w)
            return
        }

        inviteID, err := validateInviteIDFromPath(r)
        if err != nil {
            logger.Errorf("invalid invite ID: %v", err)
            response.CreateBadRequestResponse(w)
            return
        }

        // Get the invite to verify it exists
        invite, err := deps.GetInviteByID(pg, inviteID)
        if err != nil {
            if err == ErrInviteNotFound {
                response.CreateNotFoundResponse(w)
            } else {
                logger.Errorf("failed to get invite: %v", err)
                response.CreateInternalErrorResponse(w)
            }
            return
        }

        // Verify invite is in pending status
        if invite.Status != StatusPending {
            response.CreateBadRequestResponse(w)
            return
        }

        // Update invite status to redeemed
        if err := deps.UpdateInviteStatus(pg, inviteID, StatusRedeemed, userID); err != nil {
            if err == ErrInviteNotFound {
                response.CreateNotFoundResponse(w)
            } else {
                logger.Errorf("failed to redeem invite: %v", err)
                response.CreateInternalErrorResponse(w)
            }
            return
        }

        // Update invite status for logging
        invite.Status = StatusRedeemed

        // Log event to BigQuery
        if err := deps.LogInviteEvent(batcher, EventTypeRedeem, invite, userID); err != nil {
            logger.Errorf("failed to log invite event: %v", err)
            // Don't fail the request for logging errors
        }

        response.CreateSuccessResponse(nil, w)
    }
}

// ResendInviteHandlerWithDeps resends an invite with new token
func ResendInviteHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
    return func(w http.ResponseWriter, r *http.Request) {
        ctx := r.Context()

        // Get connections
        connections, err := deps.GetConnections(ctx)
        if err != nil {
            logger.Errorf("Error getting connections: %v", err)
            response.CreateInternalErrorResponse(w)
            return
        }
        pg := connections.Postgres

        // Get BigQuery batcher
        batcher, err := deps.GetBQBatch(ctx)
        if err != nil {
            logger.Errorf("Error getting BigQuery batcher: %v", err)
            response.CreateInternalErrorResponse(w)
            return
        }

        // Validate organization ID and invite ID from path
        orgID, err := validateOrganizationIDFromPath(r)
        if err != nil {
            logger.Errorf("invalid organization ID: %v", err)
            response.CreateBadRequestResponse(w)
            return
        }

        inviteID, err := validateInviteIDFromPath(r)
        if err != nil {
            logger.Errorf("invalid invite ID: %v", err)
            response.CreateBadRequestResponse(w)
            return
        }

        // Parse request body
        var req ResendInviteRequest
        if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
            logger.Errorf("Error parsing request body: %v", err)
            response.CreateBadRequestResponse(w)
            return
        }

        if req.Actor == "" {
            logger.Error("Actor is required")
            response.CreateBadRequestResponse(w)
            return
        }

        // Get the invite
        invite, err := deps.GetInviteByID(pg, inviteID)
        if err != nil {
            if err == ErrInviteNotFound {
                response.CreateNotFoundResponse(w)
            } else {
                logger.Errorf("failed to get invite: %v", err)
                response.CreateInternalErrorResponse(w)
            }
            return
        }

        // Verify invite belongs to the organization
        if invite.OrganizationIdentifier != orgID {
            response.CreateNotFoundResponse(w)
            return
        }

        // Check cooldown period
        if err := deps.CheckResendCooldown(invite); err != nil {
            if err == ErrResendCooldown {
                response.CreateCustomErrorResponse("Must wait before resending invite", nil, http.StatusTooEarly, w)
            } else {
                logger.Errorf("failed to check resend cooldown: %v", err)
                response.CreateInternalErrorResponse(w)
            }
            return
        }

        // Generate new token
        newToken, err := deps.GenerateSecureToken()
        if err != nil {
            logger.Errorf("failed to generate new token: %v", err)
            response.CreateInternalErrorResponse(w)
            return
        }

        newTokenHash := deps.HashToken(newToken)

        // Parse expiration
        expired, err := parseExpiredDays(req.ExpiredDays)
        if err != nil {
            logger.Errorf("failed to parse expired days: %v", err)
            response.CreateBadRequestResponse(w)
            return
        }

        var message *string
        if req.Message != "" {
            message = &req.Message
        }

        // Update invite for resend
        if err := deps.UpdateInviteForResend(pg, inviteID, newTokenHash, message, expired); err != nil {
            if err == ErrInviteNotFound {
                response.CreateNotFoundResponse(w)
            } else {
                logger.Errorf("failed to update invite for resend: %v", err)
                response.CreateInternalErrorResponse(w)
            }
            return
        }

        // Update invite object for logging and email
        invite.TokenHash = newTokenHash
        invite.RetryCount++
        invite.Message = message
        invite.Expired = expired

        // Log event to BigQuery
        if err := deps.LogInviteEvent(batcher, EventTypeRetry, invite, req.Actor); err != nil {
            logger.Errorf("failed to log invite event: %v", err)
            // Don't fail the request for logging errors
        }

        // Get organization name for email
        orgName, err := deps.GetOrganizationName(pg, orgID)
        if err != nil {
            logger.Errorf("failed to get organization name: %v", err)
            orgName = "Unknown Organization" // Fallback
        }

        // Render email template
        templateData := EmailTemplateData{
            AppName:          "Onramp",
            Message:          req.Message,
            InviteLink:       buildInviteLink(orgID, newToken),
            OrganizationName: orgName,
        }

        htmlContent, err := deps.RenderEmailTemplate(templateData)
        if err != nil {
            logger.Errorf("failed to render email template: %v", err)
            response.CreateInternalErrorResponse(w)
            return
        }

        // Publish email notification
        if err := deps.PublishEmailNotification(connections.Pubsub, invite.Email, htmlContent, orgID, newToken); err != nil {
            logger.Errorf("failed to publish email notification: %v", err)
            // Don't fail the request for email errors
        }

        response.CreateSuccessResponse(nil, w)
    }
}
```

#### 6.4 Validation Helper Functions

**Continue in `shared/rest/onramp/invites/invites.go`:**

```go
// Validation helper functions for handlers

// validateOrganizationIDFromPath extracts and validates organization ID from URL path
func validateOrganizationIDFromPath(r *http.Request) (string, error) {
    vars := mux.Vars(r)
    orgIDStr := vars["orgid"]

    if orgIDStr == "" {
        return "", ErrInvalidOrganizationId
    }

    // Validate UUID format
    if _, err := uuid.Parse(orgIDStr); err != nil {
        return "", ErrInvalidOrganizationId
    }

    return orgIDStr, nil
}

// validateUserIDFromPath extracts and validates user ID from URL path
func validateUserIDFromPath(r *http.Request) (string, error) {
    vars := mux.Vars(r)
    userIDStr := vars["userid"]

    if userIDStr == "" {
        return "", ErrInvalidUserID
    }

    // Validate UUID format
    if _, err := uuid.Parse(userIDStr); err != nil {
        return "", ErrInvalidUserID
    }

    return userIDStr, nil
}

// validateInviteIDFromPath extracts and validates invite ID from URL path
func validateInviteIDFromPath(r *http.Request) (string, error) {
    vars := mux.Vars(r)
    inviteIDStr := vars["id"]

    if inviteIDStr == "" {
        return "", ErrInvalidInviteID
    }

    // Validate UUID format
    if _, err := uuid.Parse(inviteIDStr); err != nil {
        return "", ErrInvalidInviteID
    }

    return inviteIDStr, nil
}

// validateCreateInviteRequest validates the create invite request
func validateCreateInviteRequest(req *CreateInviteRequest) error {
    // Validate email
    if req.Email == "" {
        return ErrInvalidEmail
    }

    if _, err := mail.ParseAddress(req.Email); err != nil {
        return ErrInvalidEmail
    }

    // Validate inviter ID
    if req.InviterID == "" {
        return ErrInvalidUserID
    }

    if _, err := uuid.Parse(req.InviterID); err != nil {
        return ErrInvalidUserID
    }

    // Validate organization role
    if req.OrganizationRole == "" {
        return ErrInvalidRequestBody
    }

    if _, err := uuid.Parse(req.OrganizationRole); err != nil {
        return ErrInvalidRequestBody
    }

    return nil
}

// Production handlers using default dependencies
var (
    CreateInviteHandler = CreateInviteHandlerWithDeps(HandlerDeps{
        GetConnections:              connect.GetConnections,
        GetBQBatch:                  getBQBatchFromContext,
        CreateInvite:                createInvite,
        LogInviteEvent:              logInviteEvent,
        PublishEmailNotification:    publishEmailNotification,
        GetOrganizationName:         getOrganizationName,
        RenderEmailTemplate:         renderEmailTemplate,
        GenerateSecureToken:         generateSecureToken,
        HashToken:                   hashToken,
    })

    ListOrgInvitesHandler = ListOrgInvitesHandlerWithDeps(HandlerDeps{
        GetConnections:           connect.GetConnections,
        GetInvitesByOrganization: getInvitesByOrganization,
    })

    ListUserInvitesHandler = ListUserInvitesHandlerWithDeps(HandlerDeps{
        GetConnections:        connect.GetConnections,
        GetInvitesByUserEmail: getInvitesByUserEmail,
    })

    RejectInviteHandler = RejectInviteHandlerWithDeps(HandlerDeps{
        GetConnections:     connect.GetConnections,
        GetBQBatch:         getBQBatchFromContext,
        GetInviteByID:      getInviteByID,
        UpdateInviteStatus: updateInviteStatus,
        LogInviteEvent:     logInviteEvent,
    })

    RevokeInviteHandler = RevokeInviteHandlerWithDeps(HandlerDeps{
        GetConnections:     connect.GetConnections,
        GetBQBatch:         getBQBatchFromContext,
        GetInviteByID:      getInviteByID,
        UpdateInviteStatus: updateInviteStatus,
        LogInviteEvent:     logInviteEvent,
    })

    ValidateInviteHandler = ValidateInviteHandlerWithDeps(HandlerDeps{
        GetConnections:      connect.GetConnections,
        ValidateInviteToken: validateInviteToken,
    })

    RedeemInviteHandler = RedeemInviteHandlerWithDeps(HandlerDeps{
        GetConnections:     connect.GetConnections,
        GetBQBatch:         getBQBatchFromContext,
        GetInviteByID:      getInviteByID,
        UpdateInviteStatus: updateInviteStatus,
        LogInviteEvent:     logInviteEvent,
    })

    ResendInviteHandler = ResendInviteHandlerWithDeps(HandlerDeps{
        GetConnections:              connect.GetConnections,
        GetBQBatch:                  getBQBatchFromContext,
        GetInviteByID:               getInviteByID,
        CheckResendCooldown:         checkResendCooldown,
        UpdateInviteForResend:       updateInviteForResend,
        LogInviteEvent:              logInviteEvent,
        PublishEmailNotification:    publishEmailNotification,
        GetOrganizationName:         getOrganizationName,
        RenderEmailTemplate:         renderEmailTemplate,
        GenerateSecureToken:         generateSecureToken,
        HashToken:                   hashToken,
    })
)

// getBQBatchFromContext retrieves the BigQuery batcher from context
func getBQBatchFromContext(ctx context.Context) (bqbatch.Batcher, error) {
    // This function should retrieve the batcher from context
    // Implementation depends on how bqbatch is set up in the middleware
    return bqbatch.GetFromContext(ctx)
}
```

### Phase 7: Route Registration

#### 7.1 Update Organization Handler

**File**: `microservices/onramp/modules/organization/handler.go`

Add the invite routes to the existing organization handler:

```go
package organization

import (
    "net/http"
    "github.com/gorilla/mux"
    RestOrganization "synapse-its.com/shared/rest/onramp/organization"
    RestPermissions "synapse-its.com/shared/rest/onramp/permissions"
    RestRoles "synapse-its.com/shared/rest/onramp/roles"
    RestInvites "synapse-its.com/shared/rest/onramp/invites"  // Add this import
)

type Handler struct{}

func NewHandler() *Handler {
    return &Handler{}
}

func (h *Handler) RegisterRoutes(router *mux.Router) {
    // Existing organization routes
    router.HandleFunc("/organizations", RestOrganization.CreateHandler).Methods(http.MethodPost)
    router.HandleFunc("/organizations", RestOrganization.GetAllHandler).Methods(http.MethodGet)
    router.HandleFunc("/organizations/{identifier}", RestOrganization.GetByIdentifierHandler).Methods(http.MethodGet)
    router.HandleFunc("/organizations/{identifier}", RestOrganization.UpdateHandler).Methods(http.MethodPatch)
    router.HandleFunc("/organizations/{identifier}", RestOrganization.DeleteHandler).Methods(http.MethodDelete)
    router.HandleFunc("/organizations/{organizationId}/roles", RestRoles.GetRolesHandler).Methods(http.MethodGet)
    router.HandleFunc("/organizations/{organizationId}/roles", RestRoles.CreateRoleHandler).Methods(http.MethodPost)
    router.HandleFunc("/organizations/{organizationId}/roles/{roleId}", RestRoles.DeleteRoleHandler).Methods(http.MethodDelete)
    router.HandleFunc("/organizations/{organizationId}/roles/templates", RestRoles.GetRoleTemplatesHandler).Methods(http.MethodGet)
    router.HandleFunc("/organizations/{organizationId}/permissions", RestPermissions.GetPermissionsHandler).Methods(http.MethodGet)
    router.HandleFunc("/organizations/{organizationId}/permissions", RestPermissions.UpdatePermissionHandler).Methods(http.MethodPatch)

    // Add new invite routes
    router.HandleFunc("/organizations/{orgid}/invites", RestInvites.CreateInviteHandler).Methods(http.MethodPost)
    router.HandleFunc("/organizations/{orgid}/invites", RestInvites.ListOrgInvitesHandler).Methods(http.MethodGet)
    router.HandleFunc("/organizations/{orgid}/invites/{id}", RestInvites.RevokeInviteHandler).Methods(http.MethodDelete)
    router.HandleFunc("/organizations/{orgid}/invites/{id}/resend", RestInvites.ResendInviteHandler).Methods(http.MethodPost)
}
```

#### 7.2 Update User Handler

**File**: `microservices/onramp/modules/user/handler.go`

Add the user invite routes:

```go
package user

import (
    "net/http"

    "synapse-its.com/onramp/domain"
    "synapse-its.com/onramp/middlewares"
    RestUserPermissions "synapse-its.com/onramp/modules/user/permissions"
    RestInvites "synapse-its.com/shared/rest/onramp/invites"  // Add this import

    "github.com/gorilla/mux"
)

type Handler struct {
    sessionStore domain.SessionStore
}

func NewHandler(sessionStore domain.SessionStore) *Handler {
    return &Handler{
        sessionStore: sessionStore,
    }
}

func (h *Handler) RegisterRoutes(router *mux.Router) {
    // Apply session middleware to user routes
    userRouter := router.PathPrefix("/user").Subrouter()
    userRouter.Use(middlewares.SessionMiddleware(h.sessionStore))

    // Existing user routes
    userRouter.HandleFunc("/permissions", RestUserPermissions.Handler).Methods(http.MethodGet)

    // Add new invite routes
    userRouter.HandleFunc("/{userid}/invites", RestInvites.ListUserInvitesHandler).Methods(http.MethodGet)
    userRouter.HandleFunc("/{userid}/invites/{id}", RestInvites.RejectInviteHandler).Methods(http.MethodDelete)
    userRouter.HandleFunc("/{userid}/invites/validate", RestInvites.ValidateInviteHandler).Methods(http.MethodGet)
    userRouter.HandleFunc("/{userid}/invites/{id}/redeem", RestInvites.RedeemInviteHandler).Methods(http.MethodPost)
}
```

### Phase 8: BigQuery Integration

#### 8.1 Register BigQuery Event Table

**File**: Update the main application to register the InviteEvents table with bqbatch

In `microservices/onramp/main.go` or wherever bqbatch is initialized:

```go
// Register the InviteEvents table for BigQuery logging
func setupBigQueryTables(batcher bqbatch.Batcher) error {
    // Register InviteEvents table
    inviteEvent := &invites.InviteEvent{}
    cfg := bqbatch.QueueConfig{
        MaxRows:    1000,
        MaxWait:    30 * time.Second,
        MaxRetries: 3,
    }

    if err := batcher.Register(inviteEvent, "InviteEvents", cfg); err != nil {
        return fmt.Errorf("failed to register InviteEvents table: %v", err)
    }

    return nil
}
```

### Phase 9: Testing

#### 9.1 Unit Tests Structure

Create comprehensive unit tests for each component:

**Files to create:**
- `shared/rest/onramp/invites/models_test.go`
- `shared/rest/onramp/invites/security_test.go`
- `shared/rest/onramp/invites/events_test.go`
- `shared/rest/onramp/invites/email_test.go`
- `shared/rest/onramp/invites/invites_test.go`

**Example test structure for `invites_test.go`:**

```go
package invites

import (
    "bytes"
    "context"
    "encoding/json"
    "net/http"
    "net/http/httptest"
    "testing"
    "time"

    "github.com/gorilla/mux"
    "github.com/stretchr/testify/assert"
    "github.com/stretchr/testify/mock"
    "synapse-its.com/shared/mocks"
)

func TestCreateInviteHandler(t *testing.T) {
    tests := []struct {
        name           string
        requestBody    CreateInviteRequest
        orgID          string
        setupMocks     func(*mocks.FakeDBExecutor, *mocks.FakeBatcher, *mocks.FakePubsubClient)
        expectedStatus int
        expectedError  string
    }{
        {
            name: "successful_invite_creation",
            requestBody: CreateInviteRequest{
                Email:            "<EMAIL>",
                InviterID:        "123e4567-e89b-12d3-a456-426614174000",
                Message:          "Welcome to our organization!",
                OrganizationRole: "456e7890-e89b-12d3-a456-426614174000",
                ExpiredDays:      "30",
            },
            orgID: "789e0123-e89b-12d3-a456-426614174000",
            setupMocks: func(db *mocks.FakeDBExecutor, batcher *mocks.FakeBatcher, ps *mocks.FakePubsubClient) {
                // Setup successful database operations
                db.On("QueryRow", mock.AnythingOfType("string"), mock.Anything).Return(
                    map[string]interface{}{
                        "id":      "invite-id-123",
                        "created": time.Now(),
                        "updated": time.Now(),
                    }, nil)

                // Setup successful BigQuery logging
                batcher.On("Add", mock.AnythingOfType("*invites.InviteEvent")).Return(nil)

                // Setup successful PubSub publishing
                topic := &mocks.FakePubsubTopic{}
                result := &mocks.FakePubsubResult{}
                topic.On("Publish", mock.Anything, mock.Anything).Return(result)
                result.On("Get", mock.Anything).Return("msg-id", nil)
                ps.On("Topic", "etl-notifications").Return(topic)
            },
            expectedStatus: http.StatusOK,
        },
        // Add more test cases for error scenarios
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            // Setup mocks
            db := &mocks.FakeDBExecutor{}
            batcher := &mocks.FakeBatcher{}
            psClient := &mocks.FakePubsubClient{}

            if tt.setupMocks != nil {
                tt.setupMocks(db, batcher, psClient)
            }

            // Create handler with mocked dependencies
            deps := HandlerDeps{
                GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
                    return &connect.Connections{
                        Postgres: db,
                        Pubsub:   psClient,
                    }, nil
                },
                GetBQBatch: func(ctx context.Context) (bqbatch.Batcher, error) {
                    return batcher, nil
                },
                CreateInvite:                createInvite,
                LogInviteEvent:              logInviteEvent,
                PublishEmailNotification:    publishEmailNotification,
                GetOrganizationName:         getOrganizationName,
                RenderEmailTemplate:         renderEmailTemplate,
                GenerateSecureToken:         generateSecureToken,
                HashToken:                   hashToken,
            }

            handler := CreateInviteHandlerWithDeps(deps)

            // Create request
            body, _ := json.Marshal(tt.requestBody)
            req := httptest.NewRequest(http.MethodPost, "/api/organizations/"+tt.orgID+"/invites", bytes.NewReader(body))
            req = mux.SetURLVars(req, map[string]string{"orgid": tt.orgID})

            // Create response recorder
            w := httptest.NewRecorder()

            // Execute handler
            handler(w, req)

            // Assert response
            assert.Equal(t, tt.expectedStatus, w.Code)

            // Verify mocks were called as expected
            db.AssertExpectations(t)
            batcher.AssertExpectations(t)
            psClient.AssertExpectations(t)
        })
    }
}
```

### Phase 10: Documentation

#### 10.1 API Documentation

**File**: `shared/rest/onramp/invites/README.md`

```markdown
# User Invite System API

This package provides a comprehensive user invite system for organizations.

## Features

- Create and manage user invites
- Secure token generation and validation
- Email notifications via PubSub
- BigQuery event logging
- Comprehensive validation and error handling

## API Endpoints

### Organization Endpoints

#### Create Invite
- **POST** `/api/organizations/{orgid}/invites`
- Creates a new user invite for the organization
- Sends email notification automatically

#### List Organization Invites
- **GET** `/api/organizations/{orgid}/invites`
- Lists all invites for the organization

#### Revoke Invite
- **DELETE** `/api/organizations/{orgid}/invites/{id}`
- Revokes an invite (organization action)

#### Resend Invite
- **POST** `/api/organizations/{orgid}/invites/{id}/resend`
- Resends an invite with new token (1-minute cooldown)

### User Endpoints

#### List User Invites
- **GET** `/api/user/{userid}/invites`
- Lists all invites for the user (by email matching)

#### Reject Invite
- **DELETE** `/api/user/{userid}/invites/{id}`
- Rejects an invite (user action)

#### Validate Invite
- **GET** `/api/user/{userid}/invites/validate?token={token}`
- Validates an invite token

#### Redeem Invite
- **POST** `/api/user/{userid}/invites/{id}/redeem`
- Redeems an invite (marks as accepted)

## Security

- Cryptographically secure token generation (32 bytes)
- SHA256 token hashing for storage
- Proper UUID validation
- Email format validation
- Rate limiting for resend operations

## Event Logging

All invite operations are logged to BigQuery with the following event types:
- `create` - Invite created
- `retry` - Invite resent
- `revoke` - Invite revoked by organization
- `redeem` - Invite accepted by user
- `rejected` - Invite rejected by user

## Email Notifications

Email notifications are sent via PubSub to the `etl-notifications` topic with HTML templates.
```

## Implementation Checklist

### Phase 1: Core Data Models ✓
- [ ] Create directory structure
- [ ] Implement error definitions
- [ ] Create data models
- [ ] Implement database operations
- [ ] Add validation functions

### Phase 2: Security Functions ✓
- [ ] Implement secure token generation
- [ ] Add token hashing functions
- [ ] Create token validation

### Phase 3: BigQuery Event Logging ✓
- [ ] Create event models
- [ ] Implement event logging functions
- [ ] Register with bqbatch

### Phase 4: Email Template System ✓
- [ ] Create HTML email template
- [ ] Implement template rendering
- [ ] Add PubSub publishing
- [ ] Create organization name lookup

### Phase 5: API Handlers ✓
- [ ] Implement all 8 API handlers
- [ ] Add validation functions
- [ ] Create production handlers with dependencies

### Phase 6: Route Registration ✓
- [ ] Update organization handler
- [ ] Update user handler
- [ ] Test route registration

### Phase 7: BigQuery Integration ✓
- [ ] Register InviteEvents table
- [ ] Test event logging

### Phase 8: Testing
- [ ] Create unit tests for all components
- [ ] Add integration tests
- [ ] Test error scenarios
- [ ] Validate security measures

### Phase 9: Documentation
- [ ] Create API documentation
- [ ] Add code comments
- [ ] Update system documentation

## Deployment Notes

1. **Database Migration**: Ensure the UserInvites table is created in PostgreSQL
2. **BigQuery Schema**: Ensure the InviteEvents table is created in BigQuery
3. **Environment Variables**: Configure PubSub topic and email settings
4. **Monitoring**: Set up monitoring for invite creation and email delivery
5. **Security Review**: Conduct security review of token generation and validation

This implementation follows all existing patterns in the codebase and provides a robust, secure, and scalable invite system.
