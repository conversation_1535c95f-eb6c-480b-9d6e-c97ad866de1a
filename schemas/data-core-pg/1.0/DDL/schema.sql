-- This SQL script enables the "uuid-ossp" extension in PostgreSQL.
-- It is used to generate universally unique identifiers (UUIDs) in the database.
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- OrgType definition
-- Lookup table defining types of organizations (synapse, municipality, oem)
CREATE TABLE {{OrgType}} (
  Identifier TEXT,
  Name TEXT NOT NULL,
  Description TEXT NOT NULL,
  IsHidden BOOLEAN NOT NULL DEFAULT FALSE,
  CONSTRAINT {{OrgType_PK}} PRIMARY KEY (Identifier)
);

-- Organization definition  
-- Core organizations in the system (municipalities, OEMs, internal Synapse)
CREATE TABLE {{Organization}} (
  Id UUID DEFAULT uuid_generate_v4(),
  Name TEXT NOT NULL,
  Description TEXT NOT NULL,
  OrgTypeIdentifier TEXT NOT NULL,
  IsDeleted BOOLEAN NOT NULL DEFAULT FALSE,
  CreatedAt TIMESTAMP NOT NULL DEFAULT NOW(),
  UpdatedAt TIMESTAMP NOT NULL DEFAULT NOW(),
  CONSTRAINT {{Organization_PK}} PRIMARY KEY (Id),
  CONSTRAINT {{Organization_OrgType_FK}} FOREIGN KEY (OrgTypeIdentifier) REFERENCES {{OrgType}} (Identifier)
);

CREATE INDEX {{Organization_OrgTypeIdentifier_IDX}} ON {{Organization}} (OrgTypeIdentifier);

-- DeviceGroups definition
-- Logical groupings of devices within an organization for permission management
CREATE TABLE {{DeviceGroups}} (
  Id UUID DEFAULT uuid_generate_v4(),
  OrganizationId UUID NOT NULL,
  Name TEXT NOT NULL,
  IsDeleted BOOLEAN NOT NULL DEFAULT FALSE,
  CreatedAt TIMESTAMP NOT NULL DEFAULT NOW(),
  UpdatedAt TIMESTAMP NOT NULL DEFAULT NOW(),
  CONSTRAINT {{DeviceGroups_PK}} PRIMARY KEY (Id),
  CONSTRAINT {{DeviceGroups_Organization_FK}} FOREIGN KEY (OrganizationId) REFERENCES {{Organization}} (Id)
);

CREATE INDEX {{DeviceGroups_OrganizationId_IDX}} ON {{DeviceGroups}} (OrganizationId);

-- LocationGroups definition  
-- Logical groupings of locations within an organization for permission management
CREATE TABLE {{LocationGroups}} (
  Id UUID DEFAULT uuid_generate_v4(),
  OrganizationId UUID NOT NULL,
  Name TEXT NOT NULL,
  IsDeleted BOOLEAN NOT NULL DEFAULT FALSE,
  CreatedAt TIMESTAMP NOT NULL DEFAULT NOW(),
  UpdatedAt TIMESTAMP NOT NULL DEFAULT NOW(),
  CONSTRAINT {{LocationGroups_PK}} PRIMARY KEY (Id),
  CONSTRAINT {{LocationGroups_Organization_FK}} FOREIGN KEY (OrganizationId) REFERENCES {{Organization}} (Id)
);

CREATE INDEX {{LocationGroups_OrganizationId_IDX}} ON {{LocationGroups}} (OrganizationId);

-- PermissionGroup definition
-- Categories of permissions grouped by scope (synapse, organization, device_group, etc.)
CREATE TABLE {{PermissionGroup}} (
  Identifier TEXT,
  Scope TEXT NOT NULL,
  Weight NUMERIC(5,2) NOT NULL,
  Name TEXT NOT NULL,
  Description TEXT NOT NULL,
  CONSTRAINT {{PermissionGroup_PK}} PRIMARY KEY (Identifier)
);

-- Permission definition
-- Individual permissions that can be granted to roles, scoped by organization type
CREATE TABLE {{Permission}} (
  Identifier TEXT,
  PermissionGroupIdentifier TEXT NOT NULL,
  OrgTypeIdentifier TEXT NOT NULL,
  Weight NUMERIC(5,2) NOT NULL,
  Name TEXT NOT NULL,
  Description TEXT NOT NULL,
  CONSTRAINT {{Permission_PK}} PRIMARY KEY (Identifier),
  CONSTRAINT {{Permission_OrgType_FK}} FOREIGN KEY (OrgTypeIdentifier) REFERENCES {{OrgType}} (Identifier),
  CONSTRAINT {{Permission_PermissionGroup_FK}} FOREIGN KEY (PermissionGroupIdentifier) REFERENCES {{PermissionGroup}} (Identifier)
);

CREATE INDEX {{Permission_PermissionGroupIdentifier_IDX}} ON {{Permission}} (PermissionGroupIdentifier);
CREATE INDEX {{Permission_OrgTypeIdentifier_IDX}} ON {{Permission}} (OrgTypeIdentifier);

-- TemplateRole definition
-- Predefined role templates that define standard permission sets for organization types
CREATE TABLE {{TemplateRole}} (
  Identifier TEXT,
  Name TEXT NOT NULL,
  OrgTypeIdentifier TEXT NOT NULL,
  Description TEXT NOT NULL,
  CONSTRAINT {{TemplateRole_PK}} PRIMARY KEY (Identifier),
  CONSTRAINT {{TemplateRole_OrgType_FK}} FOREIGN KEY (OrgTypeIdentifier) REFERENCES {{OrgType}} (Identifier)
);

CREATE INDEX {{TemplateRole_OrgTypeIdentifier_IDX}} ON {{TemplateRole}} (OrgTypeIdentifier);

-- TemplateRolePermission definition
-- Default permission assignments for template roles
CREATE TABLE {{TemplateRolePermission}} (
  TemplateRoleIdentifier TEXT NOT NULL,
  PermissionIdentifier TEXT NOT NULL,
  DefaultValue BOOLEAN NOT NULL,
  IsDeleted BOOLEAN NOT NULL DEFAULT FALSE,
  CreatedAt TIMESTAMP NOT NULL DEFAULT NOW(),
  UpdatedAt TIMESTAMP NOT NULL DEFAULT NOW(),
  CONSTRAINT {{TemplateRolePermission_PK}} PRIMARY KEY (TemplateRoleIdentifier, PermissionIdentifier),
  CONSTRAINT {{TemplateRolePermission_TemplateRole_FK}} FOREIGN KEY (TemplateRoleIdentifier) REFERENCES {{TemplateRole}} (Identifier),
  CONSTRAINT {{TemplateRolePermission_Permission_FK}} FOREIGN KEY (PermissionIdentifier) REFERENCES {{Permission}} (Identifier)
);

-- Index for reverse lookups by permission
CREATE INDEX {{TemplateRolePermission_PermissionIdentifier_IDX}} ON {{TemplateRolePermission}} (PermissionIdentifier);

-- CustomRole definition
-- Organization-specific roles derived from template roles with customized permissions
CREATE TABLE {{CustomRole}} (
  Id UUID DEFAULT uuid_generate_v4(),
  OrganizationId UUID NOT NULL,
  TemplateRoleIdentifier TEXT NOT NULL,
  OrgTypeIdentifier TEXT NOT NULL,
  Name TEXT NOT NULL,
  Description TEXT NOT NULL,
  IsDeleted BOOLEAN NOT NULL DEFAULT FALSE,
  CreatedAt TIMESTAMP NOT NULL DEFAULT NOW(),
  UpdatedAt TIMESTAMP NOT NULL DEFAULT NOW(),
  CONSTRAINT {{CustomRole_PK}} PRIMARY KEY (Id),
  CONSTRAINT {{CustomRole_Organization_FK}} FOREIGN KEY (OrganizationId) REFERENCES {{Organization}} (Id),
  CONSTRAINT {{CustomRole_TemplateRole_FK}} FOREIGN KEY (TemplateRoleIdentifier) REFERENCES {{TemplateRole}} (Identifier),
  CONSTRAINT {{CustomRole_OrgType_FK}} FOREIGN KEY (OrgTypeIdentifier) REFERENCES {{OrgType}} (Identifier)
);

CREATE INDEX {{CustomRole_OrganizationId_IDX}} ON {{CustomRole}} (OrganizationId);
CREATE INDEX {{CustomRole_TemplateRoleIdentifier_IDX}} ON {{CustomRole}} (TemplateRoleIdentifier);
CREATE INDEX {{CustomRole_OrgTypeIdentifier_IDX}} ON {{CustomRole}} (OrgTypeIdentifier);

-- CustomRolePermission definition
-- Actual permission assignments for custom roles (can override template defaults)
CREATE TABLE {{CustomRolePermission}} (
  CustomRoleId UUID NOT NULL,
  PermissionIdentifier TEXT NOT NULL,
  Value BOOLEAN NOT NULL,
  IsDeleted BOOLEAN NOT NULL DEFAULT FALSE,
  CreatedAt TIMESTAMP NOT NULL DEFAULT NOW(),
  UpdatedAt TIMESTAMP NOT NULL DEFAULT NOW(),
  CONSTRAINT {{CustomRolePermission_PK}} PRIMARY KEY (CustomRoleId, PermissionIdentifier),
  CONSTRAINT {{CustomRolePermission_CustomRole_FK}} FOREIGN KEY (CustomRoleId) REFERENCES {{CustomRole}} (Id),
  CONSTRAINT {{CustomRolePermission_Permission_FK}} FOREIGN KEY (PermissionIdentifier) REFERENCES {{Permission}} (Identifier)
);

-- Index for reverse lookups by permission
CREATE INDEX {{CustomRolePermission_PermissionIdentifier_IDX}} ON {{CustomRolePermission}} (PermissionIdentifier);

-- SoftwareGatewayInstructionList definition
-- Lookup table of valid instructions that can be sent to software gateways
CREATE TABLE {{SoftwareGatewayInstructionList}} (
  Instruction TEXT,
  CONSTRAINT {{SoftwareGatewayInstructionList_PK}} PRIMARY KEY (Instruction)
);

-- SoftwareGatewayInstructionStatus definition
-- Lookup table of valid status values for software gateway instructions
CREATE TABLE {{SoftwareGatewayInstructionStatus}} (
  Status TEXT,
  CONSTRAINT {{SoftwareGatewayInstructionStatus_PK}} PRIMARY KEY (Status)
);

-- SoftwareGateway definition
-- Software agents that manage devices within an organization's network
CREATE TABLE {{SoftwareGateway}} (
  Id UUID DEFAULT uuid_generate_v4(),
  MachineKey TEXT NOT NULL,
  OrganizationId UUID NOT NULL,
  ApiKey TEXT NOT NULL,
  Token TEXT NOT NULL DEFAULT '',
  GatewayVersion TEXT,
  DateLastCheckedIn TIMESTAMP NOT NULL DEFAULT NOW(),
  PushConfigOnNextCheck BOOLEAN NOT NULL DEFAULT FALSE,
  IsEnabled BOOLEAN NOT NULL DEFAULT TRUE,
  Config TEXT NOT NULL DEFAULT '{"log_level": "error", "log_filename": "log/gateway-app.log", "log_max_backups": 10, "application_version": "deprecated", "log_max_age_in_days": 30, "log_compress_backups": true, "log_file_max_size_mb": 1, "rest_api_device_endpoint": "https://broker.caterpillar.synapse-its.app/api/v3/gateway/ingest", "send_gateway_logs_to_cloud": false, "edi_device_persist_connection": true, "edi_device_processing_retries": 5, "record_http_requests_to_folder": false, "device_state_send_frequency_seconds": 60, "config_change_check_frequency_seconds": 30, "send_gateway_performance_stats_to_cloud": true, "software_update_check_frequency_seconds": 60, "channel_state_send_frequency_milliseconds": 500, "gateway_performance_stats_output_frequency_seconds": 45, "ws_active": true, "ws_port": "8079", "ws_endpoint": "/gateway", "ws_max_connections": 2, "ws_send_frequency_milliseconds": 5000, "ws_heartbeat_send_frequency_milliseconds": 60000, "threshold_device_error_seconds": 90}',
  Name TEXT NOT NULL,
  Description TEXT NOT NULL,
  CreatedAt TIMESTAMP NOT NULL DEFAULT NOW(),
  UpdatedAt TIMESTAMP NOT NULL DEFAULT NOW(),
  IsDeleted BOOLEAN NOT NULL DEFAULT FALSE,
  CONSTRAINT {{SoftwareGateway_PK}} PRIMARY KEY (Id),
  CONSTRAINT {{SoftwareGateway_MachineKey_UQ}} UNIQUE (MachineKey),
  CONSTRAINT {{SoftwareGateway_Organization_FK}} FOREIGN KEY (OrganizationId) REFERENCES {{Organization}} (Id)
);


-- User definition
-- Core user profiles in the system
CREATE SEQUENCE {{User_OrigId_SEQ}};

CREATE TABLE {{User}} (
  Id UUID DEFAULT uuid_generate_v4(),
  OrigId INTEGER DEFAULT nextval('{{User_OrigId_SEQ}}'),
  FirstName TEXT,
  LastName TEXT,
  Mobile TEXT,
  NotificationSmsEnabled BOOLEAN NOT NULL DEFAULT FALSE,
  IanaTimezone TEXT NOT NULL DEFAULT 'America/Chicago',
  Description TEXT,
  LastLogin TIMESTAMP,
  IsDeleted BOOLEAN NOT NULL DEFAULT FALSE,
  CreatedAt TIMESTAMP NOT NULL DEFAULT NOW(),
  UpdatedAt TIMESTAMP NOT NULL DEFAULT NOW(),
  CONSTRAINT {{User_PK}} PRIMARY KEY (Id)
);

CREATE INDEX {{User_OrigId_IDX}} ON {{User}} (OrigId);

-- AuthMethod definition
-- Authentication methods for users (username/password, OIDC, etc.)
CREATE TABLE {{AuthMethod}} (
  Id UUID DEFAULT uuid_generate_v4(),
  UserId UUID NOT NULL,
  Type TEXT NOT NULL CHECK (Type IN ('USERNAME_PASSWORD', 'OIDC')),
  Sub TEXT,
  Issuer TEXT,
  UserName TEXT,
  PasswordHash TEXT,
  Email TEXT,
  Metadata JSONB,
  LastLogin TIMESTAMP,
  FailedLoginAttempts INTEGER NOT NULL DEFAULT 0,
  IsEnabled BOOLEAN NOT NULL DEFAULT TRUE,
  ForcePasswordChange BOOLEAN NOT NULL DEFAULT FALSE,
  IsDeleted BOOLEAN NOT NULL DEFAULT FALSE,
  CreatedAt TIMESTAMP NOT NULL DEFAULT NOW(),
  UpdatedAt TIMESTAMP NOT NULL DEFAULT NOW(),
  CONSTRAINT {{AuthMethod_PK}} PRIMARY KEY (Id),
  CONSTRAINT {{AuthMethod_TypeIssuerSub_UQ}} UNIQUE (Type, Issuer, Sub),
  CONSTRAINT {{AuthMethod_User_FK}} FOREIGN KEY (UserId) REFERENCES {{User}} (Id)
);

CREATE UNIQUE INDEX {{AuthMethod_UserName_UQ}} ON {{AuthMethod}} (UserName) WHERE Type = 'USERNAME_PASSWORD';
CREATE INDEX {{AuthMethod_UserId_IDX}} ON {{AuthMethod}} (UserId);


-- Session definition
-- Active user sessions for authentication and authorization
CREATE TABLE {{Session}} (
  Id UUID DEFAULT uuid_generate_v4(),
  UserId UUID NOT NULL,
  AuthMethodId UUID NOT NULL,
  Created TIMESTAMP NOT NULL,
  LastUsed TIMESTAMP,
  Expires TIMESTAMP NOT NULL,
  IpAddress TEXT,
  UserAgent TEXT,
  IsRevoked BOOLEAN NOT NULL DEFAULT FALSE,
  CONSTRAINT {{Session_PK}} PRIMARY KEY (Id),
  CONSTRAINT {{Session_User_FK}} FOREIGN KEY (UserId) REFERENCES {{User}} (Id),
  CONSTRAINT {{Session_AuthMethod_FK}} FOREIGN KEY (AuthMethodId) REFERENCES {{AuthMethod}} (Id)
);

CREATE INDEX {{Session_UserId_IDX}} ON {{Session}} (UserId);
CREATE INDEX {{Session_AuthMethodId_IDX}} ON {{Session}} (AuthMethodId);


-- Memberships definition
-- Links authentication methods to organizations they can access
CREATE TABLE {{Memberships}} (
  Id UUID DEFAULT uuid_generate_v4(),
  AuthMethodId UUID NOT NULL,
  OrganizationId UUID NOT NULL,
  IsDeleted BOOLEAN NOT NULL DEFAULT FALSE,
  CreatedAt TIMESTAMP NOT NULL DEFAULT NOW(),
  UpdatedAt TIMESTAMP NOT NULL DEFAULT NOW(),
  CONSTRAINT {{Memberships_PK}} PRIMARY KEY (Id),
  CONSTRAINT {{Memberships_AuthMethodOrganization_UQ}} UNIQUE (AuthMethodId, OrganizationId),
  CONSTRAINT {{Memberships_AuthMethod_FK}} FOREIGN KEY (AuthMethodId) REFERENCES {{AuthMethod}} (Id),
  CONSTRAINT {{Memberships_Organization_FK}} FOREIGN KEY (OrganizationId) REFERENCES {{Organization}} (Id)
);

CREATE INDEX {{Memberships_AuthMethodId_IDX}} ON {{Memberships}} (AuthMethodId);
CREATE INDEX {{Memberships_OrganizationId_IDX}} ON {{Memberships}} (OrganizationId);

-- OrgRoleAssignments definition
-- Assigns organization-wide roles to memberships
CREATE TABLE {{OrgRoleAssignments}} (
  MembershipId UUID NOT NULL,
  RoleId UUID NOT NULL,
  IsDeleted BOOLEAN NOT NULL DEFAULT FALSE,
  CreatedAt TIMESTAMP NOT NULL DEFAULT NOW(),
  UpdatedAt TIMESTAMP NOT NULL DEFAULT NOW(),
  CONSTRAINT {{OrgRoleAssignments_PK}} PRIMARY KEY (MembershipId, RoleId),
  CONSTRAINT {{OrgRoleAssignments_Membership_FK}} FOREIGN KEY (MembershipId) REFERENCES {{Memberships}} (Id),
  CONSTRAINT {{OrgRoleAssignments_CustomRole_FK}} FOREIGN KEY (RoleId) REFERENCES {{CustomRole}} (Id)
);

CREATE INDEX {{OrgRoleAssignments_MembershipId_IDX}} ON {{OrgRoleAssignments}} (MembershipId);
CREATE INDEX {{OrgRoleAssignments_RoleId_IDX}} ON {{OrgRoleAssignments}} (RoleId);

-- DeviceGroupRoleAssignments definition
-- Assigns device group-scoped roles to memberships
CREATE TABLE {{DeviceGroupRoleAssignments}} (
  MembershipId UUID NOT NULL,
  DeviceGroupId UUID NOT NULL,
  RoleId UUID NOT NULL,
  IsDeleted BOOLEAN NOT NULL DEFAULT FALSE,
  CreatedAt TIMESTAMP NOT NULL DEFAULT NOW(),
  UpdatedAt TIMESTAMP NOT NULL DEFAULT NOW(),
  CONSTRAINT {{DeviceGroupRoleAssignments_PK}} PRIMARY KEY (MembershipId, DeviceGroupId, RoleId),
  CONSTRAINT {{DeviceGroupRoleAssignments_Membership_FK}} FOREIGN KEY (MembershipId) REFERENCES {{Memberships}} (Id),
  CONSTRAINT {{DeviceGroupRoleAssignments_DeviceGroup_FK}} FOREIGN KEY (DeviceGroupId) REFERENCES {{DeviceGroups}} (Id),
  CONSTRAINT {{DeviceGroupRoleAssignments_CustomRole_FK}} FOREIGN KEY (RoleId) REFERENCES {{CustomRole}} (Id)
);

CREATE INDEX {{DeviceGroupRoleAssignments_MembershipId_IDX}} ON {{DeviceGroupRoleAssignments}} (MembershipId);
CREATE INDEX {{DeviceGroupRoleAssignments_DeviceGroupId_IDX}} ON {{DeviceGroupRoleAssignments}} (DeviceGroupId);
CREATE INDEX {{DeviceGroupRoleAssignments_RoleId_IDX}} ON {{DeviceGroupRoleAssignments}} (RoleId);

-- LocationGroupRoleAssignments definition  
-- Assigns location group-scoped roles to memberships
CREATE TABLE {{LocationGroupRoleAssignments}} (
  MembershipId UUID NOT NULL,
  LocationGroupId UUID NOT NULL,
  RoleId UUID NOT NULL,
  IsDeleted BOOLEAN NOT NULL DEFAULT FALSE,
  CreatedAt TIMESTAMP NOT NULL DEFAULT NOW(),
  UpdatedAt TIMESTAMP NOT NULL DEFAULT NOW(),
  CONSTRAINT {{LocationGroupRoleAssignments_PK}} PRIMARY KEY (MembershipId, LocationGroupId, RoleId),
  CONSTRAINT {{LocationGroupRoleAssignments_Membership_FK}} FOREIGN KEY (MembershipId) REFERENCES {{Memberships}} (Id),
  CONSTRAINT {{LocationGroupRoleAssignments_LocationGroup_FK}} FOREIGN KEY (LocationGroupId) REFERENCES {{LocationGroups}} (Id),
  CONSTRAINT {{LocationGroupRoleAssignments_CustomRole_FK}} FOREIGN KEY (RoleId) REFERENCES {{CustomRole}} (Id)
);

CREATE INDEX {{LocationGroupRoleAssignments_MembershipId_IDX}} ON {{LocationGroupRoleAssignments}} (MembershipId);
CREATE INDEX {{LocationGroupRoleAssignments_LocationGroupId_IDX}} ON {{LocationGroupRoleAssignments}} (LocationGroupId);
CREATE INDEX {{LocationGroupRoleAssignments_RoleId_IDX}} ON {{LocationGroupRoleAssignments}} (RoleId);

-- UserSoftwareGateway definition
-- Legacy table linking users directly to software gateways (replaced by role-based system)
-- TODO: Remove this table when we remove the legacy system
CREATE TABLE {{UserSoftwareGateway}} (
  SoftwareGatewayId UUID NOT NULL,
  UserId UUID NOT NULL,
  PermissionId INTEGER NOT NULL,
  CreatedAt TIMESTAMP NOT NULL DEFAULT NOW(),
  UpdatedAt TIMESTAMP NOT NULL DEFAULT NOW(),
  IsDeleted BOOLEAN NOT NULL DEFAULT FALSE,
  CONSTRAINT {{UserSoftwareGateway_PK}} PRIMARY KEY (UserId, SoftwareGatewayId),
  CONSTRAINT {{UserSoftwareGateway_SoftwareGateway_FK}} FOREIGN KEY (SoftwareGatewayId) REFERENCES {{SoftwareGateway}} (Id),
  CONSTRAINT {{UserSoftwareGateway_User_FK}} FOREIGN KEY (UserId) REFERENCES {{User}} (Id)
);

-- Index for reverse lookups
CREATE INDEX {{UserSoftwareGateway_SoftwareGatewayId_IDX}} ON {{UserSoftwareGateway}} (SoftwareGatewayId);

-- UserToken definition
-- JWT tokens for user authentication
CREATE TABLE {{UserToken}} (
  UserId UUID NOT NULL,
  JwtToken TEXT NOT NULL,
  JwtTokenSha256 TEXT NOT NULL,
  Created TIMESTAMP NOT NULL,
  Expiration TIMESTAMP NOT NULL,
  CONSTRAINT {{UserToken_User_FK}} FOREIGN KEY (UserId) REFERENCES {{User}} (Id)
);

CREATE INDEX {{UserToken_UserId_IDX}} ON {{UserToken}} (UserId);

-- Location definition
-- Physical locations within an organization where devices can be deployed
CREATE TABLE {{Location}} (
  Id UUID DEFAULT uuid_generate_v4(),
  OrganizationId UUID NOT NULL,
  Name TEXT NOT NULL,
  Description TEXT NOT NULL,
  Latitude NUMERIC(10, 8) NOT NULL,
  Longitude NUMERIC(11, 8) NOT NULL,
  IsDeleted BOOLEAN NOT NULL DEFAULT FALSE,
  CreatedAt TIMESTAMP NOT NULL DEFAULT NOW(),
  UpdatedAt TIMESTAMP NOT NULL DEFAULT NOW(),
  CONSTRAINT {{Location_PK}} PRIMARY KEY (Id),
  CONSTRAINT {{Location_Organization_FK}} FOREIGN KEY (OrganizationId) REFERENCES {{Organization}} (Id)
);

CREATE INDEX {{Location_OrganizationId_IDX}} ON {{Location}} (OrganizationId);

-- DeviceType definition
-- Lookup table of valid device types
CREATE TABLE {{DeviceType}} (
  Type TEXT,
  Description TEXT NOT NULL,
  CONSTRAINT {{DeviceType_PK}} PRIMARY KEY (Type)
);

-- Device definition
-- Physical devices managed by software gateways within an organization
CREATE SEQUENCE {{Device_OrigId_SEQ}};

CREATE TABLE {{Device}} (
  Id UUID DEFAULT uuid_generate_v4(),
  OrigId INTEGER DEFAULT nextval('{{Device_OrigId_SEQ}}'),
  SoftwareGatewayId UUID,
  LocationId UUID NOT NULL,
  Name TEXT NOT NULL,
  Description TEXT NOT NULL,
  IpAddress TEXT NOT NULL,
  Port INTEGER NOT NULL,
  Type TEXT NOT NULL DEFAULT 'UNKNOWN_DEVICE',
  FlushConnectionMs INTEGER NOT NULL,
  EnableRealtime BOOLEAN NOT NULL DEFAULT TRUE,
  IsEnabled BOOLEAN NOT NULL DEFAULT TRUE,
  CreatedAt TIMESTAMP NOT NULL DEFAULT NOW(),
  UpdatedAt TIMESTAMP NOT NULL DEFAULT NOW(),
  IsDeleted BOOLEAN NOT NULL DEFAULT FALSE,
  CONSTRAINT {{Device_PK}} PRIMARY KEY (Id),
  CONSTRAINT {{Device_SoftwareGateway_FK}} FOREIGN KEY (SoftwareGatewayId) REFERENCES {{SoftwareGateway}} (Id),
  CONSTRAINT {{Device_Location_FK}} FOREIGN KEY (LocationId) REFERENCES {{Location}} (Id),
  CONSTRAINT {{Device_DeviceType_FK}} FOREIGN KEY (Type) REFERENCES {{DeviceType}} (Type)
);

CREATE INDEX {{Device_OrigId_IDX}} ON {{Device}} (OrigId);
CREATE INDEX {{Device_SoftwareGatewayId_IDX}} ON {{Device}} (SoftwareGatewayId);
CREATE INDEX {{Device_LocationId_IDX}} ON {{Device}} (LocationId);

-- SoftwareGatewayInstruction definition
-- Instructions queued for execution by software gateways on specific devices
CREATE TABLE {{SoftwareGatewayInstruction}} (
  UserId UUID NOT NULL,
  DeviceId UUID NOT NULL,
  Instruction TEXT NOT NULL,
  DateQueued TIMESTAMP NOT NULL,
  DateReceived TIMESTAMP,
  Status TEXT NOT NULL,
  CONSTRAINT {{SoftwareGatewayInstruction_Device_FK}} FOREIGN KEY (DeviceId) REFERENCES {{Device}} (Id),
  CONSTRAINT {{SoftwareGatewayInstruction_Instruction_FK}} FOREIGN KEY (Instruction) REFERENCES {{SoftwareGatewayInstructionList}} (Instruction),
  CONSTRAINT {{SoftwareGatewayInstruction_Status_FK}} FOREIGN KEY (Status) REFERENCES {{SoftwareGatewayInstructionStatus}} (Status),
  CONSTRAINT {{SoftwareGatewayInstruction_User_FK}} FOREIGN KEY (UserId) REFERENCES {{User}} (Id)
);

CREATE INDEX {{SoftwareGatewayInstruction_Status_IDX}} ON {{SoftwareGatewayInstruction}} (Status);

-- DeviceGroupDevices definition
-- Many-to-many mapping between device groups and devices
CREATE TABLE {{DeviceGroupDevices}} (
  DeviceGroupId UUID NOT NULL,
  DeviceId UUID NOT NULL,
  CONSTRAINT {{DeviceGroupDevices_PK}} PRIMARY KEY (DeviceGroupId, DeviceId),
  CONSTRAINT {{DeviceGroupDevices_DeviceGroup_FK}} FOREIGN KEY (DeviceGroupId) REFERENCES {{DeviceGroups}} (Id),
  CONSTRAINT {{DeviceGroupDevices_Device_FK}} FOREIGN KEY (DeviceId) REFERENCES {{Device}} (Id)
);

-- Indexes for DeviceGroupDevices table (bi-directional lookups)
CREATE INDEX {{DeviceGroupDevices_DeviceGroupId_IDX}} ON {{DeviceGroupDevices}} (DeviceGroupId);
CREATE INDEX {{DeviceGroupDevices_DeviceId_IDX}} ON {{DeviceGroupDevices}} (DeviceId);

-- LocationGroupLocations definition
-- Many-to-many mapping between location groups and locations
CREATE TABLE {{LocationGroupLocations}} (
  LocationGroupId UUID NOT NULL,
  LocationId UUID NOT NULL,
  CONSTRAINT {{LocationGroupLocations_PK}} PRIMARY KEY (LocationGroupId, LocationId),
  CONSTRAINT {{LocationGroupLocations_LocationGroup_FK}} FOREIGN KEY (LocationGroupId) REFERENCES {{LocationGroups}} (Id),
  CONSTRAINT {{LocationGroupLocations_Location_FK}} FOREIGN KEY (LocationId) REFERENCES {{Location}} (Id)
);

-- Indexes for LocationGroupLocations table (bi-directional lookups)
CREATE INDEX {{LocationGroupLocations_LocationGroupId_IDX}} ON {{LocationGroupLocations}} (LocationGroupId);
CREATE INDEX {{LocationGroupLocations_LocationId_IDX}} ON {{LocationGroupLocations}} (LocationId);

-- DeviceMonitorName definition
-- Current monitor configuration for devices
CREATE TABLE {{DeviceMonitorName}} (
  DeviceId UUID,
  MonitorId INTEGER,
  MonitorName TEXT,
  PubsubTimestamp TIMESTAMP NOT NULL,
  UpdatedAt TIMESTAMP NOT NULL,
  CONSTRAINT {{DeviceMonitorName_PK}} PRIMARY KEY (DeviceId),
  CONSTRAINT {{DeviceMonitorName_Device_FK}} FOREIGN KEY (DeviceId) REFERENCES {{Device}} (Id)
);

-- DeviceRMSEngine definition
-- Engine version information for devices
CREATE TABLE {{DeviceRMSEngine}} (
  DeviceId UUID,
  EngineVersion TEXT,
  EngineRevision TEXT,
  PubsubTimestamp TIMESTAMP NOT NULL,
  UpdatedAt TIMESTAMP NOT NULL,
  CONSTRAINT {{DeviceRMSEngine_PK}} PRIMARY KEY (DeviceId),
  CONSTRAINT {{DeviceRMSEngine_Device_FK}} FOREIGN KEY (DeviceId) REFERENCES {{Device}} (Id)
);

-- DeviceFault definition
-- Current fault status and diagnostics for devices
CREATE TABLE {{DeviceFault}} (
  DeviceId UUID,
  IsFaulted BOOLEAN,
  Fault TEXT,
  FaultStatus TEXT,
  ChannelGreenStatus BOOLEAN[],
  ChannelYellowStatus BOOLEAN[],
  ChannelRedStatus BOOLEAN[],
  MonitorTime TIMESTAMP,
  Temperature INTEGER,
  VoltagesGreen INTEGER[],
  VoltagesYellow INTEGER[],
  VoltagesRed INTEGER[],
  DeviceModel TEXT,
  CONSTRAINT {{DeviceFault_PK}} PRIMARY KEY (DeviceId),
  CONSTRAINT {{DeviceFault_Device_FK}} FOREIGN KEY (DeviceId) REFERENCES {{Device}} (Id)
);

-- DeviceLog definition
-- Log upload tracking for devices
CREATE TABLE {{DeviceLog}} (
  DeviceId UUID,
  DateUploaded TIMESTAMP NOT NULL,
  LogId UUID NOT NULL,
  CONSTRAINT {{DeviceLog_PK}} PRIMARY KEY (DeviceId),
  CONSTRAINT {{DeviceLog_Device_FK}} FOREIGN KEY (DeviceId) REFERENCES {{Device}} (Id)
);

-- DeviceMacAddress definition
-- MAC address information for devices
CREATE TABLE {{DeviceMacAddress}} (
  DeviceId UUID,
  MacAddress TEXT,
  PubsubTimestamp TIMESTAMP NOT NULL,
  UpdatedAt TIMESTAMP NOT NULL,
  CONSTRAINT {{DeviceMacAddress_PK}} PRIMARY KEY (DeviceId),
  CONSTRAINT {{DeviceMacAddress_Device_FK}} FOREIGN KEY (DeviceId) REFERENCES {{Device}} (Id)
);

-- UserInvites definition
-- UserInvites table for storing invitation data
CREATE TABLE {{UserInvites}} (
  id UUID NOT NULL DEFAULT gen_random_uuid(),
  organizationidentifier UUID NOT NULL,
  tokenhash TEXT NOT NULL,
  email TEXT NOT NULL,
  inviterid UUID NOT NULL,
  customroleid UUID NOT NULL,
  status TEXT NOT NULL,
  message TEXT,
  requiresso BOOLEAN NOT NULL DEFAULT FALSE,
  retrycount INT NOT NULL DEFAULT 0,
  retried TIMESTAMPTZ,
  expired TIMESTAMPTZ,
  created TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  sent TIMESTAMPTZ,
  updated TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  CONSTRAINT {{UserInvites_PK}} PRIMARY KEY (id),
  CONSTRAINT {{UserInvites_tokenhash_UQ}} UNIQUE (tokenhash),
  CONSTRAINT {{UserInvites_inviterid_FK}} FOREIGN KEY (inviterid) REFERENCES {{User}} (id),
  CONSTRAINT {{UserInvites_org_FK}} FOREIGN KEY (organizationidentifier) REFERENCES {{Organization}} (id),
  CONSTRAINT {{UserInvites_status_CHK}} CHECK (status IN ('pending','redeemed','revoked','expired','rejected')),
  CONSTRAINT {{UserInvites_CustomRole_FK}} FOREIGN KEY (customroleid) REFERENCES {{CustomRole}} (Id)
);
CREATE INDEX {{UserInvites_organizationidentifier_IDX}} ON {{UserInvites}} (organizationidentifier);
CREATE INDEX {{UserInvites_email_IDX}} ON {{UserInvites}} (email);
CREATE INDEX {{UserInvites_status_IDX}} ON {{UserInvites}} (status);