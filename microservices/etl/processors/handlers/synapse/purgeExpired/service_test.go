package purgeExpired

import (
	"context"
	"encoding/base64"
	"errors"
	"fmt"
	"testing"
	"time"

	gatewayv1 "bitbucket.org/synapse-its/protobuf-schemas/protomessages/gateway/v1"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"google.golang.org/protobuf/proto"
	apiShared "synapse-its.com/shared/api"
	"synapse-its.com/shared/mocks"
)

// Mock implementations
type mockPersistenceRepository struct {
	mock.Mock
}

func (m *mockPersistenceRepository) RemoveExpiredTokens() (int64, error) {
	args := m.Called()
	return args.Get(0).(int64), args.Error(1)
}

func (m *mockPersistenceRepository) RemoveProcessedInstructions() (int64, error) {
	args := m.Called()
	return args.Get(0).(int64), args.Error(1)
}

func (m *mockPersistenceRepository) GetAllDevicesAndKeys() (OrgDevSet, []string, error) {
	args := m.Called()
	return args.Get(0).(OrgDevSet), args.Get(1).([]string), args.Error(2)
}

type mockCacheRepository struct {
	mock.Mock
}

func (m *mockCacheRepository) GetGatewayRMSDataKeys(ctx context.Context) ([]string, error) {
	args := m.Called(ctx)
	return args.Get(0).([]string), args.Error(1)
}

func (m *mockCacheRepository) GetAllGatewayRMSData(ctx context.Context, keys []string) ([]RedisGatewayRMSData, error) {
	args := m.Called(ctx, keys)
	return args.Get(0).([]RedisGatewayRMSData), args.Error(1)
}

func (m *mockCacheRepository) DeleteInvalidRMSData(ctx context.Context, keys []string) (int64, error) {
	args := m.Called(ctx, keys)
	return args.Get(0).(int64), args.Error(1)
}

func TestService_PurgeExpiredData(t *testing.T) {
	tests := []struct {
		name                  string
		setupMocks            func(*mockPersistenceRepository, *mockCacheRepository, *mocks.FakeFirestoreClient)
		expectedError         bool
		expectedErrorContains string
	}{
		{
			name: "successful purge",
			setupMocks: func(persistence *mockPersistenceRepository, cache *mockCacheRepository, firestore *mocks.FakeFirestoreClient) {
				// Setup firestore with some initial data
				firestore.SeedDoc("org1", "_state", map[string]any{
					"device1": map[string]any{"status": "ok"},
					"device2": map[string]any{"status": "error"},
				})
				firestore.SeedDoc("org1", "device1", map[string]any{"status": "ok"})
				firestore.SeedDoc("org1", "device2", map[string]any{"status": "error"})

				// Mock persistence calls
				persistence.On("RemoveExpiredTokens").Return(int64(5), nil)
				persistence.On("RemoveProcessedInstructions").Return(int64(10), nil)
				persistence.On("GetAllDevicesAndKeys").Return(OrgDevSet{
					"org1": DeviceSet{
						"device1": &Device{Status: "ok"},
						"device2": &Device{Status: "error"},
					},
				}, []string{"GatewayRMSData:key1", "GatewayRMSData:key2"}, nil)

				// Mock cache calls
				cache.On("GetGatewayRMSDataKeys", mock.Anything).Return([]string{"GatewayRMSData:key1", "GatewayRMSData:key2"}, nil)

				// Create test data
				deviceData := &gatewayv1.DeviceData{
					Messages: []*gatewayv1.DeviceEntry{
						{DeviceId: "device1"},
					},
				}
				protoData, _ := proto.Marshal(deviceData)
				encodedData := base64.StdEncoding.EncodeToString(protoData)

				redisData := &apiShared.RedisData{
					MsgVersion:      "1.0",
					MsgData:         encodedData,
					MsgTimestamp:    time.Now().UTC().Format(time.RFC3339),
					GatewayTimezone: "UTC",
				}

				cache.On("GetAllGatewayRMSData", mock.Anything, []string{"GatewayRMSData:key1", "GatewayRMSData:key2"}).Return([]RedisGatewayRMSData{redisData, redisData}, nil)
				cache.On("DeleteInvalidRMSData", mock.Anything, mock.Anything).Return(int64(0), nil)
			},
			expectedError: false,
		},
		{
			name: "error deleting unused devices on firestore",
			setupMocks: func(persistence *mockPersistenceRepository, cache *mockCacheRepository, firestore *mocks.FakeFirestoreClient) {
				// Mock persistence to return error
				persistence.On("GetAllDevicesAndKeys").Return(OrgDevSet{}, []string{}, errors.New("database error"))
			},
			expectedError:         true,
			expectedErrorContains: "database error",
		},
		{
			name: "error removing expired tokens",
			setupMocks: func(persistence *mockPersistenceRepository, cache *mockCacheRepository, firestore *mocks.FakeFirestoreClient) {
				// Setup firestore
				firestore.SeedDoc("org1", "_state", map[string]any{})

				// Mock persistence calls
				persistence.On("GetAllDevicesAndKeys").Return(OrgDevSet{
					"org1": DeviceSet{},
				}, []string{}, nil)
				persistence.On("RemoveExpiredTokens").Return(int64(0), errors.New("token removal error"))

				// Mock cache call for empty keys (called in deleteUnusedDevicesOnFirestore)
				cache.On("GetAllGatewayRMSData", mock.Anything, []string{}).Return([]RedisGatewayRMSData{}, nil)
			},
			expectedError:         true,
			expectedErrorContains: "token removal error",
		},
		{
			name: "error removing processed instructions",
			setupMocks: func(persistence *mockPersistenceRepository, cache *mockCacheRepository, firestore *mocks.FakeFirestoreClient) {
				// Setup firestore
				firestore.SeedDoc("org1", "_state", map[string]any{})

				// Mock persistence calls
				persistence.On("GetAllDevicesAndKeys").Return(OrgDevSet{
					"org1": DeviceSet{},
				}, []string{}, nil)
				persistence.On("RemoveExpiredTokens").Return(int64(5), nil)
				persistence.On("RemoveProcessedInstructions").Return(int64(0), errors.New("instruction removal error"))

				// Mock cache call for empty keys (called in deleteUnusedDevicesOnFirestore)
				cache.On("GetAllGatewayRMSData", mock.Anything, []string{}).Return([]RedisGatewayRMSData{}, nil)
			},
			expectedError:         true,
			expectedErrorContains: "instruction removal error",
		},
		{
			name: "error updating non-reporting device states",
			setupMocks: func(persistence *mockPersistenceRepository, cache *mockCacheRepository, firestore *mocks.FakeFirestoreClient) {
				// Setup firestore
				firestore.SeedDoc("org1", "_state", map[string]any{})

				// Mock persistence calls
				persistence.On("GetAllDevicesAndKeys").Return(OrgDevSet{
					"org1": DeviceSet{},
				}, []string{}, nil)
				persistence.On("RemoveExpiredTokens").Return(int64(5), nil)
				persistence.On("RemoveProcessedInstructions").Return(int64(10), nil)

				// Mock cache calls
				cache.On("GetAllGatewayRMSData", mock.Anything, []string{}).Return([]RedisGatewayRMSData{}, nil) // Called in deleteUnusedDevicesOnFirestore
				cache.On("GetGatewayRMSDataKeys", mock.Anything).Return([]string{}, errors.New("cache error"))   // Called in updateNonReportingDeviceStatesToError
			},
			expectedError:         true,
			expectedErrorContains: "cache error",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create mocks
			persistence := &mockPersistenceRepository{}
			cache := &mockCacheRepository{}
			firestore := mocks.NewFakeFirestoreClient()

			// Setup mocks
			tt.setupMocks(persistence, cache, firestore)

			// Create service
			service := &service{
				persistence:     persistence,
				cache:           cache,
				firestoreClient: firestore,
			}

			// Execute test
			err := service.PurgeExpiredData(context.Background())

			// Assertions
			if tt.expectedError {
				assert.Error(t, err)
				if tt.expectedErrorContains != "" {
					assert.Contains(t, err.Error(), tt.expectedErrorContains)
				}
			} else {
				assert.NoError(t, err)
			}

			// Verify all mocks were called as expected
			persistence.AssertExpectations(t)
			cache.AssertExpectations(t)
		})
	}
}

func TestService_updateNonReportingDeviceStatesToError(t *testing.T) {
	tests := []struct {
		name                  string
		setupMocks            func(*mockCacheRepository)
		expectedCount         int64
		expectedError         bool
		expectedErrorContains string
	}{
		{
			name: "successful update with expired data",
			setupMocks: func(cache *mockCacheRepository) {
				// Mock cache calls
				cache.On("GetGatewayRMSDataKeys", mock.Anything).Return([]string{"GatewayRMSData:key1", "GatewayRMSData:key2"}, nil)

				// Create expired data (more than 1 hour old)
				expiredTimestamp := time.Now().UTC().Add(-2 * time.Hour).Format(time.RFC3339)
				redisData := &apiShared.RedisData{
					MsgVersion:      "1.0",
					MsgData:         "test-data",
					MsgTimestamp:    expiredTimestamp,
					GatewayTimezone: "UTC",
				}

				cache.On("GetAllGatewayRMSData", mock.Anything, []string{"GatewayRMSData:key1", "GatewayRMSData:key2"}).Return([]RedisGatewayRMSData{redisData, redisData}, nil)
				cache.On("DeleteInvalidRMSData", mock.Anything, []string{"GatewayRMSData:key1", "GatewayRMSData:key2"}).Return(int64(2), nil)
			},
			expectedCount: 2,
			expectedError: false,
		},
		{
			name: "successful update with mixed data",
			setupMocks: func(cache *mockCacheRepository) {
				// Mock cache calls
				cache.On("GetGatewayRMSDataKeys", mock.Anything).Return([]string{"GatewayRMSData:key1", "GatewayRMSData:key2", "GatewayRMSData:key3"}, nil)

				// Create mixed data (some expired, some not)
				expiredTimestamp := time.Now().UTC().Add(-2 * time.Hour).Format(time.RFC3339)
				currentTimestamp := time.Now().UTC().Format(time.RFC3339)

				redisData1 := &apiShared.RedisData{
					MsgVersion:      "1.0",
					MsgData:         "test-data",
					MsgTimestamp:    expiredTimestamp,
					GatewayTimezone: "UTC",
				}
				redisData2 := &apiShared.RedisData{
					MsgVersion:      "1.0",
					MsgData:         "test-data",
					MsgTimestamp:    currentTimestamp,
					GatewayTimezone: "UTC",
				}

				cache.On("GetAllGatewayRMSData", mock.Anything, []string{"GatewayRMSData:key1", "GatewayRMSData:key2", "GatewayRMSData:key3"}).Return([]RedisGatewayRMSData{redisData1, redisData2, nil}, nil)
				cache.On("DeleteInvalidRMSData", mock.Anything, []string{"GatewayRMSData:key1"}).Return(int64(1), nil)
			},
			expectedCount: 1,
			expectedError: false,
		},
		{
			name: "error getting gateway RMS data keys",
			setupMocks: func(cache *mockCacheRepository) {
				cache.On("GetGatewayRMSDataKeys", mock.Anything).Return([]string{}, errors.New("redis scan error"))
			},
			expectedError:         true,
			expectedErrorContains: "error getting gateway RMS data keys",
		},
		{
			name: "error getting gateway RMS raw data",
			setupMocks: func(cache *mockCacheRepository) {
				cache.On("GetGatewayRMSDataKeys", mock.Anything).Return([]string{"GatewayRMSData:key1"}, nil)
				cache.On("GetAllGatewayRMSData", mock.Anything, []string{"GatewayRMSData:key1"}).Return(([]RedisGatewayRMSData)(nil), errors.New("redis mget error"))
			},
			expectedError:         true,
			expectedErrorContains: "error getting gateway RMS raw data",
		},
		{
			name: "error parsing timestamp",
			setupMocks: func(cache *mockCacheRepository) {
				cache.On("GetGatewayRMSDataKeys", mock.Anything).Return([]string{"GatewayRMSData:key1"}, nil)

				// Create data with invalid timestamp
				redisData := &apiShared.RedisData{
					MsgVersion:      "1.0",
					MsgData:         "test-data",
					MsgTimestamp:    "invalid-timestamp",
					GatewayTimezone: "UTC",
				}

				cache.On("GetAllGatewayRMSData", mock.Anything, []string{"GatewayRMSData:key1"}).Return([]RedisGatewayRMSData{redisData}, nil)
				cache.On("DeleteInvalidRMSData", mock.Anything, []string{}).Return(int64(0), nil)
			},
			expectedCount: 0,
			expectedError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create mocks
			cache := &mockCacheRepository{}
			firestore := mocks.NewFakeFirestoreClient()

			// Setup mocks
			tt.setupMocks(cache)

			// Create service
			service := &service{
				cache:           cache,
				firestoreClient: firestore,
			}

			// Execute test
			count, err := service.updateNonReportingDeviceStatesToError(context.Background())

			// Assertions
			if tt.expectedError {
				assert.Error(t, err)
				if tt.expectedErrorContains != "" {
					assert.Contains(t, err.Error(), tt.expectedErrorContains)
				}
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedCount, count)
			}

			// Verify all mocks were called as expected
			cache.AssertExpectations(t)
		})
	}
}

func TestService_deleteUnusedDevicesOnFirestore(t *testing.T) {
	tests := []struct {
		name                  string
		setupMocks            func(*mockPersistenceRepository, *mockCacheRepository, *mocks.FakeFirestoreClient)
		expectedError         bool
		expectedErrorContains string
	}{
		{
			name: "successful deletion with reported devices",
			setupMocks: func(persistence *mockPersistenceRepository, cache *mockCacheRepository, firestore *mocks.FakeFirestoreClient) {
				// Setup firestore with initial data
				firestore.SeedDoc("org1", "_state", map[string]any{
					"device1": map[string]any{"status": "ok"},
					"device2": map[string]any{"status": "error"},
				})
				firestore.SeedDoc("org1", "device1", map[string]any{"status": "ok"})
				firestore.SeedDoc("org1", "device2", map[string]any{"status": "error"})
				firestore.SeedDoc("org1", "device3", map[string]any{"status": "ok"}) // Will be deleted

				// Mock persistence
				persistence.On("GetAllDevicesAndKeys").Return(OrgDevSet{
					"org1": DeviceSet{
						"device1": &Device{Status: "ok"},
						"device2": &Device{Status: "error"},
					},
				}, []string{"GatewayRMSData:key1"}, nil)

				// Create test device data
				deviceData := &gatewayv1.DeviceData{
					Messages: []*gatewayv1.DeviceEntry{
						{DeviceId: "device1"},
						{DeviceId: "device2"},
					},
				}
				protoData, _ := proto.Marshal(deviceData)
				encodedData := base64.StdEncoding.EncodeToString(protoData)

				redisData := &apiShared.RedisData{
					MsgVersion:      "1.0",
					MsgData:         encodedData,
					MsgTimestamp:    time.Now().UTC().Format(time.RFC3339),
					GatewayTimezone: "UTC",
				}

				cache.On("GetAllGatewayRMSData", mock.Anything, []string{"GatewayRMSData:key1"}).Return([]RedisGatewayRMSData{redisData}, nil)
			},
			expectedError: false,
		},
		{
			name: "error getting all devices and keys",
			setupMocks: func(persistence *mockPersistenceRepository, cache *mockCacheRepository, firestore *mocks.FakeFirestoreClient) {
				persistence.On("GetAllDevicesAndKeys").Return(OrgDevSet{}, []string{}, errors.New("database error"))
			},
			expectedError:         true,
			expectedErrorContains: "database error",
		},
		{
			name: "error getting gateway RMS raw data",
			setupMocks: func(persistence *mockPersistenceRepository, cache *mockCacheRepository, firestore *mocks.FakeFirestoreClient) {
				persistence.On("GetAllDevicesAndKeys").Return(OrgDevSet{
					"org1": DeviceSet{},
				}, []string{"GatewayRMSData:key1"}, nil)
				cache.On("GetAllGatewayRMSData", mock.Anything, []string{"GatewayRMSData:key1"}).Return(([]RedisGatewayRMSData)(nil), errors.New("redis error"))
			},
			expectedError:         true,
			expectedErrorContains: "error getting gateway RMS raw data",
		},
		{
			name: "error decoding base64 data",
			setupMocks: func(persistence *mockPersistenceRepository, cache *mockCacheRepository, firestore *mocks.FakeFirestoreClient) {
				persistence.On("GetAllDevicesAndKeys").Return(OrgDevSet{
					"org1": DeviceSet{},
				}, []string{"GatewayRMSData:key1"}, nil)

				// Create data with invalid base64
				redisData := &apiShared.RedisData{
					MsgVersion:      "1.0",
					MsgData:         "invalid-base64!@#",
					MsgTimestamp:    time.Now().UTC().Format(time.RFC3339),
					GatewayTimezone: "UTC",
				}

				cache.On("GetAllGatewayRMSData", mock.Anything, []string{"GatewayRMSData:key1"}).Return([]RedisGatewayRMSData{redisData}, nil)
			},
			expectedError: false, // Should continue processing other devices
		},
		{
			name: "nil gateway RMS data - should skip",
			setupMocks: func(persistence *mockPersistenceRepository, cache *mockCacheRepository, firestore *mocks.FakeFirestoreClient) {
				persistence.On("GetAllDevicesAndKeys").Return(OrgDevSet{
					"org1": DeviceSet{},
				}, []string{"GatewayRMSData:key1", "GatewayRMSData:key2"}, nil)

				// Return nil for one key, valid data for another
				deviceData := &gatewayv1.DeviceData{
					Messages: []*gatewayv1.DeviceEntry{
						{DeviceId: "device1"},
					},
				}
				protoData, _ := proto.Marshal(deviceData)
				encodedData := base64.StdEncoding.EncodeToString(protoData)

				redisData := &apiShared.RedisData{
					MsgVersion:      "1.0",
					MsgData:         encodedData,
					MsgTimestamp:    time.Now().UTC().Format(time.RFC3339),
					GatewayTimezone: "UTC",
				}

				cache.On("GetAllGatewayRMSData", mock.Anything, []string{"GatewayRMSData:key1", "GatewayRMSData:key2"}).Return([]RedisGatewayRMSData{nil, redisData}, nil)
			},
			expectedError: false, // Should continue processing and handle nil data gracefully
		},
		{
			name: "protobuf unmarshalling failure - should skip",
			setupMocks: func(persistence *mockPersistenceRepository, cache *mockCacheRepository, firestore *mocks.FakeFirestoreClient) {
				persistence.On("GetAllDevicesAndKeys").Return(OrgDevSet{
					"org1": DeviceSet{},
				}, []string{"GatewayRMSData:key1", "GatewayRMSData:key2"}, nil)

				// Create valid base64 data but invalid protobuf content
				invalidProtoData := []byte("invalid-protobuf-data")
				encodedData := base64.StdEncoding.EncodeToString(invalidProtoData)

				redisData := &apiShared.RedisData{
					MsgVersion:      "1.0",
					MsgData:         encodedData,
					MsgTimestamp:    time.Now().UTC().Format(time.RFC3339),
					GatewayTimezone: "UTC",
				}

				// Also include valid data to ensure processing continues
				deviceData := &gatewayv1.DeviceData{
					Messages: []*gatewayv1.DeviceEntry{
						{DeviceId: "device1"},
					},
				}
				validProtoData, _ := proto.Marshal(deviceData)
				validEncodedData := base64.StdEncoding.EncodeToString(validProtoData)

				validRedisData := &apiShared.RedisData{
					MsgVersion:      "1.0",
					MsgData:         validEncodedData,
					MsgTimestamp:    time.Now().UTC().Format(time.RFC3339),
					GatewayTimezone: "UTC",
				}

				cache.On("GetAllGatewayRMSData", mock.Anything, []string{"GatewayRMSData:key1", "GatewayRMSData:key2"}).Return([]RedisGatewayRMSData{redisData, validRedisData}, nil)
			},
			expectedError: false, // Should continue processing and handle protobuf unmarshalling errors gracefully
		},
		{
			name: "error from updateDeviceStateOnFirestore",
			setupMocks: func(persistence *mockPersistenceRepository, cache *mockCacheRepository, firestore *mocks.FakeFirestoreClient) {
				persistence.On("GetAllDevicesAndKeys").Return(OrgDevSet{
					"org1": DeviceSet{
						"device1": &Device{Status: "ok"},
					},
				}, []string{"GatewayRMSData:key1"}, nil)

				// Create valid device data
				deviceData := &gatewayv1.DeviceData{
					Messages: []*gatewayv1.DeviceEntry{
						{DeviceId: "device1"},
					},
				}
				protoData, _ := proto.Marshal(deviceData)
				encodedData := base64.StdEncoding.EncodeToString(protoData)

				redisData := &apiShared.RedisData{
					MsgVersion:      "1.0",
					MsgData:         encodedData,
					MsgTimestamp:    time.Now().UTC().Format(time.RFC3339),
					GatewayTimezone: "UTC",
				}

				cache.On("GetAllGatewayRMSData", mock.Anything, []string{"GatewayRMSData:key1"}).Return([]RedisGatewayRMSData{redisData}, nil)

				// Set error for the Get operation in updateDeviceStateOnFirestore
				firestore.GetError = errors.New("firestore get error")
			},
			expectedError:         true,
			expectedErrorContains: "error syncing organization org1 with firestore",
		},
		{
			name: "error from deleteNonExistentDevicesOnFirestore",
			setupMocks: func(persistence *mockPersistenceRepository, cache *mockCacheRepository, firestore *mocks.FakeFirestoreClient) {
				persistence.On("GetAllDevicesAndKeys").Return(OrgDevSet{
					"org1": DeviceSet{
						"device1": &Device{Status: "ok"},
					},
				}, []string{"GatewayRMSData:key1"}, nil)

				// Create valid device data
				deviceData := &gatewayv1.DeviceData{
					Messages: []*gatewayv1.DeviceEntry{
						{DeviceId: "device1"},
					},
				}
				protoData, _ := proto.Marshal(deviceData)
				encodedData := base64.StdEncoding.EncodeToString(protoData)

				redisData := &apiShared.RedisData{
					MsgVersion:      "1.0",
					MsgData:         encodedData,
					MsgTimestamp:    time.Now().UTC().Format(time.RFC3339),
					GatewayTimezone: "UTC",
				}

				cache.On("GetAllGatewayRMSData", mock.Anything, []string{"GatewayRMSData:key1"}).Return([]RedisGatewayRMSData{redisData}, nil)

				// Set error for the Next operation in deleteNonExistentDevicesOnFirestore
				firestore.NextError = errors.New("firestore iteration error")
			},
			expectedError:         true,
			expectedErrorContains: "error deleting non-existent devices from firestore",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create mocks
			persistence := &mockPersistenceRepository{}
			cache := &mockCacheRepository{}
			firestore := mocks.NewFakeFirestoreClient()

			// Setup mocks
			tt.setupMocks(persistence, cache, firestore)

			// Create service
			service := &service{
				persistence:     persistence,
				cache:           cache,
				firestoreClient: firestore,
			}

			// Execute test
			err := service.deleteUnusedDevicesOnFirestore(context.Background())

			// Assertions
			if tt.expectedError {
				assert.Error(t, err)
				if tt.expectedErrorContains != "" {
					assert.Contains(t, err.Error(), tt.expectedErrorContains)
				}
			} else {
				assert.NoError(t, err)
			}

			// Verify all mocks were called as expected
			persistence.AssertExpectations(t)
			cache.AssertExpectations(t)
		})
	}
}

func TestService_updateDeviceStateOnFirestore(t *testing.T) {
	tests := []struct {
		name                  string
		orgId                 string
		persistedDevices      DeviceSet
		reportedDevices       DeviceSet
		setupFirestore        func(*mocks.FakeFirestoreClient)
		expectedError         bool
		expectedErrorContains string
		expectedDeviceStates  map[string]string
	}{
		{
			name:  "successful update with existing state document",
			orgId: "org1",
			persistedDevices: DeviceSet{
				"device1": &Device{Status: "ok"},
				"device2": &Device{Status: "ok"},
			},
			reportedDevices: DeviceSet{
				"device1": &Device{Status: "ok"},
			},
			setupFirestore: func(firestore *mocks.FakeFirestoreClient) {
				firestore.SeedDoc("org1", "_state", map[string]any{
					"device1": map[string]any{"status": "ok"},
					"device2": map[string]any{"status": "ok"},
				})
			},
			expectedError: false,
		},
		{
			name:  "successful update with non-existent state document",
			orgId: "org1",
			persistedDevices: DeviceSet{
				"device1": &Device{Status: "ok"},
			},
			reportedDevices: DeviceSet{
				"device1": &Device{Status: "ok"},
			},
			setupFirestore: func(firestore *mocks.FakeFirestoreClient) {
				// No state document exists
			},
			expectedError: false,
		},
		{
			name:  "successful update with device removal",
			orgId: "org1",
			persistedDevices: DeviceSet{
				"device1": &Device{Status: "ok"},
			},
			reportedDevices: DeviceSet{
				"device1": &Device{Status: "ok"},
			},
			setupFirestore: func(firestore *mocks.FakeFirestoreClient) {
				firestore.SeedDoc("org1", "_state", map[string]any{
					"device1": map[string]any{"status": "ok"},
					"device2": map[string]any{"status": "ok"}, // Should be removed since not in persistedDevices
				})
			},
			expectedError: false,
		},
		{
			name:  "no modifications - no update to firestore",
			orgId: "org1",
			persistedDevices: DeviceSet{
				"device1": &Device{Status: "ok"},
				"device2": &Device{Status: "ok"},
			},
			reportedDevices: DeviceSet{
				"device1": &Device{Status: "ok"},
				"device2": &Device{Status: "ok"}, // Both devices are reported, so no changes needed
			},
			setupFirestore: func(firestore *mocks.FakeFirestoreClient) {
				firestore.SeedDoc("org1", "_state", map[string]any{
					"device1": map[string]any{"status": "ok"},
					"device2": map[string]any{"status": "ok"},
				})
			},
			expectedError: false,
		},
		{
			name:  "error getting state document",
			orgId: "org1",
			persistedDevices: DeviceSet{
				"device1": &Device{Status: "ok"},
			},
			reportedDevices: DeviceSet{
				"device1": &Device{Status: "ok"},
			},
			setupFirestore: func(firestore *mocks.FakeFirestoreClient) {
				firestore.GetError = errors.New("firestore get error")
			},
			expectedError:         true,
			expectedErrorContains: "error getting state document for organization org1",
		},
		{
			name:  "error unmarshalling state document",
			orgId: "org1",
			persistedDevices: DeviceSet{
				"device1": &Device{Status: "ok"},
			},
			reportedDevices: DeviceSet{
				"device1": &Device{Status: "ok"},
			},
			setupFirestore: func(firestore *mocks.FakeFirestoreClient) {
				// Seed with invalid data structure
				firestore.SeedDoc("org1", "_state", map[string]any{
					"device1": "invalid-device-structure",
				})
			},
			expectedError:         true,
			expectedErrorContains: "error unmarshalling state document for organization org1",
		},
		{
			name:  "error setting state document",
			orgId: "org1",
			persistedDevices: DeviceSet{
				"device1": &Device{Status: "ok"},
				"device2": &Device{Status: "ok"},
			},
			reportedDevices: DeviceSet{
				"device1": &Device{Status: "ok"},
			},
			setupFirestore: func(firestore *mocks.FakeFirestoreClient) {
				// Seed with initial state that will be modified
				firestore.SeedDoc("org1", "_state", map[string]any{
					"device1": map[string]any{"status": "ok"},
					"device2": map[string]any{"status": "ok"},
				})
				// Set error for the Set operation
				firestore.SetError = errors.New("firestore set error")
			},
			expectedError:         true,
			expectedErrorContains: "error setting state document for organization org1",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create firestore mock
			firestore := mocks.NewFakeFirestoreClient()

			// Setup firestore
			tt.setupFirestore(firestore)

			// Create service
			service := &service{
				firestoreClient: firestore,
			}

			// Execute test
			err := service.updateDeviceStateOnFirestore(context.Background(), tt.orgId, tt.persistedDevices, tt.reportedDevices)

			// Assertions
			if tt.expectedError {
				assert.Error(t, err)
				if tt.expectedErrorContains != "" {
					assert.Contains(t, err.Error(), tt.expectedErrorContains)
				}
			} else {
				assert.NoError(t, err)

				// Check final state if expected
				if tt.expectedDeviceStates != nil {
					docs := firestore.GetAllDocs(tt.orgId)
					stateDoc, exists := docs["_state"]
					if exists {
						for deviceId, expectedStatus := range tt.expectedDeviceStates {
							deviceData, deviceExists := stateDoc[deviceId]
							if deviceExists {
								// Handle the case where DataTo might not work properly with Firestore fake
								if deviceMap, ok := deviceData.(map[string]any); ok {
									if status, hasStatus := deviceMap["status"]; hasStatus {
										assert.Equal(t, expectedStatus, status)
									} else {
										assert.Fail(t, "Device %s missing status field", deviceId)
									}
								} else {
									assert.Fail(t, "Device %s data is not a map", deviceId)
								}
							} else {
								// If device is not in expectedDeviceStates, it should not exist in Firestore
								assert.False(t, deviceExists, "Device %s should not exist in Firestore", deviceId)
							}
						}

						// Check that no unexpected devices exist in Firestore
						for deviceId := range stateDoc {
							if deviceId != "_state" { // Skip the state document itself
								_, expected := tt.expectedDeviceStates[deviceId]
								assert.True(t, expected, "Unexpected device %s found in Firestore", deviceId)
							}
						}
					}
				}
			}
		})
	}
}

func TestService_deleteNonExistentDevicesOnFirestore(t *testing.T) {
	tests := []struct {
		name                  string
		orgId                 string
		persistedDevices      DeviceSet
		setupFirestore        func(*mocks.FakeFirestoreClient)
		expectedError         bool
		expectedErrorContains string
		expectedRemainingDocs []string
	}{
		{
			name:  "successful deletion of non-existent devices",
			orgId: "org1",
			persistedDevices: DeviceSet{
				"device1": &Device{Status: "ok"},
				"device2": &Device{Status: "ok"},
			},
			setupFirestore: func(firestore *mocks.FakeFirestoreClient) {
				firestore.SeedDoc("org1", "device1", map[string]any{"status": "ok"})
				firestore.SeedDoc("org1", "device2", map[string]any{"status": "ok"})
				firestore.SeedDoc("org1", "device3", map[string]any{"status": "ok"}) // Should be deleted
				firestore.SeedDoc("org1", "_state", map[string]any{"status": "ok"})  // Should not be deleted
			},
			expectedError:         false,
			expectedRemainingDocs: []string{"device1", "device2", "_state"},
		},
		{
			name:  "error getting document IDs",
			orgId: "org1",
			persistedDevices: DeviceSet{
				"device1": &Device{Status: "ok"},
			},
			setupFirestore: func(firestore *mocks.FakeFirestoreClient) {
				firestore.NextError = errors.New("firestore iteration error")
			},
			expectedError:         true,
			expectedErrorContains: "error getting realtime documents from firestore",
		},
		{
			name:  "error during batch delete",
			orgId: "org1",
			persistedDevices: DeviceSet{
				"device1": &Device{Status: "ok"},
			},
			setupFirestore: func(firestore *mocks.FakeFirestoreClient) {
				firestore.SeedDoc("org1", "device2", map[string]any{"status": "ok"}) // Should be deleted
				firestore.DeleteError = errors.New("firestore delete error")
			},
			expectedError:         true,
			expectedErrorContains: "error deleting device from firestore",
		},
		{
			name:  "error during batch flush",
			orgId: "org1",
			persistedDevices: DeviceSet{
				"device1": &Device{Status: "ok"},
			},
			setupFirestore: func(firestore *mocks.FakeFirestoreClient) {
				firestore.SeedDoc("org1", "device2", map[string]any{"status": "ok"}) // Should be deleted
				firestore.FlushError = errors.New("firestore flush error")
			},
			expectedError:         true,
			expectedErrorContains: "error flushing final batch",
		},
		{
			name:  "error during batch flush at limit",
			orgId: "org1",
			persistedDevices: DeviceSet{
				"device1": &Device{Status: "ok"},
			},
			setupFirestore: func(firestore *mocks.FakeFirestoreClient) {
				// Seed many devices to trigger the batch limit (498+ devices)
				for i := 1; i <= 500; i++ {
					deviceName := fmt.Sprintf("device%d", i)
					firestore.SeedDoc("org1", deviceName, map[string]any{"status": "ok"})
				}
				firestore.FlushError = errors.New("firestore batch flush error")
			},
			expectedError:         true,
			expectedErrorContains: "error flushing batch",
		},
		{
			name:  "successful batch flush at limit - counter reset",
			orgId: "org1",
			persistedDevices: DeviceSet{
				"device1": &Device{Status: "ok"},
			},
			setupFirestore: func(firestore *mocks.FakeFirestoreClient) {
				// Seed many devices to trigger the batch limit (498+ devices)
				for i := 1; i <= 500; i++ {
					deviceName := fmt.Sprintf("device%d", i)
					firestore.SeedDoc("org1", deviceName, map[string]any{"status": "ok"})
				}
				// No FlushError set, so the flush will succeed and counter should be reset to 0
			},
			expectedError: false, // Should succeed and reset the counter
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create firestore mock
			firestore := mocks.NewFakeFirestoreClient()

			// Setup firestore
			tt.setupFirestore(firestore)

			// Create service
			service := &service{
				firestoreClient: firestore,
			}

			// Execute test
			err := service.deleteNonExistentDevicesOnFirestore(context.Background(), tt.orgId, tt.persistedDevices)

			// Assertions
			if tt.expectedError {
				assert.Error(t, err)
				if tt.expectedErrorContains != "" {
					assert.Contains(t, err.Error(), tt.expectedErrorContains)
				}
			} else {
				assert.NoError(t, err)

				// Check remaining documents
				if tt.expectedRemainingDocs != nil {
					docs := firestore.GetAllDocs(tt.orgId)
					for _, expectedDoc := range tt.expectedRemainingDocs {
						assert.Contains(t, docs, expectedDoc)
					}
					assert.Equal(t, len(tt.expectedRemainingDocs), len(docs))
				}
			}
		})
	}
}

func TestService_getAllDocumentIDsInCollection(t *testing.T) {
	tests := []struct {
		name                  string
		collectionName        string
		setupFirestore        func(*mocks.FakeFirestoreClient)
		expectedDocIDs        []string
		expectedError         bool
		expectedErrorContains string
	}{
		{
			name:           "successful retrieval of document IDs",
			collectionName: "org1",
			setupFirestore: func(firestore *mocks.FakeFirestoreClient) {
				firestore.SeedDoc("org1", "device1", map[string]any{"status": "ok"})
				firestore.SeedDoc("org1", "device2", map[string]any{"status": "error"})
				firestore.SeedDoc("org1", "_state", map[string]any{"status": "ok"})
			},
			expectedDocIDs: []string{"device1", "device2", "_state"},
			expectedError:  false,
		},
		{
			name:           "empty collection",
			collectionName: "empty-org",
			setupFirestore: func(firestore *mocks.FakeFirestoreClient) {
				// No documents seeded
			},
			expectedDocIDs: []string{},
			expectedError:  false,
		},
		{
			name:           "error during iteration",
			collectionName: "org1",
			setupFirestore: func(firestore *mocks.FakeFirestoreClient) {
				firestore.SeedDoc("org1", "device1", map[string]any{"status": "ok"})
				firestore.NextError = errors.New("firestore iteration error")
			},
			expectedError:         true,
			expectedErrorContains: "firestore iteration error",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create firestore mock
			firestore := mocks.NewFakeFirestoreClient()

			// Setup firestore
			tt.setupFirestore(firestore)

			// Create service
			service := &service{
				firestoreClient: firestore,
			}

			// Execute test
			docIDs, err := service.getAllDocumentIDsInCollection(context.Background(), tt.collectionName)

			// Assertions
			if tt.expectedError {
				assert.Error(t, err)
				if tt.expectedErrorContains != "" {
					assert.Contains(t, err.Error(), tt.expectedErrorContains)
				}
			} else {
				assert.NoError(t, err)
				assert.ElementsMatch(t, tt.expectedDocIDs, docIDs)
			}
		})
	}
}
