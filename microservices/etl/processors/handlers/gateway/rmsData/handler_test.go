package rmsData

import (
	"context"
	"errors"
	"testing"
	"time"

	gatewayv1 "bitbucket.org/synapse-its/protobuf-schemas/protomessages/gateway/v1"
	"cloud.google.com/go/pubsub"
	"google.golang.org/protobuf/proto"
	"synapse-its.com/shared/bqbatch"
	"synapse-its.com/shared/connect"
	helper "synapse-its.com/shared/devices/edi/helper"
	mocks "synapse-its.com/shared/mocks"
	"synapse-its.com/shared/pubsubdata"
	"synapse-its.com/shared/schemas"
)

func TestHandlerWithDeps(t *testing.T) {
	tests := []struct {
		name         string
		connectorErr error // error from Connector
		receiveErr   error // error from Subscription.Receive
		attrErr      error // error from ParseAttributes
		unmarshalErr error // error from UnmarshalDevice
		messages     []*gatewayv1.DeviceEntry
		batchErr     error // error from GetBatch
		processErr   bool  // error from ProcessRMS
		batchAddErr  error // error from batch.Add
		marshalErr   error // error from MarshalProto
		sendDlqErr   error // error from sendToDlq

		wantDLQ  int
		wantAdds int
	}{
		{"Connector error", errors.New("no conn"), nil, nil, nil, nil, nil, false, nil, nil, nil, 0, 0},
		{"Receive error", nil, errors.New("recv fail"), nil, nil, nil, nil, false, nil, nil, nil, 0, 0},
		{"Attribute unmarshal error", nil, nil, errors.New("fail attr parse"), nil, nil, nil, false, nil, nil, nil, 1, 0},
		{"Attribute unmarshal error + dlq error", nil, nil, errors.New("fail attr parse"), nil, nil, nil, false, nil, nil, errors.New("DLQ error"), 1, 0},
		{"Unmarshal error", nil, nil, nil, errors.New("bad proto"), nil, nil, false, nil, nil, nil, 1, 0},
		{"Unmarshal error + dlq error", nil, nil, nil, errors.New("bad proto"), nil, nil, false, nil, nil, errors.New("DLQ error"), 1, 0},
		{"GetBatch error", nil, nil, nil, nil, []*gatewayv1.DeviceEntry{{DeviceId: "d1", Message: []byte("x")}}, errors.New("no batch"), false, nil, nil, nil, 0, 0},
		{"ProcessRMS error", nil, nil, nil, nil, []*gatewayv1.DeviceEntry{{DeviceId: "d2", Message: []byte("m")}}, nil, true, nil, nil, nil, 1, 0},
		{"ProcessRMS error", nil, nil, nil, nil, []*gatewayv1.DeviceEntry{{DeviceId: "d2", Message: []byte("m")}}, nil, true, nil, nil, errors.New("DLQ error"), 1, 0},
		{"Batch Add error", nil, nil, nil, nil, []*gatewayv1.DeviceEntry{{DeviceId: "d3", Message: []byte("y")}}, nil, false, errors.New("add fail"), nil, nil, 1, 1},
		{"Marshal error path", nil, nil, nil, nil, []*gatewayv1.DeviceEntry{{DeviceId: "ee", Message: []byte("msg")}}, nil, true, nil, errors.New("marshal fail"), nil, 0, 0},
		{"Happy path", nil, nil, nil, nil, []*gatewayv1.DeviceEntry{{DeviceId: "d4", Message: []byte("z")}}, nil, false, nil, nil, nil, 0, 1},
	}

	for _, tc := range tests {
		tc := tc
		t.Run(tc.name, func(t *testing.T) {
			t.Parallel()
			ctx := context.Background()
			dlqCount := 0
			addCount := 0

			// Setup fake connections and Pub/Sub client
			conn := mocks.FakeConns()
			client := conn.Pubsub.(*mocks.FakePubsubClient)
			client.ReceiveError = tc.receiveErr

			// Setup FakeBatcher
			fakeBatch, _ := mocks.FakeBatch(ctx)
			fakeBatch.(*mocks.FakeBatcher).AddFn = func(row interface{}) error {
				addCount++
				return tc.batchAddErr
			}

			// Build HandlerDeps
			deps := HandlerDeps{
				Connector: ConnectorFunc(func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
					return conn, tc.connectorErr
				}),
				SendToDLQ: DLQSender(func(ctx context.Context, c connect.PsClient, msg *pubsub.Message, reason string) error {
					dlqCount++
					return tc.sendDlqErr
				}),
				UnmarshalDevice: UnmarshalDeviceDataFunc(func(raw []byte) (*gatewayv1.DeviceData, error) {
					if tc.unmarshalErr != nil {
						return nil, tc.unmarshalErr
					}
					return &gatewayv1.DeviceData{Messages: tc.messages}, nil
				}),
				GetBatch: BatchGetter(func(ctx context.Context) (bqbatch.Batcher, error) {
					if tc.batchErr != nil {
						return nil, tc.batchErr
					}
					return fakeBatch, nil
				}),
				ProcessRMS: ProcessRMSFunc(func(_ *pubsubdata.HeaderDetails, raw []byte) (*helper.RmsStatusRecord, *helper.HeaderRecord, error) {
					if tc.processErr {
						return nil, nil, errors.New("proc fail")
					}
					return &helper.RmsStatusRecord{}, &helper.HeaderRecord{}, nil
				}),
				ToBQ: ToBQConverter(func(orgID, sgwID, tz, topic, pubsubID, deviceID string, ts time.Time, header schemas.HeaderRecord, rawMsg []byte, status *helper.RmsStatusRecord) schemas.RmsData {
					return schemas.RmsData{}
				}),
				MarshalDevice: MarshalDeviceDataFunc(func(msg proto.Message) ([]byte, error) {
					if tc.marshalErr != nil {
						return nil, tc.marshalErr
					}
					return []byte{1, 2, 3}, nil
				}),
				ParseAttributes: ParseAttributesFunc(func(attrs map[string]string) (pubsubdata.CommonAttributes, pubsubdata.HeaderDetails, error) {
					if tc.attrErr != nil {
						return pubsubdata.CommonAttributes{}, pubsubdata.HeaderDetails{}, tc.attrErr
					}
					return pubsubdata.CommonAttributes{}, pubsubdata.HeaderDetails{}, nil
				}),
			}

			msg := &pubsub.Message{Data: []byte{1, 2, 3}, ID: "id", PublishTime: time.Now()}

			topic := client.Topic("topic")
			topic.Publish(ctx, msg)
			client.ReceiveError = tc.receiveErr
			sub, _ := client.CreateSubscription(ctx, "sub", connect.SubscriptionConfig{Topic: topic})

			// Invoke handler
			h := HandlerWithDeps(deps)
			h(ctx, sub)

			// Verify counts
			if dlqCount != tc.wantDLQ {
				t.Errorf("DLQ calls = %d; want %d", dlqCount, tc.wantDLQ)
			}
			if addCount != tc.wantAdds {
				t.Errorf("Batch adds = %d; want %d", addCount, tc.wantAdds)
			}
		})
	}
}
