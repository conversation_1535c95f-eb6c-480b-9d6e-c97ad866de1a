package faultLogs

import (
	"context"
	"database/sql"
	"errors"
	"testing"
	"time"

	gatewayv1 "bitbucket.org/synapse-its/protobuf-schemas/protomessages/gateway/v1"
	"cloud.google.com/go/pubsub"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"synapse-its.com/shared/bqbatch"
	"synapse-its.com/shared/connect"
	edihelper "synapse-its.com/shared/devices/edi/helper"
	mocks "synapse-its.com/shared/mocks"
	"synapse-its.com/shared/pubsubdata"
	"synapse-its.com/shared/schemas"
)

// Mock BQ converter functions
func mockLogMonitorResetBQConverter(httpHeader *pubsubdata.HeaderDetails, commonAttrs *pubsubdata.CommonAttributes, pubsubMessage *pubsub.Message, translater edihelper.LogMonitorResetTranslater, loguuid string, deviceID string) edihelper.BQConverter {
	return func(byteMsg []byte) (any, error) {
		return &edihelper.LogMonitorResetRecords{}, nil
	}
}

func mockLogMonitorResetBQConverterWithError(httpHeader *pubsubdata.HeaderDetails, commonAttrs *pubsubdata.CommonAttributes, pubsubMessage *pubsub.Message, translater edihelper.LogMonitorResetTranslater, loguuid string, deviceID string) edihelper.BQConverter {
	return func(byteMsg []byte) (any, error) {
		return nil, errors.New("converter error")
	}
}

func mockLogPreviousFailBQConverter(httpHeader *pubsubdata.HeaderDetails, commonAttrs *pubsubdata.CommonAttributes, pubsubMessage *pubsub.Message, translater edihelper.LogPreviousFailTranslater, loguuid string, deviceID string) edihelper.BQConverter {
	return func(byteMsg []byte) (any, error) {
		return &edihelper.LogPreviousFailRecords{}, nil
	}
}

func mockLogACLineEventBQConverter(httpHeader *pubsubdata.HeaderDetails, commonAttrs *pubsubdata.CommonAttributes, pubsubMessage *pubsub.Message, translater edihelper.LogACLineEventTranslater, loguuid string, deviceID string) edihelper.BQConverter {
	return func(byteMsg []byte) (any, error) {
		return &edihelper.LogACLineEventRecords{}, nil
	}
}

func mockLogFaultSignalSequenceBQConverter(httpHeader *pubsubdata.HeaderDetails, commonAttrs *pubsubdata.CommonAttributes, pubsubMessage *pubsub.Message, translater edihelper.FaultSignalSequenceTranslater, existingSeq *schemas.LogFaultSignalSequence, loguuid string, deviceID string) edihelper.BQConverter {
	return func(byteMsg []byte) (any, error) {
		return &edihelper.FaultSignalSequenceRecords{}, nil
	}
}

func mockLogConfigurationBQConverter(httpHeader *pubsubdata.HeaderDetails, commonAttrs *pubsubdata.CommonAttributes, pubsubMessage *pubsub.Message, translater edihelper.ConfigurationChangeLogTranslater, loguuid string, deviceID string) edihelper.BQConverter {
	return func(byteMsg []byte) (any, error) {
		return &edihelper.ConfigurationChangeLogRecords{}, nil
	}
}

func TestHandlerWithDeps(t *testing.T) {
	tests := []struct {
		name         string
		connectorErr error
		receiveErr   error
		attrErr      error
		unmarshalErr error
		logs         []*gatewayv1.LogEntry
		batchErr     error
		batchAddErr  error
		sendDlqErr   error
		upsertErr    error
		wantDLQ      int
		wantAdds     int
	}{
		{name: "Connector error", connectorErr: errors.New("no conn"), wantDLQ: 0, wantAdds: 0},
		{name: "Receive error", receiveErr: errors.New("recv fail"), wantDLQ: 0, wantAdds: 0},
		{name: "Attribute unmarshal error", attrErr: errors.New("fail attr parse"), wantDLQ: 1, wantAdds: 0},
		{name: "Unmarshal error", unmarshalErr: errors.New("bad proto"), wantDLQ: 1, wantAdds: 0},
		{name: "GetBatch error", logs: []*gatewayv1.LogEntry{{LogType: "MonitorReset", Message: [][]byte{[]byte("x")}}}, batchErr: errors.New("no batch"), wantDLQ: 0, wantAdds: 0},
		{name: "Batch Add error", logs: []*gatewayv1.LogEntry{{LogType: "MonitorReset", Message: [][]byte{[]byte("y")}}}, batchAddErr: errors.New("add fail"), wantDLQ: 2, wantAdds: 0},
		{
			name: "Happy path - Multiple log types",
			logs: []*gatewayv1.LogEntry{
				{LogType: "MonitorReset", Message: [][]byte{[]byte("reset")}},
				{LogType: "PreviousFail", Message: [][]byte{[]byte("fail")}},
				{LogType: "ACLine", Message: [][]byte{[]byte("ac")}},
				{LogType: "FaultSignalSequence", Message: [][]byte{[]byte("fault")}},
				{LogType: "Configuration", Message: [][]byte{[]byte("config")}},
				{LogType: "Not Supported", Message: [][]byte{[]byte("kaboom")}},
			},
			wantDLQ:  0,
			wantAdds: 6,
		},
		{
			name: "ExistingSeq appended for FaultSignalSequence",
			logs: []*gatewayv1.LogEntry{
				{LogType: "FaultSignalSequence", Message: [][]byte{[]byte("fault1")}},
			},
			wantDLQ:  0,
			wantAdds: 2,
		},
		{
			name:       "ParseAttributes error DLQ failure",
			attrErr:    errors.New("fail attr parse"),
			sendDlqErr: errors.New("dlq send fail"),
			wantDLQ:    0,
			wantAdds:   0,
		},
		{
			name:         "Unmarshal error DLQ failure",
			unmarshalErr: errors.New("bad proto"),
			sendDlqErr:   errors.New("dlq send fail"),
			wantDLQ:      0,
			wantAdds:     0,
		},
		{
			name:        "Initial batch.Add error DLQ failure",
			logs:        []*gatewayv1.LogEntry{{LogType: "MonitorReset", Message: [][]byte{[]byte("y")}}},
			batchAddErr: errors.New("add fail"),
			sendDlqErr:  errors.New("dlq send fail"),
			wantDLQ:     0,
			wantAdds:    0,
		},
		{
			name:       "ExistingSeq empty OrganizationIdentifier DLQ failure",
			logs:       []*gatewayv1.LogEntry{{LogType: "FaultSignalSequence", Message: [][]byte{[]byte("fault")}}},
			sendDlqErr: errors.New("dlq send fail"),
			wantDLQ:    0,
			wantAdds:   0,
		},
		{
			name:        "Batch Add error DLQ failure",
			logs:        []*gatewayv1.LogEntry{{LogType: "MonitorReset", Message: [][]byte{[]byte("y")}}},
			batchAddErr: errors.New("add fail"),
			sendDlqErr:  errors.New("dlq send fail"),
			wantDLQ:     0,
			wantAdds:    0,
		},
		{
			name:       "Error item DLQ failure",
			logs:       []*gatewayv1.LogEntry{{LogType: "MonitorReset", Message: [][]byte{[]byte("error")}}},
			sendDlqErr: errors.New("dlq send fail"),
			wantDLQ:    0,
			wantAdds:   2,
		},
		{
			name:     "Converter error in createIndividualLogBQItems",
			logs:     []*gatewayv1.LogEntry{{LogType: "MonitorReset", Message: [][]byte{[]byte("converter_error")}}},
			wantDLQ:  1,
			wantAdds: 1,
		},
		{
			name:       "Converter error with DLQ failure triggers msg.Nack",
			logs:       []*gatewayv1.LogEntry{{LogType: "MonitorReset", Message: [][]byte{[]byte("converter_error")}}},
			sendDlqErr: errors.New("dlq send fail"),
			wantDLQ:    0,
			wantAdds:   0,
		},
		{
			name: "Empty log messages are skipped",
			logs: []*gatewayv1.LogEntry{
				{LogType: "MonitorReset", Message: [][]byte{[]byte("valid"), {}, []byte("also-valid")}},
			},
			wantDLQ:  0,
			wantAdds: 3, // 2 valid messages + 1 faultLogs struct
		},
		// New test cases for upsertDeviceLog functionality
		{
			name:     "UpsertDeviceLog success",
			logs:     []*gatewayv1.LogEntry{{LogType: "MonitorReset", Message: [][]byte{[]byte("success")}}},
			wantDLQ:  0,
			wantAdds: 2, // 1 individual log + 1 faultLogs struct
		},
		{
			name:      "UpsertDeviceLog error",
			logs:      []*gatewayv1.LogEntry{{LogType: "MonitorReset", Message: [][]byte{[]byte("upsert_fail")}}},
			upsertErr: errors.New("database connection failed"),
			wantDLQ:   1,
			wantAdds:  2,
		},
		{
			name:       "UpsertDeviceLog error with DLQ failure triggers msg.Nack",
			logs:       []*gatewayv1.LogEntry{{LogType: "MonitorReset", Message: [][]byte{[]byte("upsert_fail")}}},
			upsertErr:  errors.New("database connection failed"),
			sendDlqErr: errors.New("dlq send fail"),
			wantDLQ:    0,
			wantAdds:   0,
		},
		{
			name:      "UpsertDeviceLog error with converter errors",
			logs:      []*gatewayv1.LogEntry{{LogType: "MonitorReset", Message: [][]byte{[]byte("converter_error")}}},
			upsertErr: errors.New("database connection failed"),
			wantDLQ:   2, // 1 for converter error + 1 for upsert error
			wantAdds:  1,
		},
		{
			name:       "UpsertDeviceLog error with converter errors and DLQ failure",
			logs:       []*gatewayv1.LogEntry{{LogType: "MonitorReset", Message: [][]byte{[]byte("converter_error")}}},
			upsertErr:  errors.New("database connection failed"),
			sendDlqErr: errors.New("dlq send fail"),
			wantDLQ:    0,
			wantAdds:   0,
		},
	}

	for _, tc := range tests {
		tc := tc
		t.Run(tc.name, func(t *testing.T) {
			t.Parallel()
			ctx := context.Background()
			dlqCount := 0
			addCount := 0

			// Setup fake connections and batcher
			conn := mocks.FakeConns()
			client := conn.Pubsub.(*mocks.FakePubsubClient)
			client.ReceiveError = tc.receiveErr

			fakeBatch, _ := mocks.FakeBatch(ctx)
			fakeBatch.(*mocks.FakeBatcher).AddFn = func(row interface{}) error {
				if tc.batchAddErr != nil {
					return tc.batchAddErr
				}
				addCount++
				return nil
			}
			logUUID := uuid.New().String()

			// Build HandlerDeps
			deps := HandlerDeps{
				Connector: ConnectorFunc(func(ctx context.Context, _ ...bool) (*connect.Connections, error) { return conn, tc.connectorErr }),
				SendToDLQ: DLQSender(func(ctx context.Context, c connect.PsClient, msg *pubsub.Message, reason string) error {
					if tc.sendDlqErr != nil {
						return tc.sendDlqErr
					}
					dlqCount++
					return nil
				}),
				UnmarshalLogs: UnmarshalDeviceLogsFunc(func(raw []byte) (*gatewayv1.DeviceLogs, error) {
					if tc.unmarshalErr != nil {
						return nil, tc.unmarshalErr
					}
					return &gatewayv1.DeviceLogs{DeviceId: "test-device", Logs: tc.logs}, nil
				}),
				GetBatch: BatchGetter(func(ctx context.Context) (bqbatch.Batcher, error) {
					if tc.batchErr != nil {
						return nil, tc.batchErr
					}
					return fakeBatch, nil
				}),
				ParseAttributes: ParseAttributesFunc(func(attrs map[string]string) (pubsubdata.CommonAttributes, pubsubdata.HeaderDetails, error) {
					if tc.attrErr != nil {
						return pubsubdata.CommonAttributes{}, pubsubdata.HeaderDetails{}, tc.attrErr
					}
					return pubsubdata.CommonAttributes{OrganizationIdentifier: "test-org", Topic: "test-topic"}, pubsubdata.HeaderDetails{GatewayDeviceID: "test-gateway", GatewayTimezone: "UTC"}, nil
				}),
				LogMonitorResetBQConverterConstructor: func(
					httpHeader *pubsubdata.HeaderDetails,
					commonAttrs *pubsubdata.CommonAttributes,
					pubsubMessage *pubsub.Message,
					translater edihelper.LogMonitorResetTranslater,
					uuid string,
					deviceID string,
				) edihelper.BQConverter {
					// Use error converter for specific test cases
					if tc.name == "Converter error in createIndividualLogBQItems" || tc.name == "Converter error with DLQ failure triggers msg.Nack" || tc.name == "UpsertDeviceLog error with converter errors" || tc.name == "UpsertDeviceLog error with converter errors and DLQ failure" {
						return mockLogMonitorResetBQConverterWithError(httpHeader, commonAttrs, pubsubMessage, nil, logUUID, deviceID)
					}
					return mockLogMonitorResetBQConverter(httpHeader, commonAttrs, pubsubMessage, nil, logUUID, deviceID)
				},
				LogPreviousFailBQConverterConstructor: func(
					httpHeader *pubsubdata.HeaderDetails,
					commonAttrs *pubsubdata.CommonAttributes,
					pubsubMessage *pubsub.Message,
					translater edihelper.LogPreviousFailTranslater,
					uuid string,
					deviceID string,
				) edihelper.BQConverter {
					return mockLogPreviousFailBQConverter(httpHeader, commonAttrs, pubsubMessage, nil, logUUID, deviceID)
				},
				LogACLineEventBQConverterConstructor: func(
					httpHeader *pubsubdata.HeaderDetails,
					commonAttrs *pubsubdata.CommonAttributes,
					pubsubMessage *pubsub.Message,
					translater edihelper.LogACLineEventTranslater,
					uuid string,
					deviceID string,
				) edihelper.BQConverter {
					return mockLogACLineEventBQConverter(httpHeader, commonAttrs, pubsubMessage, nil, logUUID, deviceID)
				},
				LogFaultSignalSequenceBQConverterConstructor: func(
					httpHeader *pubsubdata.HeaderDetails,
					commonAttrs *pubsubdata.CommonAttributes,
					pubsubMessage *pubsub.Message,
					translater edihelper.FaultSignalSequenceTranslater,
					existing *schemas.LogFaultSignalSequence,
					uuid string,
					deviceID string,
				) edihelper.BQConverter {
					if existing != nil && tc.name != "ExistingSeq empty OrganizationIdentifier DLQ failure" {
						existing.OrganizationIdentifier = "test-org"
					}
					return mockLogFaultSignalSequenceBQConverter(httpHeader, commonAttrs, pubsubMessage, nil, existing, logUUID, deviceID)
				},
				LogConfigurationBQConverterConstructor: func(
					httpHeader *pubsubdata.HeaderDetails,
					commonAttrs *pubsubdata.CommonAttributes,
					pubsubMessage *pubsub.Message,
					translater edihelper.ConfigurationChangeLogTranslater,
					uuid string,
					deviceID string,
				) edihelper.BQConverter {
					return mockLogConfigurationBQConverter(httpHeader, commonAttrs, pubsubMessage, nil, logUUID, deviceID)
				},
				UpsertDeviceLog: UpsertDeviceLogFunc(func(pg connect.DatabaseExecutor, deviceID string, logId string) error {
					return tc.upsertErr
				}),
			}

			// Publish test message if no connector or receive error
			if tc.connectorErr == nil && tc.receiveErr == nil {
				msg := &pubsub.Message{
					Data:        []byte{1, 2, 3},
					ID:          "id",
					PublishTime: time.Now(),
					Attributes: map[string]string{
						"organizationIdentifier": "test-org",
						"topic":                  "test-topic",
					},
				}

				topic := client.Topic("topic")
				topic.Publish(ctx, msg)
				client.CreateSubscription(ctx, "sub", connect.SubscriptionConfig{Topic: topic})
			}

			// Invoke handler
			h := HandlerWithDeps(deps)
			topic := client.Topic("topic")
			fakeSub, _ := client.CreateSubscription(ctx, "sub", connect.SubscriptionConfig{Topic: topic})
			h(ctx, fakeSub)

			// Verify counts using testify assertions
			assert.Equal(t, tc.wantDLQ, dlqCount, "DLQ calls count mismatch")
			assert.Equal(t, tc.wantAdds, addCount, "Batch adds count mismatch")
		})
	}
}

func TestUpsertDeviceLog(t *testing.T) {
	deviceID := "550e8400-e29b-41d4-a716-************"
	logID := "test-log-uuid-12345"

	cases := []struct {
		name       string
		enableFail bool
		failAfter  int
		wantErr    bool
	}{
		{"success", false, 0, false},
		{"db error", true, 0, true},
		{"retry success after initial failure", true, 1, false}, // First call fails, retry succeeds
	}

	for _, tc := range cases {
		t.Run(tc.name, func(t *testing.T) {
			t.Parallel()

			// Variables to capture the query and arguments
			var capturedQuery string
			var capturedArgs []interface{}

			// Setup fake database executor
			fdb := &mocks.FakeDBExecutor{}
			if tc.enableFail {
				fdb.EnableFailAfter = true
				fdb.ExecCallFailAfter = tc.failAfter
			} else {
				// Only set ExecFunc for success cases to capture query/args
				fdb.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					capturedQuery = query
					capturedArgs = args
					return nil, nil
				}
			}

			// Execute function under test
			err := upsertDeviceLog(fdb, deviceID, logID)

			// Assert results
			if tc.wantErr {
				assert.Error(t, err, "expected error but got nil")
			} else {
				assert.NoError(t, err, "unexpected error")
				// Should execute query at least once (more if retries occurred)
				assert.GreaterOrEqual(t, fdb.ExecCallCount, 1, "should execute query at least once")
			}

			// Verify the query was called with correct parameters (only for success cases)
			if capturedQuery != "" {
				// Check that Exec was called with the expected query and parameters
				assert.Contains(t, capturedQuery, "INSERT INTO {{DeviceLog}}", "should use correct table name")
				assert.Contains(t, capturedQuery, "ON CONFLICT (DeviceId) DO UPDATE", "should have upsert logic")
				assert.Equal(t, deviceID, capturedArgs[0], "first parameter should be deviceID")
				assert.Equal(t, logID, capturedArgs[2], "third parameter should be logID")
				// Second parameter is time.Now(), so we can't easily test the exact value
				assert.NotNil(t, capturedArgs[1], "second parameter should be timestamp")
			}
		})
	}
}
