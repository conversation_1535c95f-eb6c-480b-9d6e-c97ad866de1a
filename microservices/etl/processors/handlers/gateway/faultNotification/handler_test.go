package faultNotification

import (
	"context"
	"errors"
	"testing"
	"time"

	gatewayv1 "bitbucket.org/synapse-its/protobuf-schemas/protomessages/gateway/v1"
	"cloud.google.com/go/pubsub"
	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/proto"

	"synapse-its.com/shared/bqbatch"
	connect "synapse-its.com/shared/connect"
	edihelper "synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/mocks"
	"synapse-its.com/shared/pubsubdata"
)

var originalOsGetenv = osGetenv

// --- defaultUpsertDeviceFault table tests ---

func TestDefaultUpsertDeviceFault(t *testing.T) {
	t.<PERSON>llel()

	now := time.Now().UTC()
	rec := &edihelper.RmsStatusRecord{
		IsFaulted:           true,
		Fault:               "No Error",
		FaultStatus:         "OK",
		ChannelGreenStatus:  []bool{true, false},
		ChannelYellowStatus: []bool{false, true},
		ChannelRedStatus:    []bool{false, false},
		MonitorTime:         now,
		Temperature:         42,
		VoltagesGreen:       []int64{1, 1},
		VoltagesYellow:      []int64{2, 2},
		VoltagesRed:         []int64{3, 3},
		DeviceModel:         "EDIMMU16LE",
	}

	cases := []struct {
		name       string
		enableFail bool
		failAfter  int
		wantErr    bool
	}{
		{"success", false, 0, false},
		{"db error", true, 0, true},
	}

	for _, tc := range cases {
		t.Run(tc.name, func(t *testing.T) {
			t.Parallel()

			// Setup fake database executor
			fdb := &mocks.FakeDBExecutor{}
			if tc.enableFail {
				fdb.EnableFailAfter = true
				fdb.ExecCallFailAfter = tc.failAfter
			}

			// Execute function under test
			err := upsertDeviceFault(fdb, "550e8400-e29b-41d4-a716-446655440000", rec)

			// Assert results
			if tc.wantErr {
				assert.Error(t, err, "expected error but got nil")
				assert.Error(t, err, "should return wrapped upsert error")
			} else {
				assert.NoError(t, err, "unexpected error")
				assert.Equal(t, 1, fdb.ExecCallCount, "should execute query exactly once")
			}
		})
	}
}

// --- HandlerWithDeps table tests ---

func TestHandlerWithDeps(t *testing.T) {
	t.Parallel()

	ctx := context.Background()
	now := time.Now()

	cases := []struct {
		name         string
		connErr      error
		recvErr      error
		attrErr      error
		unmarshalErr error
		batchErr     error
		processErr   bool
		batchAddErr  error
		upsertErr    bool
		marshalErr   error
		dlqErr       error

		wantDLQ       int
		wantExecCalls int
		wantBatchAdds int
	}{
		{
			name:    "connector error",
			connErr: errors.New("no conn"),
			wantDLQ: 0, wantExecCalls: 0, wantBatchAdds: 0,
		},
		{
			name:    "receive error",
			recvErr: errors.New("recv fail"),
			wantDLQ: 0, wantExecCalls: 0, wantBatchAdds: 0,
		},
		{
			name:    "attribute parse error",
			attrErr: errors.New("fail attr parse"),
			wantDLQ: 1, wantExecCalls: 0, wantBatchAdds: 0,
		},
		{
			name:         "unmarshal error",
			unmarshalErr: errors.New("bad proto"),
			wantDLQ:      1, wantExecCalls: 0, wantBatchAdds: 0,
		},
		{
			name:         "get batch error",
			unmarshalErr: nil,
			batchErr:     errors.New("no batch"),
			// on GetBatch error we Nack but do not DLQ
			wantDLQ: 0, wantExecCalls: 0, wantBatchAdds: 0,
		},
		{
			name:       "process error",
			processErr: true,
			wantDLQ:    1, wantExecCalls: 0, wantBatchAdds: 0,
		},
		{
			name:        "batch add error",
			batchAddErr: errors.New("add fail"),
			wantDLQ:     1, wantExecCalls: 1, wantBatchAdds: 0,
		},
		{
			name:      "upsert error",
			upsertErr: true,
			wantDLQ:   1, wantExecCalls: 1, wantBatchAdds: 0,
		},
		{
			name:        "marshal error",
			batchAddErr: errors.New("any"),
			marshalErr:  errors.New("marshal fail"),
			wantDLQ:     0, wantExecCalls: 1, wantBatchAdds: 0,
		},
		{
			name:    "JSON parse + DLQ error", // TODO: check for nack
			attrErr: errors.New("fail attr parse"),
			dlqErr:  errors.New("dlq failed"),
			wantDLQ: 1, wantExecCalls: 0, wantBatchAdds: 0,
		},
		{
			name:         "proto unmarshal + DLQ error", // TODO: check for nack
			unmarshalErr: errors.New("bad proto"),
			dlqErr:       errors.New("dlq failed"),
			wantDLQ:      1, wantExecCalls: 0, wantBatchAdds: 0,
		},
		{
			name:        "final DLQ error path", // TODO: check for ack
			batchAddErr: errors.New("any"),      // forces unprocessed path
			dlqErr:      errors.New("dlq failed"),
			wantDLQ:     1, wantExecCalls: 1, wantBatchAdds: 0,
		},
		{
			name:    "happy path",
			wantDLQ: 0, wantExecCalls: 1, wantBatchAdds: 1,
		},
	}

	for _, tc := range cases {
		tc := tc
		t.Run(tc.name, func(t *testing.T) {
			// --- setup fakes ---
			conns := mocks.FakeConns()
			psc := conns.Pubsub.(*mocks.FakePubsubClient)
			psc.ReceiveError = tc.recvErr

			// create subscription
			topic := psc.Topic("topic-" + tc.name)
			sub, err := psc.CreateSubscription(ctx, "sub-"+tc.name, connect.SubscriptionConfig{Topic: topic})
			if err != nil {
				t.Fatal(err)
			}

			// publish message if connector & receive ok
			if tc.connErr == nil && tc.recvErr == nil {
				msg := &pubsub.Message{ID: "m-" + tc.name, Data: []byte{1, 2, 3}, PublishTime: now}
				topic.Publish(ctx, msg)
			}

			// swap in FakeDBExecutor and configure for upsert error
			fdb := &mocks.FakeDBExecutor{}
			if tc.upsertErr {
				fdb.EnableFailAfter = true
				fdb.ExecCallFailAfter = 0
			}
			conns.Postgres = fdb

			// fake batcher
			added := 0
			fakeBatch, _ := mocks.FakeBatch(ctx)
			fakeBatch.(*mocks.FakeBatcher).AddFn = func(row interface{}) error {
				if tc.batchAddErr != nil {
					return tc.batchAddErr
				}
				added++
				return nil
			}

			// collect DLQ calls
			dlq := 0

			// --- build deps ---
			deps := HandlerDeps{
				Connector: func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
					return conns, tc.connErr
				},
				SendToDLQ: func(ctx context.Context, client connect.PsClient, m *pubsub.Message, reason string) error {
					dlq++
					return tc.dlqErr
				},
				UnmarshalDevice: func(_ []byte) (*gatewayv1.DeviceData, error) {
					if tc.unmarshalErr != nil {
						return nil, tc.unmarshalErr
					}
					// default single‐message
					return &gatewayv1.DeviceData{Messages: []*gatewayv1.DeviceEntry{
						{DeviceId: "dev1", Message: []byte("p")},
					}}, nil
				},
				GetBatch: func(ctx context.Context) (bqbatch.Batcher, error) {
					return fakeBatch, tc.batchErr
				},
				ProcessRMS: func(_ *pubsubdata.HeaderDetails, _ []byte) (*edihelper.RmsStatusRecord, *edihelper.HeaderRecord, error) {
					if tc.processErr {
						return nil, nil, errors.New("proc fail")
					}
					return &edihelper.RmsStatusRecord{
							ChannelGreenStatus:  []bool{true},
							ChannelYellowStatus: []bool{false},
							ChannelRedStatus:    []bool{true},
							MonitorTime:         time.Now().UTC(),
							Temperature:         0,
							VoltagesGreen:       []int64{},
							VoltagesYellow:      []int64{},
							VoltagesRed:         []int64{},
							DeviceModel:         "M",
						},
						&edihelper.HeaderRecord{}, nil
				},
				ToBQ:          edihelper.RmsStatusToFaultNotification,
				UpsertDevice:  upsertDeviceFault,
				MarshalDevice: func(msg proto.Message) ([]byte, error) { return []byte{1, 2, 3}, tc.marshalErr },
				ParseAttributes: func(attrs map[string]string) (pubsubdata.CommonAttributes, pubsubdata.HeaderDetails, error) {
					if tc.attrErr != nil {
						return pubsubdata.CommonAttributes{}, pubsubdata.HeaderDetails{}, tc.attrErr
					}
					return pubsubdata.CommonAttributes{}, pubsubdata.HeaderDetails{}, nil
				},
				// Add missing dependencies that were causing the panic
				CreateNotificationService: func(connections *connect.Connections) NotificationService {
					return &MockNotificationService{
						ProcessFaultNotificationFunc: func(ctx context.Context, faultData *FaultNotificationData) error {
							return nil
						},
					}
				},
				GetWebsiteAppsURL: func() string {
					return "https://test.synapse-its.app/apps/"
				},
			}

			// --- invoke handler ---
			h := HandlerWithDeps(deps)
			h(ctx, sub)

			// --- asserts ---
			assert.Equal(t, tc.wantDLQ, dlq, "DLQ calls should match expected")
			assert.Equal(t, tc.wantExecCalls, fdb.ExecCallCount, "ExecCallCount should match expected")
			assert.Equal(t, tc.wantBatchAdds, added, "batch.Add calls should match expected")
		})
	}
}

// Test_createNotificationService tests the createNotificationService function
func Test_createNotificationService(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name           string
		setupConnsFn   func() *connect.Connections
		expectedType   string
		shouldNotPanic bool
	}{
		{
			name: "success_with_valid_connections",
			setupConnsFn: func() *connect.Connections {
				conns := mocks.FakeConns()
				return conns
			},
			expectedType:   "*faultNotification.notificationService",
			shouldNotPanic: true,
		},
		{
			name: "success_with_nil_postgres",
			setupConnsFn: func() *connect.Connections {
				conns := mocks.FakeConns()
				conns.Postgres = nil
				return conns
			},
			expectedType:   "*faultNotification.notificationService",
			shouldNotPanic: true,
		},
		{
			name: "success_with_nil_pubsub",
			setupConnsFn: func() *connect.Connections {
				conns := mocks.FakeConns()
				conns.Pubsub = nil
				return conns
			},
			expectedType:   "*faultNotification.notificationService",
			shouldNotPanic: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup connections
			conns := tt.setupConnsFn()

			// Execute function under test
			var result NotificationService
			var testPanic bool

			func() {
				defer func() {
					if r := recover(); r != nil {
						testPanic = true
					}
				}()
				result = createNotificationService(conns)
			}()

			// Assert results
			if tt.shouldNotPanic {
				assert.False(t, testPanic, "function should not panic")
				assert.NotNil(t, result, "should return non-nil NotificationService")
				assert.Implements(t, (*NotificationService)(nil), result, "should implement NotificationService interface")
			} else {
				assert.True(t, testPanic, "function should panic")
			}
		})
	}
}

// Test_getWebsiteAppsURL tests the getWebsiteAppsURL function
func Test_getWebsiteAppsURL(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name        string
		envValue    string
		expectedURL string
		setupFn     func(string)
		restoreFn   func()
	}{
		{
			name:        "success_with_env_var_set_with_slash",
			envValue:    "https://example.com/apps/",
			expectedURL: "https://example.com/apps/",
			setupFn: func(value string) {
				osGetenv = func(key string) string {
					if key == "WEBSITE_APPS" {
						return value
					}
					return ""
				}
			},
			restoreFn: func() {
				osGetenv = originalOsGetenv
			},
		},
		{
			name:        "success_with_env_var_set_without_slash",
			envValue:    "https://example.com/apps",
			expectedURL: "https://example.com/apps/",
			setupFn: func(value string) {
				osGetenv = func(key string) string {
					if key == "WEBSITE_APPS" {
						return value
					}
					return ""
				}
			},
			restoreFn: func() {
				osGetenv = originalOsGetenv
			},
		},
		{
			name:        "success_with_empty_env_var",
			envValue:    "",
			expectedURL: "https://www.synapse-its.app/apps/",
			setupFn: func(value string) {
				osGetenv = func(key string) string {
					return ""
				}
			},
			restoreFn: func() {
				osGetenv = originalOsGetenv
			},
		},
		{
			name:        "success_with_custom_url",
			envValue:    "https://custom.domain.com/path",
			expectedURL: "https://custom.domain.com/path/",
			setupFn: func(value string) {
				osGetenv = func(key string) string {
					if key == "WEBSITE_APPS" {
						return value
					}
					return ""
				}
			},
			restoreFn: func() {
				osGetenv = originalOsGetenv
			},
		},
		{
			name:        "success_with_single_character_url",
			envValue:    "/",
			expectedURL: "/",
			setupFn: func(value string) {
				osGetenv = func(key string) string {
					if key == "WEBSITE_APPS" {
						return value
					}
					return ""
				}
			},
			restoreFn: func() {
				osGetenv = originalOsGetenv
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup environment mock
			tt.setupFn(tt.envValue)
			defer tt.restoreFn()

			// Execute function under test
			result := getWebsiteAppsURL()

			// Assert results
			assert.Equal(t, tt.expectedURL, result, "should return expected URL")
			assert.True(t, len(result) > 0, "URL should not be empty")
			assert.True(t, result[len(result)-1] == '/', "URL should end with slash")
		})
	}
}

// MockNotificationService for testing
type MockNotificationService struct {
	ProcessFaultNotificationFunc      func(ctx context.Context, faultData *FaultNotificationData) error
	ProcessFaultNotificationCallCount int
}

func (m *MockNotificationService) ProcessFaultNotification(ctx context.Context, faultData *FaultNotificationData) error {
	m.ProcessFaultNotificationCallCount++
	if m.ProcessFaultNotificationFunc != nil {
		return m.ProcessFaultNotificationFunc(ctx, faultData)
	}
	return nil
}

// Test_handlerWithDeps_missing_dependencies tests the missing dependencies that caused the panic
func Test_handlerWithDeps_missing_dependencies(t *testing.T) {
	t.Parallel()

	t.Run("fix_missing_dependencies_in_HandlerDeps", func(t *testing.T) {
		t.Parallel()

		// This test demonstrates the fix for the panic in the existing TestHandlerWithDeps
		ctx := context.Background()
		conns := mocks.FakeConns()

		// Create proper HandlerDeps with all required functions
		deps := HandlerDeps{
			Connector: func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
				return conns, nil
			},
			ParseAttributes: func(attrs map[string]string) (pubsubdata.CommonAttributes, pubsubdata.HeaderDetails, error) {
				return pubsubdata.CommonAttributes{}, pubsubdata.HeaderDetails{}, nil
			},
			SendToDLQ: func(ctx context.Context, client connect.PsClient, m *pubsub.Message, reason string) error {
				return nil
			},
			UnmarshalDevice: func(_ []byte) (*gatewayv1.DeviceData, error) {
				return &gatewayv1.DeviceData{Messages: []*gatewayv1.DeviceEntry{
					{DeviceId: "dev1", Message: []byte("test")},
				}}, nil
			},
			GetBatch: func(ctx context.Context) (bqbatch.Batcher, error) {
				fakeBatch, _ := mocks.FakeBatch(ctx)
				return fakeBatch, nil
			},
			ProcessRMS: func(_ *pubsubdata.HeaderDetails, _ []byte) (*edihelper.RmsStatusRecord, *edihelper.HeaderRecord, error) {
				return &edihelper.RmsStatusRecord{
					IsFaulted:           true, // Make it faulted to test notification path
					ChannelGreenStatus:  []bool{true},
					ChannelYellowStatus: []bool{false},
					ChannelRedStatus:    []bool{true},
					MonitorTime:         time.Now().UTC(),
					Temperature:         0,
					VoltagesGreen:       []int64{},
					VoltagesYellow:      []int64{},
					VoltagesRed:         []int64{},
					DeviceModel:         "TestModel",
					Fault:               "Test Fault",
				}, &edihelper.HeaderRecord{}, nil
			},
			ToBQ:         edihelper.RmsStatusToFaultNotification,
			UpsertDevice: upsertDeviceFault,
			MarshalDevice: func(msg proto.Message) ([]byte, error) {
				return []byte{1, 2, 3}, nil
			},
			// These were missing and causing the panic:
			CreateNotificationService: func(connections *connect.Connections) NotificationService {
				return &MockNotificationService{
					ProcessFaultNotificationFunc: func(ctx context.Context, faultData *FaultNotificationData) error {
						return nil
					},
				}
			},
			GetWebsiteAppsURL: func() string {
				return "https://test.example.com/apps/"
			},
		}

		// Create and setup subscription
		psc := conns.Pubsub.(*mocks.FakePubsubClient)
		topic := psc.Topic("test-topic")
		sub, err := psc.CreateSubscription(ctx, "test-sub", connect.SubscriptionConfig{Topic: topic})
		assert.NoError(t, err, "should create subscription without error")

		// Publish a test message
		msg := &pubsub.Message{
			ID:          "test-msg",
			Data:        []byte{1, 2, 3},
			PublishTime: time.Now(),
		}
		topic.Publish(ctx, msg)

		// Execute function under test
		handler := HandlerWithDeps(deps)

		// This should not panic now
		assert.NotPanics(t, func() {
			handler(ctx, sub)
		}, "handler should not panic with proper dependencies")
	})
}

// Test_handlerWithDeps_fault_notification_path tests the fault notification processing path
func Test_handlerWithDeps_fault_notification_path(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name                      string
		isFaulted                 bool
		notificationError         error
		expectedNotificationCalls int
		expectError               bool
	}{
		{
			name:                      "faulted_device_triggers_notification",
			isFaulted:                 true,
			notificationError:         nil,
			expectedNotificationCalls: 1,
			expectError:               false,
		},
		{
			name:                      "non_faulted_device_skips_notification",
			isFaulted:                 false,
			notificationError:         nil,
			expectedNotificationCalls: 0,
			expectError:               false,
		},
		{
			name:                      "faulted_device_notification_error",
			isFaulted:                 true,
			notificationError:         errors.New("notification service error"),
			expectedNotificationCalls: 1,
			expectError:               true,
		},
		{
			name:                      "faulted_device_database_error",
			isFaulted:                 true,
			notificationError:         errors.New("database connection failed"),
			expectedNotificationCalls: 1,
			expectError:               true,
		},
		{
			name:                      "faulted_device_network_error",
			isFaulted:                 true,
			notificationError:         errors.New("network timeout"),
			expectedNotificationCalls: 1,
			expectError:               true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := context.Background()
			conns := mocks.FakeConns()

			// Mock notification service to track calls and return configured error
			mockNotificationService := &MockNotificationService{
				ProcessFaultNotificationFunc: func(ctx context.Context, faultData *FaultNotificationData) error {
					return tt.notificationError
				},
			}

			deps := HandlerDeps{
				Connector: func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
					return conns, nil
				},
				ParseAttributes: func(attrs map[string]string) (pubsubdata.CommonAttributes, pubsubdata.HeaderDetails, error) {
					return pubsubdata.CommonAttributes{}, pubsubdata.HeaderDetails{}, nil
				},
				SendToDLQ: func(ctx context.Context, client connect.PsClient, m *pubsub.Message, reason string) error {
					return nil
				},
				UnmarshalDevice: func(_ []byte) (*gatewayv1.DeviceData, error) {
					return &gatewayv1.DeviceData{Messages: []*gatewayv1.DeviceEntry{
						{DeviceId: "test-device", Message: []byte("test")},
					}}, nil
				},
				GetBatch: func(ctx context.Context) (bqbatch.Batcher, error) {
					fakeBatch, _ := mocks.FakeBatch(ctx)
					return fakeBatch, nil
				},
				ProcessRMS: func(_ *pubsubdata.HeaderDetails, _ []byte) (*edihelper.RmsStatusRecord, *edihelper.HeaderRecord, error) {
					return &edihelper.RmsStatusRecord{
						IsFaulted:           tt.isFaulted,
						ChannelGreenStatus:  []bool{true},
						ChannelYellowStatus: []bool{false},
						ChannelRedStatus:    []bool{true},
						MonitorTime:         time.Now().UTC(),
						Temperature:         25,
						VoltagesGreen:       []int64{12},
						VoltagesYellow:      []int64{12},
						VoltagesRed:         []int64{12},
						DeviceModel:         "TestModel",
						Fault:               "Test Fault Reason",
					}, &edihelper.HeaderRecord{}, nil
				},
				ToBQ:         edihelper.RmsStatusToFaultNotification,
				UpsertDevice: upsertDeviceFault,
				MarshalDevice: func(msg proto.Message) ([]byte, error) {
					return []byte{1, 2, 3}, nil
				},
				CreateNotificationService: func(connections *connect.Connections) NotificationService {
					return mockNotificationService
				},
				GetWebsiteAppsURL: func() string {
					return "https://test.example.com/apps/"
				},
			}

			// Setup subscription and message
			psc := conns.Pubsub.(*mocks.FakePubsubClient)
			topic := psc.Topic("fault-test-topic")
			sub, err := psc.CreateSubscription(ctx, "fault-test-sub", connect.SubscriptionConfig{Topic: topic})
			assert.NoError(t, err, "should create subscription without error")

			msg := &pubsub.Message{
				ID:          "fault-test-msg",
				Data:        []byte{1, 2, 3},
				PublishTime: time.Now(),
			}
			topic.Publish(ctx, msg)

			// Execute handler
			handler := HandlerWithDeps(deps)
			handler(ctx, sub)

			// Give time for goroutines to complete
			time.Sleep(100 * time.Millisecond)

			// Assert notification calls
			assert.Equal(t, tt.expectedNotificationCalls, mockNotificationService.ProcessFaultNotificationCallCount, "should call ProcessFaultNotification expected number of times")

			// Additional assertions for error cases
			if tt.expectError {
				// Verify that the error was handled gracefully (no panic, handler continues)
				// The handler should log the error but not fail the entire processing
				assert.True(t, mockNotificationService.ProcessFaultNotificationCallCount > 0, "should attempt to process notification even when error is expected")
			}
		})
	}
}

// Test_handlerWithDeps_notification_error_handling tests specific error handling scenarios
func Test_handlerWithDeps_notification_error_handling(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name                string
		notificationError   error
		expectedErrorString string
		shouldContinue      bool
	}{
		{
			name:                "database_connection_error",
			notificationError:   errors.New("database connection failed"),
			expectedErrorString: "Failed to process fault notification for device",
			shouldContinue:      true,
		},
		{
			name:                "network_timeout_error",
			notificationError:   errors.New("network timeout"),
			expectedErrorString: "Failed to process fault notification for device",
			shouldContinue:      true,
		},
		{
			name:                "permission_error",
			notificationError:   errors.New("insufficient permissions"),
			expectedErrorString: "Failed to process fault notification for device",
			shouldContinue:      true,
		},
		{
			name:                "invalid_data_error",
			notificationError:   errors.New("invalid notification data"),
			expectedErrorString: "Failed to process fault notification for device",
			shouldContinue:      true,
		},
		{
			name:                "service_unavailable_error",
			notificationError:   errors.New("notification service unavailable"),
			expectedErrorString: "Failed to process fault notification for device",
			shouldContinue:      true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := context.Background()
			conns := mocks.FakeConns()

			// Track if the notification service was called
			notificationCalled := false
			mockNotificationService := &MockNotificationService{
				ProcessFaultNotificationFunc: func(ctx context.Context, faultData *FaultNotificationData) error {
					notificationCalled = true
					return tt.notificationError
				},
			}

			deps := HandlerDeps{
				Connector: func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
					return conns, nil
				},
				ParseAttributes: func(attrs map[string]string) (pubsubdata.CommonAttributes, pubsubdata.HeaderDetails, error) {
					return pubsubdata.CommonAttributes{}, pubsubdata.HeaderDetails{}, nil
				},
				SendToDLQ: func(ctx context.Context, client connect.PsClient, m *pubsub.Message, reason string) error {
					return nil
				},
				UnmarshalDevice: func(_ []byte) (*gatewayv1.DeviceData, error) {
					return &gatewayv1.DeviceData{Messages: []*gatewayv1.DeviceEntry{
						{DeviceId: "error-test-device", Message: []byte("test")},
					}}, nil
				},
				GetBatch: func(ctx context.Context) (bqbatch.Batcher, error) {
					fakeBatch, _ := mocks.FakeBatch(ctx)
					return fakeBatch, nil
				},
				ProcessRMS: func(_ *pubsubdata.HeaderDetails, _ []byte) (*edihelper.RmsStatusRecord, *edihelper.HeaderRecord, error) {
					return &edihelper.RmsStatusRecord{
						IsFaulted:           true, // Ensure notification is triggered
						ChannelGreenStatus:  []bool{false},
						ChannelYellowStatus: []bool{false},
						ChannelRedStatus:    []bool{true},
						MonitorTime:         time.Now().UTC(),
						Temperature:         30,
						VoltagesGreen:       []int64{0},
						VoltagesYellow:      []int64{0},
						VoltagesRed:         []int64{15},
						DeviceModel:         "ErrorTestModel",
						Fault:               "Error Test Fault",
					}, &edihelper.HeaderRecord{}, nil
				},
				ToBQ:         edihelper.RmsStatusToFaultNotification,
				UpsertDevice: upsertDeviceFault,
				MarshalDevice: func(msg proto.Message) ([]byte, error) {
					return []byte{1, 2, 3}, nil
				},
				CreateNotificationService: func(connections *connect.Connections) NotificationService {
					return mockNotificationService
				},
				GetWebsiteAppsURL: func() string {
					return "https://test.example.com/apps/"
				},
			}

			// Setup subscription and message
			psc := conns.Pubsub.(*mocks.FakePubsubClient)
			topic := psc.Topic("error-test-topic")
			sub, err := psc.CreateSubscription(ctx, "error-test-sub", connect.SubscriptionConfig{Topic: topic})
			assert.NoError(t, err, "should create subscription without error")

			msg := &pubsub.Message{
				ID:          "error-test-msg",
				Data:        []byte{1, 2, 3},
				PublishTime: time.Now(),
			}
			topic.Publish(ctx, msg)

			// Execute handler - should not panic even with notification errors
			handler := HandlerWithDeps(deps)
			assert.NotPanics(t, func() {
				handler(ctx, sub)
			}, "handler should not panic when notification service returns error")

			// Give time for goroutines to complete
			time.Sleep(100 * time.Millisecond)

			// Verify that notification service was called despite the error
			assert.True(t, notificationCalled, "notification service should be called even when it returns an error")
			assert.Equal(t, 1, mockNotificationService.ProcessFaultNotificationCallCount, "should call ProcessFaultNotification exactly once")

			// Verify that the handler continued processing (no DLQ, no panic)
			// The error should be logged but not cause the handler to fail
			assert.True(t, tt.shouldContinue, "handler should continue processing despite notification errors")
		})
	}
}

// TestTopicNameExistsInSharedSchemas validates that the topic name used in this handler
// exists in the shared schemas to ensure consistency across the system
func TestTopicNameExistsInSharedSchemas(t *testing.T) {
	t.Parallel()

	// Create a set of valid topics from the shared schemas
	validTopics := make(map[string]bool)
	for _, topic := range pubsubdata.Topics {
		validTopics[topic] = true
	}

	// Check if the topic name used in this handler exists in the shared schemas
	if !validTopics[topicName] {
		t.Errorf("Topic name '%s' used in faultNotification handler not found in shared Topics list", topicName)
	}

	// Additional validation: ensure the topic name is also referenced in subscriptions
	// This checks if there are any subscriptions that map to this topic
	topicHasSubscriptions := false
	for _, subscriptionTopic := range pubsubdata.PubsubSubscriptions {
		if subscriptionTopic == topicName {
			topicHasSubscriptions = true
			break
		}
	}

	if !topicHasSubscriptions {
		t.Errorf("Topic name '%s' used in faultNotification handler has no subscriptions mapped to it in shared schemas", topicName)
	}

	// Log the topic name for debugging
	t.Logf("Validated topic name '%s' exists in shared schemas", topicName)
}
