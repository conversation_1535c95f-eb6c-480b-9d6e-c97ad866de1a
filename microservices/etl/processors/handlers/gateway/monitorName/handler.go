package monitorName

import (
	"context"
	"fmt"
	"sort"
	"strings"
	"time"

	gatewayv1 "bitbucket.org/synapse-its/protobuf-schemas/protomessages/gateway/v1"
	"cloud.google.com/go/pubsub"
	"google.golang.org/protobuf/proto"
	"synapse-its.com/etl/processors/handlers/etlShared"
	"synapse-its.com/shared/bqbatch"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/devices"
	edihelper "synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/logger"
	"synapse-its.com/shared/pubsubdata"
	"synapse-its.com/shared/schemas"
)

// --- Dependency abstractions for injection and testing ---
type (
	ConnectorFunc           func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error)
	ParseAttributesFunc     func(attrs map[string]string) (pubsubdata.CommonAttributes, pubsubdata.HeaderDetails, error)
	DLQSender               func(ctx context.Context, client connect.PsClient, msg *pubsub.Message, reason string) error
	UnmarshalDeviceDataFunc func(raw []byte) (*gatewayv1.DeviceData, error)
	BatchGetter             func(ctx context.Context) (bqbatch.Batcher, error)
	ProcessMonitorFunc      func(httpHeader *pubsubdata.HeaderDetails, byteMsg []byte) (monitorDetail *edihelper.MonitorNameAndId, headerDetails *edihelper.HeaderRecord, err error)
	ToBQConverter           func(orgID string, sgwID string, tz string, topic string, pubsubID string, deviceID string, pubsubTS time.Time, header schemas.HeaderRecord, rawMsg []byte, monitorData *edihelper.MonitorNameAndId) schemas.MonitorName
	MarshalDeviceDataFunc   func(msg proto.Message) ([]byte, error)
	UpsertFunc              func(pg connect.DatabaseExecutor, rec map[string]*edihelper.DeviceMonitorName) error
)

// HandlerDeps bundles all external dependencies.
type HandlerDeps struct {
	Connector       ConnectorFunc
	ParseAttributes ParseAttributesFunc
	SendToDLQ       DLQSender
	UnmarshalDevice UnmarshalDeviceDataFunc
	GetBatch        BatchGetter
	ProcessMonitor  ProcessMonitorFunc
	ToBQ            ToBQConverter
	MarshalDevice   MarshalDeviceDataFunc
	UpsertDevice    UpsertFunc
}

// HandlerWithDeps constructs a Pub/Sub processing function with injected dependencies.
func HandlerWithDeps(deps HandlerDeps) func(ctx context.Context, sub connect.PsSubscription) {
	return func(ctx context.Context, sub connect.PsSubscription) {
		batch, batchErr := deps.GetBatch(ctx)
		if batchErr != nil {
			logger.Errorf("Error getting batch: %v", batchErr)
			return
		}
		// Get the Connections from the context
		connections, err := deps.Connector(ctx)
		pg := connections.Postgres
		if err != nil {
			logger.Errorf("Error getting connections%v", err)
			return
		}

		// Subscribe to messages
		err = sub.Receive(ctx, func(ctx context.Context, msg *pubsub.Message) {
			logger.Debugf("Received message on %s MessageID: %s. Message Data:%s", sub.ID(), string(msg.ID), string(msg.Data))

			// Parse Attributes
			commonAttrs, httpHeader, errPa := deps.ParseAttributes(msg.Attributes)
			if errPa != nil {
				logger.Errorf("Unable to parse attributes: %v", msg.Attributes)
				// Try to send the message to the Dead Letter Queue
				err = deps.SendToDLQ(ctx, connections.Pubsub, msg, fmt.Sprintf("Unable to parse attributes: %v", errPa))
				// Check if sending to DLQ failed
				// If we successfully sent to DLQ, we continue processing
				if err != nil {
					// Log the DLQ error
					logger.Errorf("Error sending message to the DLQ topic %v", err)
					// Negative acknowledge so PubSub will retry delivery
					msg.Nack()
					return
				}
				msg.Ack()
				return
			}

			// Unmarshal the device data
			dd, umErr := deps.UnmarshalDevice(msg.Data)
			if umErr != nil {
				logger.Errorf("Error Unmarshaling the Device Data: %v", umErr)
				err = deps.SendToDLQ(ctx, connections.Pubsub, msg, fmt.Sprintf("Error Unmarshaling the Device Data: %v", umErr))
				if err != nil {
					logger.Errorf("Error sending message to the DLQ topic %v", err)
					msg.Nack()
					return
				}
				msg.Ack()
				return
			}

			// Collect failed messages
			unprocessed := new(gatewayv1.DeviceData)
			validRecords := make(map[string]*edihelper.DeviceMonitorName)
			// Loop through each record
			for _, d := range dd.GetMessages() {

				// Process the monitorName message
				monitorData, monitorHeader, perr := deps.ProcessMonitor(&httpHeader, d.GetMessage())
				if perr != nil {
					logger.Infof("Error parsing record: %v", perr)
					unprocessed.Messages = append(unprocessed.Messages, d)
					continue
				}

				// Add to the bigquery insert batch
				item := deps.ToBQ(
					commonAttrs.OrganizationIdentifier,
					httpHeader.GatewayDeviceID,
					httpHeader.GatewayTimezone,
					commonAttrs.Topic,
					msg.ID,
					d.DeviceId,
					msg.PublishTime.UTC(),
					*monitorHeader.ToBigQuerySchema(),
					d.GetMessage(),
					monitorData,
				)
				if err := batch.Add(item); err != nil {
					logger.Infof("Error adding to batch: %v", err)
					unprocessed.Messages = append(unprocessed.Messages, d)
				}

				validRecords[d.DeviceId] = &edihelper.DeviceMonitorName{
					DeviceID:        d.DeviceId,
					MonitorID:       monitorData.MonitorId,
					MonitorName:     monitorData.MonitorName,
					PubsubTimestamp: msg.PublishTime.UTC(),
					UpdatedAt:       time.Now(),
				}
			}

			if len(validRecords) > 0 {
				if err := deps.UpsertDevice(pg, validRecords); err != nil {
					logger.Errorf("Error adding message to postgres: %v", err)
				}
			}

			// DLQ for any unprocessed
			if len(unprocessed.Messages) > 0 {
				logger.Warnf("Unable to process %d device messages", len(unprocessed.Messages))
				msg.Data, err = deps.MarshalDevice(unprocessed)
				if err != nil {
					logger.Errorf("Unable to marshal unprocessed messages: %v", err)
					msg.Ack()
					return
				}

				err = deps.SendToDLQ(ctx, connections.Pubsub, msg, fmt.Sprintf("unable to process (%v) device messages", len(unprocessed.Messages)))
				if err != nil {
					logger.Errorf("Error sending message to the DLQ topic %v", err)
					msg.Ack()
					return
				}
			}

			msg.Ack()
		})
		if err != nil {
			logger.Errorf("Failed to receive messages from subscription %s: %v", sub.ID(), err)
		}
	}
}

func upsertDeviceMonitorName(pg connect.DatabaseExecutor, rec map[string]*edihelper.DeviceMonitorName) error {
	if len(rec) == 0 {
		return nil
	}
	const baseQuery = `
			INSERT INTO {{DeviceMonitorName}} (
					DeviceId,
					MonitorId,
					MonitorName,
					PubsubTimestamp,
					UpdatedAt
				)
				VALUES %s
				ON CONFLICT (DeviceId) DO UPDATE SET
					MonitorId        		= EXCLUDED.MonitorId,
					MonitorName      		= EXCLUDED.MonitorName,
					PubsubTimestamp     = EXCLUDED.PubsubTimestamp,
					UpdatedAt           = CURRENT_TIMESTAMP
				WHERE EXCLUDED.PubsubTimestamp > {{DeviceMonitorName}}.PubsubTimestamp;
		`
	// Sort keys to ensure consistent row locking order
	keys := make([]string, 0, len(rec))
	for k := range rec {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	valueStrings := make([]string, 0, len(keys))
	valueArgs := make([]interface{}, 0, len(keys)*4)
	i := 1

	for _, k := range keys {
		r := rec[k]
		valueStrings = append(valueStrings,
			fmt.Sprintf("($%d, $%d, $%d, $%d, $%d)", i, i+1, i+2, i+3, i+4))
		valueArgs = append(valueArgs,
			r.DeviceID,
			r.MonitorID,
			r.MonitorName,
			r.PubsubTimestamp,
			r.UpdatedAt,
		)
		i += 5
	}
	query := fmt.Sprintf(baseQuery, strings.Join(valueStrings, ", "))

	// Retry exec in case of deadlock
	return connect.WithDeadlockRetry(func() error {
		_, err := pg.Exec(query, valueArgs...)
		return err
	})
}

// Handler is the production-ready Pub/Sub processor using real dependencies.
var Handler = HandlerWithDeps(HandlerDeps{
	Connector:       connect.GetConnections,
	ParseAttributes: pubsubdata.ParseAttributes,
	SendToDLQ:       etlShared.SendToDLQ,
	UnmarshalDevice: etlShared.UnmarshalDeviceData,
	GetBatch:        bqbatch.GetBatch,
	ProcessMonitor:  devices.ProcessMonitorIDandName,
	ToBQ:            edihelper.MonitorNameAndIdToMonitorName,
	MarshalDevice:   etlShared.ProtoMarshal,
	UpsertDevice:    upsertDeviceMonitorName,
})
