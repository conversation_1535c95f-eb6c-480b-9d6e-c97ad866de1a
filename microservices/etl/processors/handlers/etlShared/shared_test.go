package etlShared

import (
	"context"
	"encoding/json"
	"errors"
	"reflect"
	"testing"
	"time"

	"cloud.google.com/go/pubsub"
	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/proto"

	gatewayv1 "bitbucket.org/synapse-its/protobuf-schemas/protomessages/gateway/v1"
	"synapse-its.com/shared/mocks"
	"synapse-its.com/shared/pubsubdata"
	"synapse-its.com/shared/schemas"
)

func TestSendToDLQ(t *testing.T) {
	ctx := context.Background()

	tests := []struct {
		name            string
		msg             *pubsub.Message
		reason          string
		expectMarshal   bool
		forceMarshalErr bool
		forcePublishErr bool
		expectErr       bool
	}{
		{
			name: "Success with DeliveryAttempt and Attributes",
			msg: &pubsub.Message{
				ID:              "msg1",
				Data:            []byte("data-1"),
				Attributes:      map[string]string{"key": "value"},
				OrderingKey:     "order1",
				PublishTime:     time.Now(),
				DeliveryAttempt: intPtr(3),
			},
			reason:        "some reason",
			expectMarshal: true,
			expectErr:     false,
		},
		{
			name: "Success without DeliveryAttempt or Attributes",
			msg: &pubsub.Message{
				ID:          "msg2",
				Data:        []byte("data-2"),
				OrderingKey: "order2",
				PublishTime: time.Now(),
			},
			reason:        "another reason",
			expectMarshal: true,
			expectErr:     false,
		},
		{
			name: "JSON Marshal fails",
			msg: &pubsub.Message{
				ID:         "msg3",
				Data:       []byte("bad"),
				Attributes: map[string]string{},
			},
			forceMarshalErr: true,
			reason:          "fail-marshal",
			expectErr:       true,
		},
		{
			name: "Publish result Get fails",
			msg: &pubsub.Message{
				ID:         "msg4",
				Data:       []byte("data-4"),
				Attributes: map[string]string{},
			},
			reason:          "publish-fail",
			forcePublishErr: true,
			expectErr:       true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			client := mocks.NewFakePubsubClient()
			origMarshal := jsonMarshal
			defer func() { jsonMarshal = origMarshal }()

			if tt.forceMarshalErr {
				jsonMarshal = func(v any) ([]byte, error) {
					return nil, errors.New("forced marshal error")
				}
			}

			if tt.forcePublishErr {
				// Configure the fake client to return an error on Get()
				topic := client.Topic(DlqTopicName).(*mocks.FakePubsubTopic)
				topic.PublishError = errors.New("forced publish error")
			}

			err := SendToDLQ(ctx, client, tt.msg, tt.reason)

			if tt.expectErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)

				// Check if the message was published correctly
				topic := client.Topic(DlqTopicName).(*mocks.FakePubsubTopic)
				assert.Len(t, topic.GetMessages(), 1)

				msg := topic.GetMessages()[0]
				var wrapper pubsubdata.PubsubMessageWrapper
				assert.NoError(t, json.Unmarshal(msg.Data, &wrapper))
				assert.Equal(t, tt.msg.ID, wrapper.ID)
				assert.Equal(t, string(tt.msg.Data), string(wrapper.Data))
				assert.Equal(t, tt.reason, wrapper.Attributes["dlq_reason"])
			}
		})
	}
}

func intPtr(i int) *int {
	return &i
}

func TestUnmarshalDeviceData(t *testing.T) {
	tests := []struct {
		name      string
		input     []byte
		expectErr bool
		setupData func() []byte
	}{
		{
			name: "Valid protobuf data",
			setupData: func() []byte {
				deviceData := &gatewayv1.DeviceData{
					Messages: []*gatewayv1.DeviceEntry{
						{
							DeviceId: "test-device",
						},
					},
				}
				data, _ := proto.Marshal(deviceData)
				return data
			},
			expectErr: false,
		},
		{
			name:      "Invalid protobuf data",
			input:     []byte("invalid protobuf data"),
			expectErr: true,
		},
		{
			name:      "Empty data",
			input:     []byte{},
			expectErr: false, // Empty protobuf is valid
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var input []byte
			if tt.setupData != nil {
				input = tt.setupData()
			} else {
				input = tt.input
			}

			result, err := UnmarshalDeviceData(input)

			if tt.expectErr {
				assert.Error(t, err)
				assert.Nil(t, result)
				assert.ErrorIs(t, err, ErrProtoUnmarshal)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
			}
		})
	}
}

func TestUnmarshalDeviceLogs(t *testing.T) {
	tests := []struct {
		name      string
		input     []byte
		expectErr bool
		setupData func() []byte
	}{
		{
			name: "Valid protobuf data",
			setupData: func() []byte {
				deviceLogs := &gatewayv1.DeviceLogs{
					DeviceId: "test-device",
				}
				data, _ := proto.Marshal(deviceLogs)
				return data
			},
			expectErr: false,
		},
		{
			name:      "Invalid protobuf data",
			input:     []byte("invalid protobuf data"),
			expectErr: true,
		},
		{
			name:      "Empty data",
			input:     []byte{},
			expectErr: false, // Empty protobuf is valid
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var input []byte
			if tt.setupData != nil {
				input = tt.setupData()
			} else {
				input = tt.input
			}

			result, err := UnmarshalDeviceLogs(input)

			if tt.expectErr {
				assert.Error(t, err)
				assert.Nil(t, result)
				assert.ErrorIs(t, err, ErrProtoUnmarshal)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
			}
		})
	}
}

func TestUnmarshalGatewayLogs(t *testing.T) {
	tests := []struct {
		name      string
		input     []byte
		expectErr bool
		setupData func() []byte
	}{
		{
			name: "Valid protobuf data",
			setupData: func() []byte {
				gatewayLogs := &gatewayv1.GatewayLogs{
					Message: []byte("test-message"),
				}
				data, _ := proto.Marshal(gatewayLogs)
				return data
			},
			expectErr: false,
		},
		{
			name:      "Invalid protobuf data",
			input:     []byte("invalid protobuf data"),
			expectErr: true,
		},
		{
			name:      "Empty data",
			input:     []byte{},
			expectErr: false, // Empty protobuf is valid
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var input []byte
			if tt.setupData != nil {
				input = tt.setupData()
			} else {
				input = tt.input
			}

			result, err := UnmarshalGatewayLogs(input)

			if tt.expectErr {
				assert.Error(t, err)
				assert.Nil(t, result)
				assert.ErrorIs(t, err, ErrProtoUnmarshal)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
			}
		})
	}
}

func TestProtoMarshal(t *testing.T) {
	tests := []struct {
		name      string
		input     proto.Message
		expectErr bool
	}{
		{
			name: "Valid protobuf message",
			input: &gatewayv1.DeviceData{
				Messages: []*gatewayv1.DeviceEntry{
					{
						DeviceId: "test-device",
					},
				},
			},
			expectErr: false,
		},
		{
			name:      "Empty protobuf message",
			input:     &gatewayv1.DeviceData{},
			expectErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := ProtoMarshal(tt.input)

			if tt.expectErr {
				assert.Error(t, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
			}
		})
	}
}

func TestProtoMarshalError(t *testing.T) {
	// Test error case by temporarily replacing ProtoMarshal
	origProtoMarshal := ProtoMarshal
	defer func() { ProtoMarshal = origProtoMarshal }()

	ProtoMarshal = func(msg proto.Message) ([]byte, error) {
		return nil, errors.New("forced marshal error")
	}

	result, err := ProtoMarshal(&gatewayv1.DeviceData{})
	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "forced marshal error")
}

func TestEntriesToBytes(t *testing.T) {
	tests := []struct {
		name      string
		entries   []*gatewayv1.DeviceEntry
		expectErr bool
	}{
		{
			name: "Valid entries",
			entries: []*gatewayv1.DeviceEntry{
				{
					DeviceId: "device1",
				},
				{
					DeviceId: "device2",
				},
			},
			expectErr: false,
		},
		{
			name:      "Empty entries",
			entries:   []*gatewayv1.DeviceEntry{},
			expectErr: false,
		},
		{
			name:      "Nil entries",
			entries:   nil,
			expectErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := EntriesToBytes(tt.entries)

			if tt.expectErr {
				assert.Error(t, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)

				// Verify we can unmarshal it back
				var deviceData gatewayv1.DeviceData
				assert.NoError(t, proto.Unmarshal(result, &deviceData))
				assert.Len(t, deviceData.Messages, len(tt.entries))
			}
		})
	}
}

func TestEntriesToBytesError(t *testing.T) {
	// Test marshal error by temporarily replacing protoMarshal
	origProtoMarshal := protoMarshal
	defer func() { protoMarshal = origProtoMarshal }()

	protoMarshal = func(msg proto.Message) ([]byte, error) {
		return nil, errors.New("forced marshal error")
	}

	entries := []*gatewayv1.DeviceEntry{
		{
			DeviceId: "test-device",
		},
	}

	result, err := EntriesToBytes(entries)

	assert.Error(t, err)
	assert.Nil(t, result)
	assert.ErrorIs(t, err, ErrMarshalDeviceData)
	assert.Contains(t, err.Error(), "forced marshal error")
}

func TestAttributesMapToBq(t *testing.T) {
	tests := []struct {
		name     string
		input    map[string]string
		expected []schemas.KV
	}{
		{
			name:     "empty map",
			input:    map[string]string{},
			expected: []schemas.KV{},
		},
		{
			name: "single key",
			input: map[string]string{
				"a": "1",
			},
			expected: []schemas.KV{
				{Key: "a", Value: "1"},
			},
		},
		{
			name: "multiple keys",
			input: map[string]string{
				"a": "1",
				"b": "2",
				"c": "3",
			},
			expected: []schemas.KV{
				{Key: "a", Value: "1"},
				{Key: "b", Value: "2"},
				{Key: "c", Value: "3"},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := AttributesMapToBq(tt.input)
			// Convert result to a map to ignore order for comparison
			m := make(map[string]string)
			for _, kv := range result {
				m[kv.Key] = kv.Value
			}
			if !reflect.DeepEqual(tt.input, m) {
				t.Errorf("Expected %v, got %v", tt.input, m)
			}
		})
	}
}

func TestBqToAttributesMap(t *testing.T) {
	tests := []struct {
		name     string
		input    []schemas.KV
		expected map[string]string
	}{
		{
			name:     "empty slice",
			input:    []schemas.KV{},
			expected: map[string]string{},
		},
		{
			name: "single kv",
			input: []schemas.KV{
				{Key: "a", Value: "1"},
			},
			expected: map[string]string{
				"a": "1",
			},
		},
		{
			name: "multiple kvs",
			input: []schemas.KV{
				{Key: "a", Value: "1"},
				{Key: "b", Value: "2"},
				{Key: "c", Value: "3"},
			},
			expected: map[string]string{
				"a": "1",
				"b": "2",
				"c": "3",
			},
		},
		{
			name: "duplicate keys, last one wins",
			input: []schemas.KV{
				{Key: "a", Value: "1"},
				{Key: "a", Value: "2"},
			},
			expected: map[string]string{
				"a": "2",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := BqToAttributesMap(tt.input)
			if !reflect.DeepEqual(result, tt.expected) {
				t.Errorf("Expected %v, got %v", tt.expected, result)
			}
		})
	}
}

func TestRoundTrip(t *testing.T) {
	original := map[string]string{
		"foo": "bar",
		"baz": "qux",
	}
	bq := AttributesMapToBq(original)
	result := BqToAttributesMap(bq)
	if !reflect.DeepEqual(original, result) {
		t.Errorf("Round-trip failed. Expected %v, got %v", original, result)
	}
}
