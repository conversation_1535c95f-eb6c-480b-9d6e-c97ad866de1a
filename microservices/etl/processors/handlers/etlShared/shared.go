package etlShared

import (
	"context"
	"encoding/json"
	"fmt"

	"cloud.google.com/go/pubsub"
	"google.golang.org/protobuf/proto"

	gatewayv1 "bitbucket.org/synapse-its/protobuf-schemas/protomessages/gateway/v1"
	"synapse-its.com/shared/connect"
	logger "synapse-its.com/shared/logger"
	"synapse-its.com/shared/pubsubdata"
	"synapse-its.com/shared/schemas"
)

var (
	jsonMarshal  = json.Marshal
	protoMarshal = proto.Marshal
)

// This function sends a message to a DLQ(Dead Letter Queue) topic if it fails to process in the ETL.
var SendToDLQ = func(ctx context.Context, client connect.PsClient, msg *pubsub.Message, reason string) error {
	// Deref DeliveryAttempt
	DAttempt := -1
	if msg.DeliveryAttempt != nil {
		DAttempt = *msg.DeliveryAttempt
	}
	// Set reason
	if reason != "" {
		if msg.Attributes == nil {
			msg.Attributes = make(map[string]string)
		}
		msg.Attributes["dlq_reason"] = reason
	}
	// Create a wrapper from msg.
	wrapper := pubsubdata.PubsubMessageWrapper{
		ID:              msg.ID,
		Data:            msg.Data,
		Attributes:      msg.Attributes,
		OrderingKey:     msg.OrderingKey,
		PublishTime:     msg.PublishTime,
		DeliveryAttempt: int64(DAttempt),
	}

	// Marshal the wrapper to JSON.
	jsonData, err := jsonMarshal(wrapper)
	if err != nil {
		logger.Errorf("Failed to marshal pubsub message wrapper to JSON: %v", err)
		return err
	}

	topic := client.Topic(DlqTopicName)
	result := topic.Publish(ctx, &pubsub.Message{
		Data: jsonData,
	})
	// Wait for the publish result.
	id, err := result.Get(ctx)
	if err != nil {
		logger.Errorf("Failed to publish message to DLQ: %v", err)
		return err
	}
	logger.Debugf("Published DLQ message ID: %v", id)
	return nil
}

// UnmarshalDeviceData decodes & unmarshals into a DeviceData.
var UnmarshalDeviceData = func(raw []byte) (*gatewayv1.DeviceData, error) {
	msg := &gatewayv1.DeviceData{}
	if err := proto.Unmarshal(raw, msg); err != nil {
		return nil, fmt.Errorf("%w: %v", ErrProtoUnmarshal, err)
	}
	return msg, nil
}

// UnmarshalDeviceLogs decodes & unmarshals into a DeviceLogs.
func UnmarshalDeviceLogs(raw []byte) (*gatewayv1.DeviceLogs, error) {
	msg := &gatewayv1.DeviceLogs{}
	if err := proto.Unmarshal(raw, msg); err != nil {
		return nil, fmt.Errorf("%w: %v", ErrProtoUnmarshal, err)
	}
	return msg, nil
}

// UnmarshalGatewayLogs decodes & unmarshals into a GatewayLogs.
func UnmarshalGatewayLogs(raw []byte) (*gatewayv1.GatewayLogs, error) {
	msg := &gatewayv1.GatewayLogs{}
	if err := proto.Unmarshal(raw, msg); err != nil {
		return nil, fmt.Errorf("%w: %v", ErrProtoUnmarshal, err)
	}
	return msg, nil
}

// ProtoMarshal marshals any protobufmessage
var ProtoMarshal = func(msg proto.Message) ([]byte, error) {
	return proto.Marshal(msg)
}

// EntriesToBytes marshals your slice of DeviceEntry into a DeviceData wrapper
// and returns the raw protobuf bytes.
func EntriesToBytes(entries []*gatewayv1.DeviceEntry) ([]byte, error) {
	wrapper := &gatewayv1.DeviceData{
		Messages: entries,
	}
	raw, err := protoMarshal(wrapper)
	if err != nil {
		return nil, fmt.Errorf("%w: %v", ErrMarshalDeviceData, err)
	}
	return raw, nil
}

func AttributesMapToBq(attr map[string]string) []schemas.KV {
	out := make([]schemas.KV, 0, len(attr))
	for k, v := range attr {
		out = append(out, schemas.KV{Key: k, Value: v})
	}
	return out
}

func BqToAttributesMap(kvs []schemas.KV) map[string]string {
	out := make(map[string]string, len(kvs))
	for _, kv := range kvs {
		out[kv.Key] = kv.Value
	}
	return out
}
