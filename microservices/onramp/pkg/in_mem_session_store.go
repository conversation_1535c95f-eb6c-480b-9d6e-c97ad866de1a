package pkg

import (
	"sync"

	"synapse-its.com/onramp/domain"
	"synapse-its.com/shared/logger"
)

// TODO: This is a simple shared storage for the in-memory session store
// Actually, the session management should belong to auth module
// But for now, we will use this simple shared storage for the in-memory session store
// TODO: Remove this once we have a proper session management

// Simple shared storage for the in-memory session store
var storage = make(map[string]*domain.Session)

// InMemorySessionStore implements SessionStore interface using in-memory storage
type inMemorySessionStore struct {
	mutex sync.RWMutex
}

// NewInMemorySessionStore creates a new instance of InMemorySessionStore
func NewInMemorySessionStore() domain.SessionStore {
	return &inMemorySessionStore{}
}

// GetSession retrieves a session by sessionID
func (s *inMemorySessionStore) GetSession(sessionID string) (*domain.Session, bool) {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	session, exists := storage[sessionID]
	logger.Debugf("[inMemorySessionStore] GetSession: %v, exists: %v, data: %+v", sessionID, exists, session)
	return session, exists
}

// SetSession stores a session with the given sessionID
func (s *inMemorySessionStore) SetSession(sessionID string, session *domain.Session) {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	logger.Debugf("[inMemorySessionStore] SetSession: %v, data: %+v", sessionID, session)
	storage[sessionID] = session
}

// ClearSession removes a session by sessionID
func (s *inMemorySessionStore) ClearSession(sessionID string) {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	delete(storage, sessionID)
}
