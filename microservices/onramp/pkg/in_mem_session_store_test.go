package pkg

import (
	"sync"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"golang.org/x/oauth2"
	"synapse-its.com/onramp/domain"
)

// TODO WIll need to be removed
// Helper function to clear the global storage before each test
func clearStorage() {
	storage = make(map[string]*domain.Session)
}

// TestNewInMemorySessionStore tests the constructor
func TestNewInMemorySessionStore(t *testing.T) {
	t.Run("NewInMemorySessionStore_Success", func(t *testing.T) {
		store := NewInMemorySessionStore()

		assert.NotNil(t, store)
		assert.Implements(t, (*domain.SessionStore)(nil), store)
	})

	t.Run("NewInMemorySessionStore_MultipleInstances", func(t *testing.T) {
		store1 := NewInMemorySessionStore()
		store2 := NewInMemorySessionStore()

		assert.NotNil(t, store1)
		assert.NotNil(t, store2)
		// Both instances should implement the same interface
		assert.Implements(t, (*domain.SessionStore)(nil), store1)
		assert.Implements(t, (*domain.SessionStore)(nil), store2)
	})
}

// TestInMemorySessionStore_SetSession tests session storage
func TestInMemorySessionStore_SetSession(t *testing.T) {
	t.Run("SetSession_Success", func(t *testing.T) {
		clearStorage()
		store := NewInMemorySessionStore()
		sessionID := "test-session-1"
		session := &domain.Session{
			UserID: "user123",
			OAuthToken: &oauth2.Token{
				AccessToken: "access-token",
			},
		}

		store.SetSession(sessionID, session)

		// Verify session was stored in global storage
		storedSession, exists := storage[sessionID]
		assert.True(t, exists)
		assert.Equal(t, session, storedSession)
	})

	t.Run("SetSession_OverwriteExisting", func(t *testing.T) {
		clearStorage()
		store := NewInMemorySessionStore()
		sessionID := "test-session-2"

		// Set initial session
		session1 := &domain.Session{UserID: "user1"}
		store.SetSession(sessionID, session1)

		// Overwrite with new session
		session2 := &domain.Session{UserID: "user2"}
		store.SetSession(sessionID, session2)

		// Verify the session was overwritten
		storedSession, exists := storage[sessionID]
		assert.True(t, exists)
		assert.Equal(t, session2, storedSession)
		assert.Equal(t, "user2", storedSession.UserID)
	})

	t.Run("SetSession_NilSession", func(t *testing.T) {
		clearStorage()
		store := NewInMemorySessionStore()
		sessionID := "test-session-3"

		store.SetSession(sessionID, nil)

		// Should still store nil session
		storedSession, exists := storage[sessionID]
		assert.True(t, exists)
		assert.Nil(t, storedSession)
	})

	t.Run("SetSession_EmptySessionID", func(t *testing.T) {
		clearStorage()
		store := NewInMemorySessionStore()
		session := &domain.Session{UserID: "user123"}

		store.SetSession("", session)

		// Should store session with empty key
		storedSession, exists := storage[""]
		assert.True(t, exists)
		assert.Equal(t, session, storedSession)
	})
}

// TestInMemorySessionStore_GetSession tests session retrieval
func TestInMemorySessionStore_GetSession(t *testing.T) {
	t.Run("GetSession_Found", func(t *testing.T) {
		clearStorage()
		store := NewInMemorySessionStore()
		sessionID := "test-session-4"
		expectedSession := &domain.Session{
			UserID: "user456",
			OAuthToken: &oauth2.Token{
				AccessToken:  "access-token",
				RefreshToken: "refresh-token",
			},
		}

		// Directly set in storage to test retrieval
		storage[sessionID] = expectedSession

		session, found := store.GetSession(sessionID)

		assert.True(t, found)
		assert.Equal(t, expectedSession, session)
	})

	t.Run("GetSession_NotFound", func(t *testing.T) {
		clearStorage()
		store := NewInMemorySessionStore()
		sessionID := "nonexistent-session"

		session, found := store.GetSession(sessionID)

		assert.False(t, found)
		assert.Nil(t, session)
	})

	t.Run("GetSession_EmptySessionID", func(t *testing.T) {
		clearStorage()
		store := NewInMemorySessionStore()

		session, found := store.GetSession("")

		assert.False(t, found)
		assert.Nil(t, session)
	})

	t.Run("GetSession_NilSessionInStorage", func(t *testing.T) {
		clearStorage()
		store := NewInMemorySessionStore()
		sessionID := "test-session-5"

		// Store nil session
		storage[sessionID] = nil

		session, found := store.GetSession(sessionID)

		assert.True(t, found)
		assert.Nil(t, session)
	})
}

// TestInMemorySessionStore_ClearSession tests session deletion
func TestInMemorySessionStore_ClearSession(t *testing.T) {
	t.Run("ClearSession_Success", func(t *testing.T) {
		clearStorage()
		store := NewInMemorySessionStore()
		sessionID := "test-session-6"
		session := &domain.Session{UserID: "user789"}

		// Set session first
		store.SetSession(sessionID, session)
		assert.Len(t, storage, 1)

		// Clear session
		store.ClearSession(sessionID)

		// Verify session was removed
		_, exists := storage[sessionID]
		assert.False(t, exists)
		assert.Len(t, storage, 0)
	})

	t.Run("ClearSession_NonexistentSession", func(t *testing.T) {
		clearStorage()
		store := NewInMemorySessionStore()
		sessionID := "nonexistent-session"

		// Should not panic when clearing nonexistent session
		assert.NotPanics(t, func() {
			store.ClearSession(sessionID)
		})

		assert.Len(t, storage, 0)
	})

	t.Run("ClearSession_EmptySessionID", func(t *testing.T) {
		clearStorage()
		store := NewInMemorySessionStore()

		// Should not panic when clearing empty session ID
		assert.NotPanics(t, func() {
			store.ClearSession("")
		})
	})
}

// TestInMemorySessionStore_Integration tests complete workflows
func TestInMemorySessionStore_Integration(t *testing.T) {
	t.Run("CompleteSessionLifecycle", func(t *testing.T) {
		clearStorage()
		store := NewInMemorySessionStore()
		sessionID := "lifecycle-test"
		session := &domain.Session{
			UserID: "user-lifecycle",
			OAuthToken: &oauth2.Token{
				AccessToken:  "access-token",
				RefreshToken: "refresh-token",
			},
		}

		// 1. Initially session should not exist
		_, found := store.GetSession(sessionID)
		assert.False(t, found)

		// 2. Set session
		store.SetSession(sessionID, session)

		// 3. Retrieve session
		retrievedSession, found := store.GetSession(sessionID)
		assert.True(t, found)
		assert.Equal(t, session, retrievedSession)

		// 4. Update session
		updatedSession := &domain.Session{
			UserID: "user-updated",
		}
		store.SetSession(sessionID, updatedSession)

		// 5. Verify update
		retrievedSession, found = store.GetSession(sessionID)
		assert.True(t, found)
		assert.Equal(t, updatedSession, retrievedSession)
		assert.Equal(t, "user-updated", retrievedSession.UserID)

		// 6. Clear session
		store.ClearSession(sessionID)

		// 7. Verify session is gone
		_, found = store.GetSession(sessionID)
		assert.False(t, found)
	})

	t.Run("MultipleSessionsManagement", func(t *testing.T) {
		clearStorage()
		store := NewInMemorySessionStore()

		// Create multiple sessions
		sessions := map[string]*domain.Session{
			"session-1": {UserID: "user1"},
			"session-2": {UserID: "user2"},
			"session-3": {UserID: "user3"},
		}

		// Set all sessions
		for id, session := range sessions {
			store.SetSession(id, session)
		}

		// Verify all sessions exist
		assert.Len(t, storage, 3)
		for id, expectedSession := range sessions {
			retrievedSession, found := store.GetSession(id)
			assert.True(t, found)
			assert.Equal(t, expectedSession, retrievedSession)
		}

		// Clear one session
		store.ClearSession("session-2")
		assert.Len(t, storage, 2)

		// Verify remaining sessions still exist
		_, found := store.GetSession("session-1")
		assert.True(t, found)
		_, found = store.GetSession("session-3")
		assert.True(t, found)
		_, found = store.GetSession("session-2")
		assert.False(t, found)
	})
}

// TestInMemorySessionStore_Concurrency tests thread safety
func TestInMemorySessionStore_Concurrency(t *testing.T) {
	t.Run("ConcurrentSetAndGet", func(t *testing.T) {
		clearStorage()
		store := NewInMemorySessionStore()
		numGoroutines := 100
		numOperations := 10

		var wg sync.WaitGroup
		wg.Add(numGoroutines * 2) // 2 operations per goroutine

		// Concurrent set operations
		for i := 0; i < numGoroutines; i++ {
			go func(idx int) {
				defer wg.Done()
				for j := 0; j < numOperations; j++ {
					sessionID := uuid.New().String()
					session := &domain.Session{
						UserID: uuid.New().String(),
					}
					store.SetSession(sessionID, session)
				}
			}(i)
		}

		// Concurrent get operations
		for i := 0; i < numGoroutines; i++ {
			go func(idx int) {
				defer wg.Done()
				for j := 0; j < numOperations; j++ {
					sessionID := uuid.New().String()
					store.GetSession(sessionID) // Most will return false, but shouldn't panic
				}
			}(i)
		}

		// Should complete without panicking
		assert.NotPanics(t, func() {
			wg.Wait()
		})
	})

	t.Run("ConcurrentSetGetClear", func(t *testing.T) {
		clearStorage()
		store := NewInMemorySessionStore()
		sessionID := "concurrent-test"
		numGoroutines := 50

		var wg sync.WaitGroup
		wg.Add(numGoroutines * 3) // 3 operations per goroutine

		// Create a session to work with
		initialSession := &domain.Session{UserID: "initial-user"}
		store.SetSession(sessionID, initialSession)

		// Concurrent operations on the same session
		for i := 0; i < numGoroutines; i++ {
			// Set operations
			go func(idx int) {
				defer wg.Done()
				session := &domain.Session{
					UserID: uuid.New().String(),
				}
				store.SetSession(sessionID, session)
			}(i)

			// Get operations
			go func(idx int) {
				defer wg.Done()
				store.GetSession(sessionID)
			}(i)

			// Clear operations
			go func(idx int) {
				defer wg.Done()
				store.ClearSession(sessionID)
			}(i)
		}

		// Should complete without panicking or deadlocking
		done := make(chan bool)
		go func() {
			wg.Wait()
			close(done)
		}()

		select {
		case <-done:
			// Success
		case <-time.After(5 * time.Second):
			t.Fatal("Test timed out - possible deadlock")
		}
	})
}

// TestInMemorySessionStore_EdgeCases tests edge cases and error conditions
func TestInMemorySessionStore_EdgeCases(t *testing.T) {
	t.Run("LargeSessionData", func(t *testing.T) {
		clearStorage()
		store := NewInMemorySessionStore()
		sessionID := "large-session"

		// Create a session with large data
		largeData := make([]byte, 1024*1024) // 1MB
		for i := range largeData {
			largeData[i] = byte(i % 256)
		}

		session := &domain.Session{
			UserID: string(largeData),
		}

		// Should handle large sessions without issues
		assert.NotPanics(t, func() {
			store.SetSession(sessionID, session)
		})

		retrievedSession, found := store.GetSession(sessionID)
		assert.True(t, found)
		assert.Equal(t, session.UserID, retrievedSession.UserID)
	})

	t.Run("SpecialCharacterSessionIDs", func(t *testing.T) {
		clearStorage()
		store := NewInMemorySessionStore()

		specialIDs := []string{
			"session-with-spaces ",
			"session/with/slashes",
			"session\\with\\backslashes",
			"session.with.dots",
			"session@with@symbols",
			"session-with-unicode-ñáéíóú",
			"session-with-numbers-123456",
		}

		session := &domain.Session{UserID: "test-user"}

		for _, sessionID := range specialIDs {
			// Should handle special characters in session IDs
			assert.NotPanics(t, func() {
				store.SetSession(sessionID, session)
			})

			retrievedSession, found := store.GetSession(sessionID)
			assert.True(t, found)
			assert.Equal(t, session, retrievedSession)

			store.ClearSession(sessionID)
		}
	})

	t.Run("GlobalStorageSharing", func(t *testing.T) {
		clearStorage()

		// Create multiple store instances
		store1 := NewInMemorySessionStore()
		store2 := NewInMemorySessionStore()

		sessionID := "shared-session"
		session := &domain.Session{UserID: "shared-user"}

		// Set session with store1
		store1.SetSession(sessionID, session)

		// Should be accessible from store2 (shared global storage)
		retrievedSession, found := store2.GetSession(sessionID)
		assert.True(t, found)
		assert.Equal(t, session, retrievedSession)

		// Clear with store2
		store2.ClearSession(sessionID)

		// Should be gone when accessed from store1
		_, found = store1.GetSession(sessionID)
		assert.False(t, found)
	})
}

// BenchmarkInMemorySessionStore_SetSession benchmarks session setting performance
func BenchmarkInMemorySessionStore_SetSession(b *testing.B) {
	clearStorage()
	store := NewInMemorySessionStore()
	session := &domain.Session{
		UserID: "benchmark-user",
		OAuthToken: &oauth2.Token{
			AccessToken: "access-token",
		},
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		sessionID := uuid.New().String()
		store.SetSession(sessionID, session)
	}
}

// BenchmarkInMemorySessionStore_GetSession benchmarks session retrieval performance
func BenchmarkInMemorySessionStore_GetSession(b *testing.B) {
	clearStorage()
	store := NewInMemorySessionStore()
	session := &domain.Session{UserID: "benchmark-user"}

	// Pre-populate with some sessions
	sessionIDs := make([]string, 1000)
	for i := 0; i < 1000; i++ {
		sessionID := uuid.New().String()
		sessionIDs[i] = sessionID
		store.SetSession(sessionID, session)
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		sessionID := sessionIDs[i%len(sessionIDs)]
		store.GetSession(sessionID)
	}
}

// BenchmarkInMemorySessionStore_ConcurrentAccess benchmarks concurrent operations
func BenchmarkInMemorySessionStore_ConcurrentAccess(b *testing.B) {
	clearStorage()
	store := NewInMemorySessionStore()
	session := &domain.Session{UserID: "concurrent-user"}

	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			sessionID := uuid.New().String()
			store.SetSession(sessionID, session)
			store.GetSession(sessionID)
			store.ClearSession(sessionID)
		}
	})
}
