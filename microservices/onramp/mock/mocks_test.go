package mock

import (
	"context"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"golang.org/x/oauth2"
	"synapse-its.com/onramp/data"
	"synapse-its.com/onramp/domain"
	"synapse-its.com/shared/api/authorizer"
)

// TestMockAuthService tests the MockAuthService implementation
func TestMockAuthService(t *testing.T) {
	t.Run("BasicAuth_Success", func(t *testing.T) {
		mockService := &MockAuthService{}
		ctx := context.Background()
		req := &data.BasicAuthRequest{
			Username: "testuser",
			Password: "testpass",
		}

		expectedResponse := &data.LoginResponse{
			User: &domain.User{
				ID:        uuid.New(),
				FirstName: "Test",
				LastName:  "User",
			},
			Token: "test-token",
		}

		mockService.On("BasicAuth", ctx, req).Return(expectedResponse, nil)

		result, err := mockService.BasicAuth(ctx, req)

		assert.NoError(t, err)
		assert.Equal(t, expectedResponse, result)
		mockService.AssertExpectations(t)
	})

	t.Run("BasicAuth_Error", func(t *testing.T) {
		mockService := &MockAuthService{}
		ctx := context.Background()
		req := &data.BasicAuthRequest{
			Username: "testuser",
			Password: "wrongpass",
		}

		mockService.On("BasicAuth", ctx, req).Return((*data.LoginResponse)(nil), assert.AnError)

		result, err := mockService.BasicAuth(ctx, req)

		assert.Error(t, err)
		assert.Nil(t, result)
		mockService.AssertExpectations(t)
	})

	t.Run("Register_Success", func(t *testing.T) {
		mockService := &MockAuthService{}
		ctx := context.Background()
		req := &data.RegisterRequest{
			FirstName: "Test",
			LastName:  "User",
			Username:  "testuser",
			Password:  "testpass",
			Email:     "<EMAIL>",
		}

		mockService.On("Register", ctx, req).Return(nil)

		err := mockService.Register(ctx, req)

		assert.NoError(t, err)
		mockService.AssertExpectations(t)
	})

	t.Run("Register_Error", func(t *testing.T) {
		mockService := &MockAuthService{}
		ctx := context.Background()
		req := &data.RegisterRequest{
			FirstName: "Test",
			LastName:  "User",
			Username:  "existinguser",
			Password:  "testpass",
			Email:     "<EMAIL>",
		}

		mockService.On("Register", ctx, req).Return(assert.AnError)

		err := mockService.Register(ctx, req)

		assert.Error(t, err)
		mockService.AssertExpectations(t)
	})

	t.Run("HandleOIDCLogin_Success", func(t *testing.T) {
		mockService := &MockAuthService{}
		ctx := context.Background()
		req := &data.OIDCLoginRequest{
			Subject: "test-subject",
			Issuer:  "https://accounts.google.com",
			Email:   "<EMAIL>",
			Name:    "Test User",
		}

		expectedResponse := &data.LoginResponse{
			User: &domain.User{
				ID:        uuid.New(),
				FirstName: "Test",
				LastName:  "User",
			},
			Token: "oidc-token",
		}

		mockService.On("HandleOIDCLogin", ctx, req).Return(expectedResponse, nil)

		result, err := mockService.HandleOIDCLogin(ctx, req)

		assert.NoError(t, err)
		assert.Equal(t, expectedResponse, result)
		mockService.AssertExpectations(t)
	})

	t.Run("ProcessOAuth2Callback_Success", func(t *testing.T) {
		mockService := &MockAuthService{}
		ctx := context.Background()
		req := &data.OAuth2CallbackRequest{
			Code:  "auth-code",
			State: "state-value",
		}

		expectedResponse := &data.OAuth2CallbackResponse{
			User: &domain.User{
				ID:        uuid.New(),
				FirstName: "Test",
				LastName:  "User",
			},
			Token:      "oauth-token",
			OAuthToken: &oauth2.Token{AccessToken: "test-token"},
		}

		mockService.On("ProcessOAuth2Callback", ctx, req).Return(expectedResponse, nil)

		result, err := mockService.ProcessOAuth2Callback(ctx, req)

		assert.NoError(t, err)
		assert.Equal(t, expectedResponse, result)
		mockService.AssertExpectations(t)
	})
}

// TestMockSessionStore tests the MockSessionStore implementation
func TestMockSessionStore(t *testing.T) {
	t.Run("GetSession_Found", func(t *testing.T) {
		mockStore := &MockSessionStore{}
		sessionID := "test-session-id"
		expectedSession := &domain.Session{
			UserID: "user123",
			OAuthToken: &oauth2.Token{
				AccessToken: "test-token",
			},
		}

		mockStore.On("GetSession", sessionID).Return(expectedSession, true)

		result, found := mockStore.GetSession(sessionID)

		assert.True(t, found)
		assert.Equal(t, expectedSession, result)
		mockStore.AssertExpectations(t)
	})

	t.Run("GetSession_NotFound", func(t *testing.T) {
		mockStore := &MockSessionStore{}
		sessionID := "nonexistent-session"

		mockStore.On("GetSession", sessionID).Return((*domain.Session)(nil), false)

		result, found := mockStore.GetSession(sessionID)

		assert.False(t, found)
		assert.Nil(t, result)
		mockStore.AssertExpectations(t)
	})

	t.Run("SetSession", func(t *testing.T) {
		mockStore := &MockSessionStore{}
		sessionID := "test-session-id"
		session := &domain.Session{
			UserID: "user123",
			OAuthToken: &oauth2.Token{
				AccessToken: "test-token",
			},
		}

		mockStore.On("SetSession", sessionID, session).Return()

		mockStore.SetSession(sessionID, session)

		mockStore.AssertExpectations(t)
	})

	t.Run("ClearSession", func(t *testing.T) {
		mockStore := &MockSessionStore{}
		sessionID := "test-session-id"

		mockStore.On("ClearSession", sessionID).Return()

		mockStore.ClearSession(sessionID)

		mockStore.AssertExpectations(t)
	})

	t.Run("Multiple_Operations", func(t *testing.T) {
		mockStore := &MockSessionStore{}
		sessionID := "test-session-id"
		session := &domain.Session{
			UserID: "user123",
		}

		// Set up expectations for multiple operations
		mockStore.On("SetSession", sessionID, session).Return()
		mockStore.On("GetSession", sessionID).Return(session, true)
		mockStore.On("ClearSession", sessionID).Return()

		// Execute operations
		mockStore.SetSession(sessionID, session)
		result, found := mockStore.GetSession(sessionID)
		mockStore.ClearSession(sessionID)

		assert.True(t, found)
		assert.Equal(t, session, result)
		mockStore.AssertExpectations(t)
	})
}

// TestMockPasswordHasher tests the MockPasswordHasher implementation
func TestMockPasswordHasher(t *testing.T) {
	t.Run("HashPassword", func(t *testing.T) {
		mockHasher := &MockPasswordHasher{}
		password := "testpassword"
		expectedHash := "$2a$10$hashedpassword"

		mockHasher.On("HashPassword", password).Return(expectedHash)

		result := mockHasher.HashPassword(password)

		assert.Equal(t, expectedHash, result)
		mockHasher.AssertExpectations(t)
	})

	t.Run("ComparePassword_Match", func(t *testing.T) {
		mockHasher := &MockPasswordHasher{}
		password := "testpassword"
		hash := "$2a$10$hashedpassword"

		mockHasher.On("ComparePassword", password, hash).Return(true)

		result := mockHasher.ComparePassword(password, hash)

		assert.True(t, result)
		mockHasher.AssertExpectations(t)
	})

	t.Run("ComparePassword_NoMatch", func(t *testing.T) {
		mockHasher := &MockPasswordHasher{}
		password := "wrongpassword"
		hash := "$2a$10$hashedpassword"

		mockHasher.On("ComparePassword", password, hash).Return(false)

		result := mockHasher.ComparePassword(password, hash)

		assert.False(t, result)
		mockHasher.AssertExpectations(t)
	})

	t.Run("HashPassword_EmptyString", func(t *testing.T) {
		mockHasher := &MockPasswordHasher{}
		password := ""
		expectedHash := ""

		mockHasher.On("HashPassword", password).Return(expectedHash)

		result := mockHasher.HashPassword(password)

		assert.Equal(t, expectedHash, result)
		mockHasher.AssertExpectations(t)
	})
}

// TestMockTokenGenerator tests the MockTokenGenerator implementation
func TestMockTokenGenerator(t *testing.T) {
	t.Run("GenerateToken_Success", func(t *testing.T) {
		mockGenerator := &MockTokenGenerator{}
		username := "testuser"
		expectedToken := "jwt.token.here"
		expectedExpiry := time.Now().Add(24 * time.Hour)

		mockGenerator.On("GenerateToken", username).Return(expectedToken, expectedExpiry, nil)

		token, expiry, err := mockGenerator.GenerateToken(username)

		assert.NoError(t, err)
		assert.Equal(t, expectedToken, token)
		assert.Equal(t, expectedExpiry, expiry)
		mockGenerator.AssertExpectations(t)
	})

	t.Run("GenerateToken_Error", func(t *testing.T) {
		mockGenerator := &MockTokenGenerator{}
		username := "invaliduser"

		mockGenerator.On("GenerateToken", username).Return("", time.Time{}, assert.AnError)

		token, expiry, err := mockGenerator.GenerateToken(username)

		assert.Error(t, err)
		assert.Empty(t, token)
		assert.Equal(t, time.Time{}, expiry)
		mockGenerator.AssertExpectations(t)
	})

	t.Run("GenerateToken_Multiple_Users", func(t *testing.T) {
		mockGenerator := &MockTokenGenerator{}

		user1 := "user1"
		user2 := "user2"
		token1 := "token1"
		token2 := "token2"
		expiry1 := time.Now().Add(1 * time.Hour)
		expiry2 := time.Now().Add(2 * time.Hour)

		mockGenerator.On("GenerateToken", user1).Return(token1, expiry1, nil)
		mockGenerator.On("GenerateToken", user2).Return(token2, expiry2, nil)

		// Generate tokens for both users
		result1Token, result1Expiry, err1 := mockGenerator.GenerateToken(user1)
		result2Token, result2Expiry, err2 := mockGenerator.GenerateToken(user2)

		assert.NoError(t, err1)
		assert.NoError(t, err2)
		assert.Equal(t, token1, result1Token)
		assert.Equal(t, token2, result2Token)
		assert.Equal(t, expiry1, result1Expiry)
		assert.Equal(t, expiry2, result2Expiry)
		mockGenerator.AssertExpectations(t)
	})
}

// TestMockAuthRepository tests the MockAuthRepository implementation
func TestMockAuthRepository(t *testing.T) {
	t.Run("GetByUsername_Found", func(t *testing.T) {
		mockRepo := &MockAuthRepository{}
		ctx := context.Background()
		username := "testuser"

		expectedUser := &domain.User{
			ID:        uuid.New(),
			FirstName: "Test",
			LastName:  "User",
		}
		expectedAuthMethod := &domain.AuthMethod{
			ID:       uuid.New(),
			UserID:   expectedUser.ID,
			Type:     domain.AuthMethodTypeUsernamePassword,
			UserName: username,
		}

		mockRepo.On("GetByUsername", ctx, username).Return(expectedUser, expectedAuthMethod, nil)

		user, authMethod, err := mockRepo.GetByUsername(ctx, username)

		assert.NoError(t, err)
		assert.Equal(t, expectedUser, user)
		assert.Equal(t, expectedAuthMethod, authMethod)
		mockRepo.AssertExpectations(t)
	})

	t.Run("GetByUsername_NotFound", func(t *testing.T) {
		mockRepo := &MockAuthRepository{}
		ctx := context.Background()
		username := "nonexistentuser"

		mockRepo.On("GetByUsername", ctx, username).Return((*domain.User)(nil), (*domain.AuthMethod)(nil), assert.AnError)

		user, authMethod, err := mockRepo.GetByUsername(ctx, username)

		assert.Error(t, err)
		assert.Nil(t, user)
		assert.Nil(t, authMethod)
		mockRepo.AssertExpectations(t)
	})

	t.Run("UpdateLastLogin", func(t *testing.T) {
		mockRepo := &MockAuthRepository{}
		ctx := context.Background()
		user := &domain.User{ID: uuid.New()}
		authMethod := &domain.AuthMethod{ID: uuid.New()}

		mockRepo.On("UpdateLastLogin", ctx, user, authMethod).Return(nil)

		err := mockRepo.UpdateLastLogin(ctx, user, authMethod)

		assert.NoError(t, err)
		mockRepo.AssertExpectations(t)
	})

	t.Run("CreateBasicAuthUser", func(t *testing.T) {
		mockRepo := &MockAuthRepository{}
		ctx := context.Background()
		user := &domain.User{
			ID:        uuid.New(),
			FirstName: "New",
			LastName:  "User",
		}
		authMethod := &domain.AuthMethod{
			ID:       uuid.New(),
			UserID:   user.ID,
			Type:     domain.AuthMethodTypeUsernamePassword,
			UserName: "newuser",
		}

		mockRepo.On("CreateBasicAuthUser", ctx, user, authMethod).Return(nil)

		err := mockRepo.CreateBasicAuthUser(ctx, user, authMethod)

		assert.NoError(t, err)
		mockRepo.AssertExpectations(t)
	})

	t.Run("GetByOIDCSubject_Found", func(t *testing.T) {
		mockRepo := &MockAuthRepository{}
		ctx := context.Background()
		issuer := "https://accounts.google.com"
		subject := "google-user-123"

		expectedUser := &domain.User{
			ID:        uuid.New(),
			FirstName: "OIDC",
			LastName:  "User",
		}
		expectedAuthMethod := &domain.AuthMethod{
			ID:     uuid.New(),
			UserID: expectedUser.ID,
			Type:   domain.AuthMethodTypeOIDC,
		}

		mockRepo.On("GetByOIDCSubject", ctx, issuer, subject).Return(expectedUser, expectedAuthMethod, nil)

		user, authMethod, err := mockRepo.GetByOIDCSubject(ctx, issuer, subject)

		assert.NoError(t, err)
		assert.Equal(t, expectedUser, user)
		assert.Equal(t, expectedAuthMethod, authMethod)
		mockRepo.AssertExpectations(t)
	})

	t.Run("CreateOIDCUser", func(t *testing.T) {
		mockRepo := &MockAuthRepository{}
		ctx := context.Background()
		user := &domain.User{
			ID:        uuid.New(),
			FirstName: "OIDC",
			LastName:  "User",
		}
		authMethod := &domain.AuthMethod{
			ID:     uuid.New(),
			UserID: user.ID,
			Type:   domain.AuthMethodTypeOIDC,
		}

		mockRepo.On("CreateOIDCUser", ctx, user, authMethod).Return(nil)

		err := mockRepo.CreateOIDCUser(ctx, user, authMethod)

		assert.NoError(t, err)
		mockRepo.AssertExpectations(t)
	})

	t.Run("GetUserPermissions_Found", func(t *testing.T) {
		mockRepo := &MockAuthRepository{}
		ctx := context.Background()
		userID := uuid.New()

		expectedPermissions := &domain.UserPermissions{
			UserID: userID.String(),
			Permissions: []authorizer.Permission{
				{
					Scope:          "organization",
					ScopeID:        uuid.New().String(),
					OrganizationID: uuid.New().String(),
					Permissions:    []string{"read", "write"},
				},
			},
		}

		mockRepo.On("GetUserPermissions", ctx, userID).Return(expectedPermissions)

		result := mockRepo.GetUserPermissions(ctx, userID)

		assert.Equal(t, expectedPermissions, result)
		mockRepo.AssertExpectations(t)
	})

	t.Run("GetUserPermissions_NotFound", func(t *testing.T) {
		mockRepo := &MockAuthRepository{}
		ctx := context.Background()
		userID := uuid.New()

		mockRepo.On("GetUserPermissions", ctx, userID).Return((*domain.UserPermissions)(nil))

		result := mockRepo.GetUserPermissions(ctx, userID)

		assert.Nil(t, result)
		mockRepo.AssertExpectations(t)
	})

	t.Run("GetUserPermissions_TypeAssertionFailure", func(t *testing.T) {
		mockRepo := &MockAuthRepository{}
		ctx := context.Background()
		userID := uuid.New()

		// Return a different type that will cause type assertion to fail
		// This will trigger the fallback "return nil" on line 51
		mockRepo.On("GetUserPermissions", ctx, userID).Return("invalid-type")

		result := mockRepo.GetUserPermissions(ctx, userID)

		assert.Nil(t, result)
		mockRepo.AssertExpectations(t)
	})
}

// TestMockIntegration tests that all mocks work together
func TestMockIntegration(t *testing.T) {
	t.Run("Complete_Auth_Flow_Simulation", func(t *testing.T) {
		// Set up all mocks
		mockRepo := &MockAuthRepository{}
		mockHasher := &MockPasswordHasher{}
		mockTokenGen := &MockTokenGenerator{}
		mockSessionStore := &MockSessionStore{}

		ctx := context.Background()
		username := "testuser"
		password := "testpass"
		hashedPassword := "$2a$10$hashedpass"
		token := "jwt.token.here"
		sessionID := "session-123"
		expiry := time.Now().Add(24 * time.Hour)

		user := &domain.User{
			ID:        uuid.New(),
			FirstName: "Test",
			LastName:  "User",
		}
		authMethod := &domain.AuthMethod{
			ID:       uuid.New(),
			UserID:   user.ID,
			Type:     domain.AuthMethodTypeUsernamePassword,
			UserName: username,
		}
		session := &domain.Session{
			UserID: user.ID.String(),
		}

		// Set up expectations for authentication flow
		mockRepo.On("GetByUsername", ctx, username).Return(user, authMethod, nil)
		mockHasher.On("ComparePassword", password, mock.AnythingOfType("string")).Return(true)
		mockTokenGen.On("GenerateToken", username).Return(token, expiry, nil)
		mockSessionStore.On("SetSession", sessionID, session).Return()
		mockRepo.On("UpdateLastLogin", ctx, user, authMethod).Return(nil)

		// Simulate authentication flow
		foundUser, foundAuthMethod, err := mockRepo.GetByUsername(ctx, username)
		assert.NoError(t, err)
		assert.Equal(t, user, foundUser)

		passwordMatch := mockHasher.ComparePassword(password, hashedPassword)
		assert.True(t, passwordMatch)

		generatedToken, tokenExpiry, err := mockTokenGen.GenerateToken(username)
		assert.NoError(t, err)
		assert.Equal(t, token, generatedToken)
		assert.Equal(t, expiry, tokenExpiry)

		mockSessionStore.SetSession(sessionID, session)

		err = mockRepo.UpdateLastLogin(ctx, foundUser, foundAuthMethod)
		assert.NoError(t, err)

		// Verify all expectations were met
		mockRepo.AssertExpectations(t)
		mockHasher.AssertExpectations(t)
		mockTokenGen.AssertExpectations(t)
		mockSessionStore.AssertExpectations(t)
	})
}

// TestMockErrorHandling tests error scenarios across all mocks
func TestMockErrorHandling(t *testing.T) {
	t.Run("Repository_Errors", func(t *testing.T) {
		mockRepo := &MockAuthRepository{}
		ctx := context.Background()

		// Test various error scenarios
		mockRepo.On("GetByUsername", ctx, "erroruser").Return((*domain.User)(nil), (*domain.AuthMethod)(nil), assert.AnError)
		mockRepo.On("CreateBasicAuthUser", ctx, mock.Anything, mock.Anything).Return(assert.AnError)
		mockRepo.On("UpdateLastLogin", ctx, mock.Anything, mock.Anything).Return(assert.AnError)

		_, _, err := mockRepo.GetByUsername(ctx, "erroruser")
		assert.Error(t, err)

		err = mockRepo.CreateBasicAuthUser(ctx, &domain.User{}, &domain.AuthMethod{})
		assert.Error(t, err)

		err = mockRepo.UpdateLastLogin(ctx, &domain.User{}, &domain.AuthMethod{})
		assert.Error(t, err)

		mockRepo.AssertExpectations(t)
	})

	t.Run("TokenGenerator_Errors", func(t *testing.T) {
		mockGen := &MockTokenGenerator{}

		mockGen.On("GenerateToken", "erroruser").Return("", time.Time{}, assert.AnError)

		token, expiry, err := mockGen.GenerateToken("erroruser")
		assert.Error(t, err)
		assert.Empty(t, token)
		assert.Equal(t, time.Time{}, expiry)

		mockGen.AssertExpectations(t)
	})
}
