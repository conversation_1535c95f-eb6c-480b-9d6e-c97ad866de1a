// src/app/app-routing.module.ts
import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ProtectedComponent } from './protected/protected.component';
import { AuthGuard } from './guards/auth.guard';
import { HomeComponent } from './home/<USER>';
import { OrganizationsComponent } from './pages/organizations/organizations.component';
import { FooListComponent } from './foo-list/foo-list.component';
import { SoftwareGatewayComponent } from './pages/software-gateway/software-gateway.component';
import { SoftwareGatewayConfigurationComponent } from './pages/software-gateway-configuration/software-gateway-configuration.component';
import { DevicesComponent } from './pages/devices/devices.component';

const routes: Routes = [
  { path: '', component: HomeComponent, data: { title: 'Home' } },
  { path: 'protected', component: ProtectedComponent, canActivate: [AuthGuard] },
  { path: 'organizations', component: OrganizationsComponent, data: { title: 'Organizations' } },
  { path: 'software-gateway', component: SoftwareGatewayComponent, data: { title: 'Software Gateway' } },
  { path: 'software-gateway-config', component: SoftwareGatewayConfigurationComponent, data: { title: 'Software Gateway Configuration' } },
  { path: 'devices', component: DevicesComponent, data: { title: 'Devices' } },
  { path: 'foo', component: FooListComponent },
  { path: '**', redirectTo: '' }
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule]
})
export class AppRoutingModule { }
