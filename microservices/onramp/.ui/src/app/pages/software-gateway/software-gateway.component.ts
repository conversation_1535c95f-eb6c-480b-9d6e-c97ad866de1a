import { Component, OnInit } from '@angular/core';
import { GatewayService } from '../../services/gateway.service';
import { ActivatedRoute, Router } from '@angular/router';
import { NzMessageService } from 'ng-zorro-antd/message';
import { SoftwareGateway } from '../../models/software-gateway.model';
import { NzTableSortFn, NzTableSortOrder } from 'ng-zorro-antd/table';

@Component({
  selector: 'app-software-gateway',
  standalone: false,
  templateUrl: './software-gateway.component.html',
  styleUrls: ['./software-gateway.component.css']
})
export class SoftwareGatewayComponent implements OnInit {
  listOfData: SoftwareGateway[] = [];
  filteredList: SoftwareGateway[] = [];
  searchTerm: string = '';
  isVisible = false;
  isEditMode = false;
  editIndex: number | null = null;
  currentGateway: SoftwareGateway | null = null;
  isTableLoading = false;
  currentSortKey: keyof SoftwareGateway | null = null;
  sortOrder: NzTableSortOrder | null = null;
  organizationFilter: string | null = null;

  constructor(
    private gatewayService: GatewayService,
    private router: Router,
    private route: ActivatedRoute,
  ) { }

  ngOnInit() {
    this.loadGatewayList();
    this.route.queryParams.subscribe(params => {
      if (params) {
        this.searchTerm = params['organizationIdentifier'];
        this.organizationFilter = params['organizationIdentifier'] || null;
      }
    });
  }
  getTagColor(status: string): string {
    return status === '1' ? 'green' : 'red';
  }
  getTagText(status: string): string {
    return status === '1' ? 'Enabled' : 'Disabled';
  }
  loadGatewayList() {
    this.isTableLoading = true;
    this.gatewayService.getGateway().subscribe(
      (data) => {
        this.listOfData = data;
        this.filteredList = [...this.listOfData];
        this.isTableLoading = false;
        this.filterGateways();
      },
      (error) => {
        console.error('Error fetching Gateway list:', error);
        this.isTableLoading = false;
      }
    );
  }

  filterGateways(): void {
    this.filteredList = [...this.listOfData];
    if (this.organizationFilter) {
      this.filteredList = this.filteredList.filter(gateway =>
        gateway.orgId === this.organizationFilter,
      );
    }
    if (this.searchTerm) {
      this.filteredList = this.filteredList.filter(gateway =>
        gateway.name.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        gateway.gatewayId.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        gateway.orgId.toLowerCase().includes(this.searchTerm.toLowerCase())
      );
    }
    if (this.sortOrder && this.currentSortKey) {
      this.sortData();
    }
  }

  sortData(): void {
    if (!this.currentSortKey || !this.sortOrder) return;
    if (!this.currentSortKey) return;
    this.filteredList.sort((a, b) => {
      const valueA = a[this.currentSortKey as keyof SoftwareGateway];
      const valueB = b[this.currentSortKey as keyof SoftwareGateway];
      if (typeof valueA === 'string' && typeof valueB === 'string') {
        return this.sortOrder === 'ascend'
          ? valueA.localeCompare(valueB)
          : valueB.localeCompare(valueA);
      } else if (typeof valueA === 'number' && typeof valueB === 'number') {
        return this.sortOrder === 'ascend' ? valueA - valueB : valueB - valueA;
      }
      return 0;
    });
  }

  onColumnClick(key: keyof SoftwareGateway): void {
    this.currentSortKey = key;
    if (this.sortOrder === null) {
      this.sortOrder = 'ascend';
    } else {
      this.sortOrder = this.sortOrder === 'ascend' ? 'descend' : null;
    }
    this.filterGateways();
  }

  showModal(): void {
    this.isEditMode = false;
    this.currentGateway = null;
    this.isVisible = true;
  }
  editGateway(index: number): void {
    this.isEditMode = true;
    this.editIndex = index;
    this.currentGateway = { ...this.listOfData[index] };
    this.isVisible = true;
  }

  handleModalSave(data: SoftwareGateway): void {
    if (this.isEditMode && this.editIndex !== null && this.currentGateway) {
      const updatedGateway = { ...this.currentGateway, ...data };
      this.listOfData[this.editIndex] = updatedGateway;
    } else {
      this.listOfData = [...this.listOfData, data];
    }
    this.filteredList = [...this.listOfData];
    this.isVisible = false;
    this.editIndex = null;
    this.currentGateway = null;
  }

  handleModalClose(): void {
    this.isVisible = false;
    this.editIndex = null;
    this.currentGateway = null;
  }

  handleLoadingChange(isLoading: boolean): void {
    this.isTableLoading = isLoading;
  }

  onClickOrganizations(value: any): void {
    this.router.navigate(['/organizations'], {
      queryParams: { organizationIdentifier: value }
    });
  }
  handleConfig(value: any) {
    this.router.navigate(['/software-gateway-config'], {
      queryParams: { gateWayId: value.orgId, name: value.name }
    });
  }
  handleDevices(value: any) {
    this.router.navigate(['/devices'], {
      queryParams: { gatewayId: value.gatewayId, name: value.name }
    });
  }
}
