import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Organization, SoftwareGateway } from '../../../models/software-gateway.model';
import { GatewayService } from '../../../services/gateway.service';

@Component({
  selector: 'app-gateway-edit-add',
  standalone: false,
  templateUrl: './gateway-edit-add.component.html',
  styleUrl: './gateway-edit-add.component.css'
})
export class GatewayEditAddComponent {
  @Input() isVisible = false;
  @Input() isEditMode = false;
  @Input() data: SoftwareGateway | null = null;
  @Output() close = new EventEmitter<void>();
  @Output() confirm = new EventEmitter<any>();
  @Output() loadingChange = new EventEmitter<boolean>();
  form: FormGroup;
  listOrganizations: Organization[] = [];

  constructor(
    private gatewayService: GatewayService,
    private fb: FormBuilder
  ) {
    this.form = this.fb.group({
      name: ['', [
        Validators.required,
        Validators.maxLength(255)
      ]],
      isEnabled: [true],
      orgId: ['', [
        Validators.required,
        Validators.maxLength(255)
      ]],
    });

    this.form.get('apiKey')?.valueChanges.subscribe(value => {
      if (typeof value === 'string') {
        const trimmedValue = value.trim().replace(/\s+/g, ' ');
        if (value !== trimmedValue) {
          this.form.get('apiKey')?.setValue(trimmedValue, { emitEvent: false });
          this.form.get('apiKey')?.updateValueAndValidity();
        }
      }
    });
  }
  ngOnInit() {
    // this.getOrganizations();
  }

  ngOnChanges() {
    if (this.isEditMode && this.data) {
      const nameValue = (this.data.apiKey || '').trim().replace(/\s+/g, ' ');
      this.form.patchValue({
        gatewayId: this.data.gatewayId || '',
        apiKey: nameValue,
        name: this.data.name || '',
        isEnabled: this.data.isEnabled === '1' ? true : false,
        orgId: this.data.orgId,
      }, { emitEvent: false });
    } else {
      this.form.reset({
        gatewayId: '',
        apiKey: '',
        name: '',
        isEnabled: true,
      });
    }
  }
  getOrganizations() {
    this.gatewayService.getOrganizations().subscribe(
      (data) => {
        this.listOrganizations = data;
      },
      (error) => {
        console.error('Error fetching Organizations list:', error);
      }
    );
  }
  // emulator auto generate ApiKey
  private generateApiKey(): string {
    const randomPart = Math.random().toString(36).substring(2, 5);
    return `keysw-${randomPart}-${Math.floor(Math.random() * 1000)}`;
  }
  // emulator auto generate token
  private generateToken(): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ.';
    for (let i = 0; i < 20; i++) {
      token += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    token += '.';
    for (let i = 0; i < 20; i++) {
      token += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return token;
  }

  handleSave() {
    if (this.form.valid) {
      const newApiKey = this.generateApiKey();
      const formValue = this.form.value;
      const newToken = this.generateToken();
      const statusValue = this.form.value.isEnabled ? '1' : '0';
      const saveData = this.isEditMode ? { ...formValue, gatewayId: this.data?.gatewayId, isEnabled: statusValue } :
        { ...formValue, apiKey: newApiKey, dateLastCheckedInUTC: new Date().toISOString(), tokenGateway: newToken, isEnabled: statusValue };
      this.loadingChange.emit(true);
      this.confirm.emit(saveData);
      this.form.reset();
      setTimeout(() => {
        this.loadingChange.emit(false);
      }, 500);
    } else {
      Object.values(this.form.controls).forEach(control => {
        if (control.invalid) {
          control.markAsDirty();
          control.updateValueAndValidity({ onlySelf: true });
        }
      });
    }
  }

  handleCancel() {
    this.form.reset();
    this.close.emit();
  }
}
