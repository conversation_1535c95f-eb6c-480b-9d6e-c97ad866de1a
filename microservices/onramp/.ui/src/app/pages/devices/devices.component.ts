import { Component } from '@angular/core';
import { Devices } from '../../models/devices.model';
import { ActivatedRoute, Router } from '@angular/router';
import { DevicesService } from '../../services/devices.service';

@Component({
  selector: 'app-devices',
  standalone: false,
  templateUrl: './devices.component.html',
  styleUrl: './devices.component.css'
})
export class DevicesComponent {
  isModalVisible = false;
  isEditMode = false;
  isVisible = false;
  isImportVisible = false;
  selectedData = null;
  listOfData: Devices[] = [];
  filteredList: Devices[] = [];
  searchTerm: string = '';
  currentDevice: Devices | null = null;
  editIndex: number | null = null;
  isTableLoading = false;
  devicesFilter: string | null = null;
  nameGateway: string | null = null;
  idGateway: string | null = null;

  constructor(
    private route: ActivatedRoute,
    private devicesService: DevicesService,
    private router: Router,
  ) { }

  getTagColor(status: string): string {
    return status === '1' ? 'green' : 'red';
  }
  getTagText(status: string): string {
    return status === '1' ? 'Enabled' : 'Disabled';
  }
  ngOnInit() {
    this.getApi();
    this.route.queryParams.subscribe(params => {
      if (params) {
        this.idGateway = params['gateWayId'];
        this.nameGateway = params['name'];
      }
    });
  }
  getApi() {
    this.isTableLoading = true;
    this.devicesService.getDevices().subscribe(
      (data) => {
        this.listOfData = data;
        this.filteredList = [...this.listOfData];
        this.isTableLoading = false;
        this.filterGateways();
      },
      (error) => {
        console.error('Error fetching Gateway list:', error);
        this.isTableLoading = false;
      }
    );
  }
  filterGateways() {
    this.filteredList = [...this.listOfData];
    if (this.devicesFilter) {
      this.filteredList = this.filteredList.filter(device =>
        device.devicesId === this.devicesFilter,
      );
    }
    if (this.searchTerm) {
      this.filteredList = this.listOfData.filter(device =>
        device.name.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        device.devicesId.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        device.gatewayId.toLowerCase().includes(this.searchTerm.toLowerCase())
      );
    }
  }
  formatDecimal(coord: string): string {
    return parseFloat(coord).toFixed(6);
  }
  // Convert from decimal to degrees-minutes-seconds
  convertToDMS(decimal: any): any {
    const absDeg = Math.floor(Math.abs(decimal));
    const minutes = Math.floor((Math.abs(decimal) - absDeg) * 60);
    const seconds = ((Math.abs(decimal) - absDeg - minutes / 60) * 3600).toFixed(2);
    const direction = decimal >= 0 ? (decimal <= 90 ? 'N' : 'E') : (decimal >= -90 ? 'S' : 'W');
    return `${absDeg}°${minutes}'${seconds}"${direction}`;
  }
  openAddPopup() {
    this.isEditMode = false;
    this.currentDevice = null;
    this.isVisible = true;
  }
  openEditPopup(index: number) {
    this.isEditMode = true;
    this.isVisible = true;
    this.editIndex = index;
    this.currentDevice = { ...this.listOfData[index] };
  }
  closeModal() {
    this.isModalVisible = false;
    this.selectedData = null;
    this.isEditMode = false;
  }
  handleModalSave(data: any) {
    if (this.isEditMode && this.editIndex !== null && this.currentDevice) {
      const updatedDevice = { ...this.currentDevice, ...data };
      this.listOfData[this.editIndex] = updatedDevice;
    } else {
      this.listOfData = [...this.listOfData, data];
    }
    this.filteredList = [...this.listOfData];
    this.isVisible = false;
    this.editIndex = null;
    this.currentDevice = null;
  }
  handleImportSave(data: any) {
    const newDevices: Devices[] = data.map((item: any) => ({
      devicesId: `DEV${Date.now()}${Math.floor(Math.random() * 1000)}`,
      gatewayId: item.gatewayId || '',
      latitude: item.latitude,
      longitude: item.longitude,
      iPAddress: item.iPAddress,
      port: item.port,
      flushConnectionMs: item.flushConnectionMs || '1000',
      enableRealtime: item.enableRealtime,
      isEnabled: item.isEnabled ? '1' : '0',
      name: `Device ${data.indexOf(item) + 1}`,
      type: item.type ? item.type : 'Imported'
    }));
    this.listOfData = [...this.listOfData, ...newDevices];
    this.filteredList = [...this.listOfData];
    this.filterGateways();
    this.isImportVisible = false;

  }
  handleModalClose() {
    this.isVisible = false;
    this.editIndex = null;
    this.currentDevice = null;
  }
  handleImportFile() {
    this.isImportVisible = true;
  }
  handleImportClose() {
    this.isImportVisible = false;
  }
  handleLoadingChange(isLoading: boolean) {
    this.isTableLoading = isLoading;
  }
  onClickSoftware(value: any) {
    this.router.navigate(['/software-gateway'], {
      queryParams: { organizationIdentifier: value }
    });
  }
}
