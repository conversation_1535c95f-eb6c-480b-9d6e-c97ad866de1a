import { Component } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { NzMessageService } from 'ng-zorro-antd/message';
import { Clipboard } from '@angular/cdk/clipboard';
import { NzModalService } from 'ng-zorro-antd/modal';
import { ConfigurationEditAddComponent } from './configuration-edit-add/configuration-edit-add.component';
import { GatewayService } from '../../services/gateway.service';

@Component({
  selector: 'app-software-gateway-configuration',
  standalone: false,
  templateUrl: './software-gateway-configuration.component.html',
  styleUrl: './software-gateway-configuration.component.css'
})
export class SoftwareGatewayConfigurationComponent {
  tableData: { key: string; value: any }[] = [];
  jsonString: string = '';
  isEditMode = false;
  isVisible = false;
  currentJson: any = null;
  isTableLoading = false;
  dataJson: any = {};
  nameGateway: string | null = null;
  idGateway: string | null = null;
  pageSize: number = 10;

  constructor(
    private route: ActivatedRoute,
    private gatewayService: GatewayService,
  ) {
    this.route.queryParams.subscribe(params => {
      if (params['gateWayId']) {
        this.idGateway = params['gateWayId'];
        this.nameGateway = params['name'];
      }
    });
  }
  ngOnInit() {
    this.getListOfData();
  }
  getListOfData() {
    this.isTableLoading = true;
    this.gatewayService.getConfigurations().subscribe(
      (data) => {
        this.dataJson = data[0];
        this.pageSize = Object.keys(this.dataJson).length;
        this.isTableLoading = false;
        this.initializeData();
      });
  }

  private initializeData() {
    if (this.dataJson) {
      this.jsonString = JSON.stringify(this.dataJson, null, 2);
      this.tableData = Object.entries(this.dataJson).map(([key, value]) => ({
        key,
        value: typeof value === 'object' ? JSON.stringify(value) : value
      }));
    }
  }
  editModal() {
    this.isEditMode = true;
    this.isVisible = true;
    this.currentJson = JSON.parse(this.jsonString);
  }

  handleModalSave(data: any) {
    this.dataJson = data;
    this.tableData = Object.entries(data).map(([key, value]) => ({
      key,
      value: typeof value === 'object' ? JSON.stringify(value) : value
    }));
  }

  handleModalClose() {
    this.isVisible = false;
    this.isEditMode = false;
  }

  handleLoadingChange(isLoading: boolean) {
    console.log('Loading state changed:', isLoading);
  }
}