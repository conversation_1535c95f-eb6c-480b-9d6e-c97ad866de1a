import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class OrganizationsService {
  constructor(private http: HttpClient) { }

  getOrganizations(): Observable<any[]> {
    return this.http.get<any[]>('/api/organizations');
  }

  getOrganizationsId(id: string): Observable<any[]> {
    return this.http.get<any[]>(`/api/organizations/${id}`);
  }

  createOrganization(data: { name: string; description: string; orgtypeidentifier: string }): Observable<any[]> {
    return this.http.post<any[]>(`/api/organizations`, data);
  }

  updateOrganization(id: string, payload: { name: string; description: string }): Observable<any[]> {
    return this.http.patch<any[]>(`/api/organizations/${id}`, payload);
  }

  deleteOrganization(id: string): Observable<any[]> {
    return this.http.delete<any[]>(`/api/organizations/${id}`);
  }
}