package organization

import (
	"net/http"
	"testing"

	"github.com/gorilla/mux"
	"github.com/stretchr/testify/assert"
)

func TestNewHandler(t *testing.T) {
	handler := NewHandler()

	assert.NotNil(t, handler, "NewHandler should return a non-nil handler")
	assert.IsType(t, &Hand<PERSON>{}, handler, "NewHandler should return a *Handler")
}

func TestHandler_RegisterRoutes(t *testing.T) {
	handler := NewHandler()
	router := mux.NewRouter()

	// Register routes
	handler.RegisterRoutes(router)

	// Test that routes are properly registered by walking through them
	var routes []string
	var methods []string

	err := router.Walk(func(route *mux.Route, router *mux.Router, ancestors []*mux.Route) error {
		template, _ := route.GetPathTemplate()
		methodsSlice, _ := route.GetMethods()

		routes = append(routes, template)
		if len(methodsSlice) > 0 {
			methods = append(methods, methodsSlice[0])
		}
		return nil
	})

	assert.NoError(t, err, "Walking routes should not produce an error")

	// Verify expected routes are registered
	expectedRoutes := []string{
		"/organizations",
		"/organizations",
		"/organizations/{identifier}",
		"/organizations/{identifier}",
		"/organizations/{identifier}",
		"/organizations/{organizationId}/roles",
		"/organizations/{organizationId}/roles",
		"/organizations/{organizationId}/roles/{roleId}",
		"/organizations/{organizationId}/roles/{roleId}/role-templates",
		"/organizations/{organizationId}/roles/{roleId}/permissions",
		"/organizations/{organizationId}/permissions/{permissionId}/roles/{roleId}",
		"/organizations/{organizationId}/invites",
		"/organizations/{organizationId}/invites",
		"/organizations/{organizationId}/invites/{inviteId}",
		"/organizations/{organizationId}/invites/{inviteId}/resend",
	}

	expectedMethods := []string{
		http.MethodPost,
		http.MethodGet,
		http.MethodGet,
		http.MethodPatch,
		http.MethodDelete,
		http.MethodGet,
		http.MethodPost,
		http.MethodDelete,
		http.MethodGet,
		http.MethodGet,
		http.MethodPatch,
		http.MethodPost,
		http.MethodGet,
		http.MethodDelete,
		http.MethodPost,
	}

	assert.Equal(t, len(expectedRoutes), len(routes), "Should register correct number of routes")
	assert.Equal(t, len(expectedMethods), len(methods), "Should register correct number of methods")

	// Check that all expected routes and methods are present
	for i, expectedRoute := range expectedRoutes {
		assert.Contains(t, routes, expectedRoute, "Route %s should be registered", expectedRoute)
		if i < len(methods) {
			assert.Contains(t, methods, expectedMethods[i], "Method %s should be registered", expectedMethods[i])
		}
	}
}

func TestHandler_RegisterRoutes_RouterIntegration(t *testing.T) {
	handler := NewHandler()
	router := mux.NewRouter()

	// Register routes
	handler.RegisterRoutes(router)

	// Test specific route patterns by creating test requests
	testCases := []struct {
		method      string
		path        string
		shouldMatch bool
	}{
		{http.MethodPost, "/organizations", true},
		{http.MethodGet, "/organizations", true},
		{http.MethodGet, "/organizations/test-org", true},
		{http.MethodPatch, "/organizations/test-org", true},
		{http.MethodDelete, "/organizations/test-org", true},
		{http.MethodPut, "/organizations", false},                  // Not registered
		{http.MethodGet, "/organizations/test-org/invalid", false}, // Wrong path
	}

	for _, tc := range testCases {
		t.Run(tc.method+"_"+tc.path, func(t *testing.T) {
			req, err := http.NewRequest(tc.method, tc.path, nil)
			assert.NoError(t, err)

			var match mux.RouteMatch
			matched := router.Match(req, &match)

			if tc.shouldMatch {
				assert.True(t, matched, "Route %s %s should match", tc.method, tc.path)
			} else {
				assert.False(t, matched, "Route %s %s should not match", tc.method, tc.path)
			}
		})
	}
}
