package user

import (
	"net/http"

	"github.com/gorilla/mux"
	"synapse-its.com/onramp/domain"
	"synapse-its.com/onramp/middlewares"
	RestUserPermissions "synapse-its.com/onramp/modules/user/permissions"
	RestInvites "synapse-its.com/shared/rest/onramp/invites"
)

type Handler struct {
	sessionStore domain.SessionStore
}

func NewHandler(sessionStore domain.SessionStore) *Handler {
	return &Handler{
		sessionStore: sessionStore,
	}
}

func (h *Handler) RegisterRoutes(router *mux.Router) {
	// Apply session middleware to user routes
	userRouter := router.PathPrefix("/user").Subrouter()
	userRouter.Use(middlewares.SessionMiddleware(h.sessionStore))

	userRouter.HandleFunc("/permissions", RestUserPermissions.Handler).Methods(http.MethodGet)

	// Add new invite routes
	userRouter.HandleFunc("/{userId}/invites", RestInvites.ListUserInvitesForUserHandler).Methods(http.MethodGet)
	userRouter.HandleFunc("/{userId}/invites/{inviteId}", RestInvites.RejectInviteHandler).Methods(http.MethodDelete)
	userRouter.HandleFunc("/{userId}/invites/validate", RestInvites.ValidateInviteHandler).Methods(http.MethodGet)
	userRouter.HandleFunc("/{userId}/invites/{inviteId}/redeem", RestInvites.RedeemInviteHandler).Methods(http.MethodPost)
}
