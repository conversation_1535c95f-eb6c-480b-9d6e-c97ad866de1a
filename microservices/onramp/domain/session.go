package domain

import (
	"golang.org/x/oauth2"
)

// Session holds information for a user session in the system.
type Session struct {
	UserID          string
	OAuthToken      *oauth2.Token
	UserPermissions *UserPermissions
}

// SessionStore defines the interface for session management.
type SessionStore interface {
	GetSession(sessionID string) (*Session, bool)
	SetSession(sessionID string, session *Session)
	ClearSession(sessionID string)
}
