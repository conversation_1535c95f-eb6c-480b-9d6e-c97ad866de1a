const { Given, When, Then } = require('@cucumber/cucumber');
const { By, until } = require('selenium-webdriver');

Given('I am on the organizations page', async function () {
  const baseUrl = process.env.BASE_URL || 'http://localhost:8080';
  const targetUrl = `${baseUrl}/organizations`;
  try {
    await this.driver.get(targetUrl);
    if (typeof this.waitForAngularStable === 'function') {
      await this.waitForAngularStable();
    }
    await this.driver.wait(until.elementLocated(By.id('organization-list')), 30000);
    await this.driver.wait(until.elementLocated(By.id('title-organizations')), 30000);
  } catch (err) {
    console.error('Navigation or element location error:', err);
    console.error('Page source at error:\n', await this.driver.getPageSource());
    throw err;
  }
});

Then('I see the page title {string}', async function (expectedTitle) {
  try {
    const titleElement = await this.driver.findElement(By.id('title-organizations'));
    const actualTitle = await titleElement.getText();
    if (actualTitle !== expectedTitle) {
      throw new Error(`Expected title "${expectedTitle}" but got "${actualTitle}"`);
    }
  } catch (err) {
    console.error('Title verification error:', err);
    console.error('Page source at error:\n', await this.driver.getPageSource());
    throw err;
  }
});

Then('I see the organization list with at least one record', async function () {
  try {
    const orgRows = await this.driver.findElements(By.css('#organization-list .default-row, #organization-list .highlight-row'));
    if (orgRows.length === 0) {
      throw new Error('No organizations found in the list');
    }
  } catch (err) {
    console.error(err);
    console.error('Page source at error:\n', await this.driver.getPageSource());
    throw err;
  }
});

Then('the first organization\'s name is not empty', async function () {
  try {
    const firstOrgName = await this.driver.findElement(By.css('#organization-list .default-row:first-child a, #organization-list .highlight-row:first-child a')).getText();
    if (!firstOrgName.trim()) {
      throw new Error('rds');
    }
  } catch (err) {
    console.error(err);
    console.error('Page source at error:\n', await this.driver.getPageSource());
    throw err;
  }
});

When('I enter the search keyword {string}', async function (searchTerm) {
  try {
    const searchInput = await this.driver.findElement(By.css('.form-search input[nz-input]'));
    await searchInput.clear();
    await searchInput.sendKeys(searchTerm);
    await this.driver.sleep(1000);
  } catch (err) {
    console.error(err);
    console.error('Page source at error:\n', await this.driver.getPageSource());
    throw err;
  }
});

Then('I see the organization list only contains organizations named {string}', async function (expectedName) {
  try {
    const orgNames = await this.driver.findElements(By.css('#organization-list .default-row a, #organization-list .highlight-row a'));
    for (let name of orgNames) {
      const text = await name.getText();
      if (text !== expectedName) {
        throw new Error(`Found unexpected organization name "${text}", expected "${expectedName}"`);
      }
    }
  } catch (err) {
    console.error(err);
    console.error('Page source at error:\n', await this.driver.getPageSource());
    throw err;
  }
});

When('I click the add organization button', async function () {
  try {
    const addButton = await this.driver.findElement(By.css('.add-btn'));
    await addButton.click();
    if (typeof this.waitForAngularStable === 'function') {
      await this.waitForAngularStable();
    }
    const modal = await this.driver.wait(until.elementLocated(By.css('.cdk-overlay-pane .ant-modal')), 15000);
    let isVisible = await modal.isDisplayed();
    let attempts = 0;
    while (!isVisible && attempts < 3) {
      await this.driver.sleep(2000);
      isVisible = await modal.isDisplayed();
      attempts++;
    }
    if (!isVisible) {
      throw new Error('Modal is not visible after waiting');
    }
  } catch (err) {
    console.error('Error in add button click:', err);
    console.error('Page source at error:\n', await this.driver.getPageSource());
    throw err;
  }
});

When('I fill in the organization name {string}', async function (orgName) {
  try {
    const modal = await this.driver.wait(until.elementLocated(By.css('.cdk-overlay-pane .ant-modal')), 15000);
    let isVisible = await modal.isDisplayed();
    let modalAttempts = 0;
    while (!isVisible && modalAttempts < 3) {
      await this.driver.sleep(2000);
      isVisible = await modal.isDisplayed();
      modalAttempts++;
    }
    if (!isVisible) {
      throw new Error('Modal is not visible after waiting');
    }
    const nameInput = await this.driver.wait(
      until.elementLocated(By.css('.cdk-overlay-pane .ant-modal input[id="new-organization-name"], .cdk-overlay-pane .ant-modal input[name="name"]')),
      15000
    );
    await this.driver.wait(until.elementIsVisible(nameInput), 5000);
    await nameInput.clear();
    await nameInput.sendKeys(orgName);
  } catch (err) {
    console.error('Error in filling organization name:', err);
    console.error('Page source at error:\n', await this.driver.getPageSource());
    throw new Error(`Failed to fill in organization name "${orgName}": ${err.message}`);
  }
});

When('I fill in the organization description {string}', async function (orgDescription) {
  try {
    const modal = await this.driver.wait(until.elementLocated(By.css('.cdk-overlay-pane .ant-modal')), 15000);
    let isVisible = await modal.isDisplayed();
    let modalAttempts = 0;
    while (!isVisible && modalAttempts < 3) {
      await this.driver.sleep(2000);
      isVisible = await modal.isDisplayed();
      modalAttempts++;
    }
    if (!isVisible) {
      throw new Error('Modal is not visible after waiting');
    }

    const descriptionInput = await this.driver.wait(
      until.elementLocated(By.css('.cdk-overlay-pane .ant-modal input[id="new-organization-description"], .cdk-overlay-pane .ant-modal input[name="description"]')),
      15000
    );
    await this.driver.wait(until.elementIsVisible(descriptionInput), 5000);
    await descriptionInput.clear();
    await descriptionInput.sendKeys(orgDescription);
  } catch (err) {
    console.error('Error in filling organization description:', err);
    console.error('Page source at error:\n', await this.driver.getPageSource());
    throw new Error(`Failed to fill in organization description "${orgDescription}": ${err.message}`);
  }
});

When('I submit the new organization form', async function () {
  try {
    const submitButton = await this.driver.wait(until.elementLocated(By.css('.cdk-overlay-pane .ant-modal button[nzType="primary"]:not([nzType="default"])')), 15000);
    await this.driver.wait(until.elementIsEnabled(submitButton), 5000);
    await submitButton.click();
    await this.driver.sleep(5000); // Increased delay to ensure database update
    await this.driver.wait(until.elementLocated(By.id('organization-list')), 20000);
  } catch (err) {
    console.error('Error in submitting form:', err);
    console.error('Page source at error:\n', await this.driver.getPageSource());
    throw err;
  }
});

Then('I see {string} in the organization list', async function (orgName) {
  try {
    await this.driver.wait(until.elementLocated(By.id('organization-list')), 30000);
    const orgLinks = await this.driver.findElements(By.css('#organization-list .default-row a, #organization-list .highlight-row a'));
    let found = false;
    const allNames = [];
    for (let link of orgLinks) {
      const text = await link.getText();
      const trimmedText = text.trim();
      allNames.push(trimmedText);
      if (trimmedText === orgName) {
        found = true;
        break;
      }
    }
    if (!found) {
      await this.checkTheDatabaseFor(orgName);
      throw new Error(`Expected to see "${orgName}" but found names: ${allNames.join(', ')}`);
    }
  } catch (err) {
    console.error(err);
    console.error('Page source at error:\n', await this.driver.getPageSource());
    throw err;
  }
});

When('I click the edit button of the first organization', async function () {
  try {
    const editButton = await this.driver.findElement(By.css('#organization-list .default-row:first-child .edit-btn, #organization-list .highlight-row:first-child .edit-btn'));
    await editButton.click();
    const modal = await this.driver.wait(until.elementLocated(By.css('.cdk-overlay-pane .ant-modal')), 15000);
    let isVisible = await modal.isDisplayed();
    let attempts = 0;
    while (!isVisible && attempts < 3) {
      await this.driver.sleep(2000);
      isVisible = await modal.isDisplayed();
      attempts++;
    }
    if (!isVisible) {
      throw new Error('Modal is not visible after waiting');
    }
  } catch (err) {
    console.error('Error in edit button click:', err);
    console.error('Page source at error:\n', await this.driver.getPageSource());
    throw err;
  }
});

When('I change the organization name to {string}', async function (newName) {
  try {
    const modal = await this.driver.wait(until.elementLocated(By.css('.cdk-overlay-pane .ant-modal')), 15000);
    let isVisible = await modal.isDisplayed();
    let modalAttempts = 0;
    while (!isVisible && modalAttempts < 3) {
      await this.driver.sleep(2000);
      isVisible = await modal.isDisplayed();
      modalAttempts++;
    }
    if (!isVisible) {
      throw new Error('Modal is not visible after waiting');
    }
    const nameInput = await this.driver.wait(
      until.elementLocated(By.css('.cdk-overlay-pane .ant-modal input[id="new-organization-name"], .cdk-overlay-pane .ant-modal input[name="name"]')),
      15000
    );
    await this.driver.wait(until.elementIsVisible(nameInput), 5000);
    await nameInput.clear();
    await nameInput.sendKeys(newName);
  } catch (err) {
    console.error('Error in changing organization name:', err);
    console.error('Page source at error:\n', await this.driver.getPageSource());
    throw new Error(`Failed to change organization name to "${newName}": ${err.message}`);
  }
});

When('I submit the edit form', async function () {
  try {
    const submitButton = await this.driver.wait(until.elementLocated(By.css('.cdk-overlay-pane .ant-modal button[nzType="primary"]:not([nzType="default"])')), 15000);
    await this.driver.wait(until.elementIsEnabled(submitButton), 5000);
    await submitButton.click();
    await this.driver.sleep(5000);
    await this.driver.wait(until.elementLocated(By.id('organization-list')), 25000);
  } catch (err) {
    console.error('Error in submitting edit form:', err);
    console.error('Page source at error:\n', await this.driver.getPageSource());
    throw err;
  }
});

When('I check the database for {string}', async function (orgName) {
  try {
    const baseUrl = process.env.BASE_URL || 'http://localhost:8080';
    let response = null;
    let attempts = 0;
    const maxAttempts = 3;
    while (attempts < maxAttempts) {
      try {
        response = await this.driver.executeAsyncScript(`
          var callback = arguments[arguments.length - 1];
          fetch('${baseUrl}/api/organizations?name=${encodeURIComponent(arguments[0])}', { method: 'GET' })
            .then(response => {
              if (!response.ok) {
                throw new Error('Network response was not ok: ' + response.status);
              }
              return response.json();
            })
            .then(data => callback(data))
            .catch(err => callback({ error: err.message }));
        `, orgName);

        if (response && !response.error) {
          break; // Successful response, exit retry loop
        }
        attempts++;
        await this.driver.sleep(2000); // Wait before retrying
      } catch (err) {
        attempts++;
        await this.driver.sleep(2000);
      }
    }
    if (response && response.error) {
      throw new Error(`API error: ${response.error}`);
    }
    if (!response) {
      throw new Error('Failed to fetch data after multiple attempts');
    }
    // Check if the response contains an organization with the expected name
    this.lastDbCheck = response && response.data && Array.isArray(response.data) && response.data.some(org => org && org.name && org.name.trim() === orgName.trim());
    if (!this.lastDbCheck) {
      throw new Error(`Organization "${orgName}" not found in API response: ${JSON.stringify(response, null, 2)}`);
    }
  } catch (err) {
    console.error('Database check error:', err);
    console.error('Page source:\n', await this.driver.getPageSource());
    throw new Error(`Failed to check database for "${orgName}": ${err.message}`);
  }
});

Then('the database contains {string}', async function (orgName) {
  try {
    if (typeof this.lastDbCheck === 'undefined' || this.lastDbCheck === null) {
      throw new Error('Database check failed or not performed');
    }
    if (!this.lastDbCheck) {
      throw new Error(`Database does not contain "${orgName}"`);
    }
  } catch (err) {
    console.error('Database verification error:', err);
    console.error('Page source at error:\n', await this.driver.getPageSource());
    throw err;
  }
});

When('I refresh the organization list', async function () {
  try {
    await this.driver.navigate().refresh();
    await this.driver.wait(until.elementLocated(By.id('organization-list')), 20000);
  } catch (err) {
    console.error(err);
    console.error('Page source at error:\n', await this.driver.getPageSource());
    throw err;
  }
});