package device

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"net/http"
	"slices"
	"strings"

	gatewayv1 "bitbucket.org/synapse-its/protobuf-schemas/protomessages/gateway/v1"
	"github.com/redis/go-redis/v9"
	"google.golang.org/protobuf/proto"
	"synapse-its.com/broker/api/brokerShared"
	apiShared "synapse-its.com/shared/api"
	authorizer "synapse-its.com/shared/api/authorizer"
	response "synapse-its.com/shared/api/response"
	connect "synapse-its.com/shared/connect"
	"synapse-its.com/shared/devices"
	logger "synapse-its.com/shared/logger"
	"synapse-its.com/shared/pubsubdata"
)

var (
	userPermissionsFromContext = authorizer.UserPermissionsFromContext
	deviceProcessRmsData       = devices.ProcessRmsData
	validateDeviceAccess       = brokerShared.ValidateDeviceAccess
)

func Handler(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	// Get user info from jwt authorizer
	userPermissions, ok := userPermissionsFromContext(ctx)
	if !ok {
		logger.Error("Unable to retrieve user info from request context")
		response.CreateInternalErrorResponse(w)
		return
	}

	allDevices, deviceOrigID, deviceUUID, err := parseRequest(r)
	if err != nil {
		logger.Infof("Unable to parse API request: %v", err)
		response.CreateUnauthorizedResponse(w)
		return
	}

	// Get the postgres connection.
	connections, err := connect.GetConnections(ctx)
	if err != nil {
		logger.Errorf("%v", err)
		response.CreateInternalErrorResponse(w)
		return
	}
	pg := connections.Postgres
	rd := connections.Redis

	// Get the list of all devices the user is authorized for
	var authorizedDevices []string
	if allDevices {
		authorizedDevices, err = userPermissions.GetAuthorizedDevices(pg)
		if err != nil {
			logger.Errorf("%v", err)
			response.CreateInternalErrorResponse(w)
			return
		}
	} else {
		deviceUUID, err := validateDeviceAccess(pg, userPermissions, deviceOrigID, deviceUUID, "org_view_devices", "device_group_view_devices", "location_group_view_devices")
		if err != nil {
			logger.Errorf("%v", err)
			// Check if this is an unauthorized access error or a database error
			if strings.Contains(err.Error(), "user does not have permission") {
				response.CreateUnauthorizedResponse(w)
			} else {
				response.CreateInternalErrorResponse(w)
			}
			return
		}
		authorizedDevices = append(authorizedDevices, deviceUUID)
	}

	// Retrieve all device information and returns pointer
	// Only devices the user is authorized for will be returned.
	// Information includes: device info, device last fault info, and software gateway identifier.
	// Software gateway will later be used to know what redis key to pull for the latest data.
	deviceData, gatewayIds, err := getPgDeviceInfo(pg, userPermissions, authorizedDevices)
	if err != nil {
		logger.Errorf("%v", err)
		response.CreateInternalErrorResponse(w)
		return
	}

	// Retrieve and append the current device status from redis to deviceData pointer
	data, err := getRedisDeviceStatus(ctx, rd, deviceData, gatewayIds)
	if err != nil {
		logger.Errorf("%v", err)
		response.CreateInternalErrorResponse(w)
		return
	}

	response.CreateSuccessResponse(data, w)
}

var parseRequest = func(r *http.Request) (allDevices bool, deviceOrigID int64, deviceUUID string, err error) {
	rQuery := r.URL.Query()
	switch len(rQuery) {
	case 0:
		allDevices = true
	case 1:
		deviceIdString := rQuery.Get("deviceid")
		if deviceIdString == "" {
			return allDevices, deviceOrigID, deviceUUID, fmt.Errorf("%w: %v", ErrInvalidUrlQuery, rQuery)
		}

		// Parse as json.RawMessage to use ParseInt64OrUUID
		deviceIdRaw := json.RawMessage(fmt.Sprintf(`"%s"`, deviceIdString))
		deviceOrigID, deviceUUID, err = brokerShared.ParseInt64OrUUID(deviceIdRaw)
		if err != nil {
			logger.Infof("unable to convert query string parameter deviceid to int64: %v", err)
			return allDevices, deviceOrigID, deviceUUID, fmt.Errorf("%w: %v", ErrConvertQueryParam, err)
		}

		allDevices = false
	default:
		logger.Infof("unexpected usage - incorrect query string parameters")
		return allDevices, deviceOrigID, deviceUUID, ErrUnexpectedUsage
	}

	return allDevices, deviceOrigID, deviceUUID, nil
}

var getPgDeviceInfo = func(pg connect.DatabaseExecutor, userPermissions *authorizer.UserPermissions, authorizedDevices []string) (*[]dataPayload, []string, error) {
	// If no devices are authorized, return an empty slice and nil error
	if len(authorizedDevices) == 0 {
		return nil, nil, nil
	}

	placeholders := make([]string, len(authorizedDevices))
	args := make([]any, len(authorizedDevices))
	for i, id := range authorizedDevices {
		placeholders[i] = fmt.Sprintf("$%d", i+1)
		args[i] = id
	}
	placeholder := strings.Join(placeholders, ",")

	// Build permission conditions directly from user permissions
	var orgScopeIDs, deviceGroupScopeIDs, locationGroupScopeIDs []string
	argIndex := len(args) + 1

	for _, permission := range userPermissions.Permissions {
		hasManagePermission := false
		for _, perm := range permission.Permissions {
			if perm == "org_manage_devices" || perm == "device_group_manage_devices" || perm == "location_group_manage_devices" {
				hasManagePermission = true
				break
			}
		}
		if hasManagePermission {
			switch permission.Scope {
			case "org":
				orgScopeIDs = append(orgScopeIDs, permission.ScopeID)
			case "device_group":
				deviceGroupScopeIDs = append(deviceGroupScopeIDs, permission.ScopeID)
			case "location_group":
				locationGroupScopeIDs = append(locationGroupScopeIDs, permission.ScopeID)
			}
		}
	}

	// Build the permission check for IP address and port using EXISTS subqueries
	var permissionCheck string
	if len(orgScopeIDs) > 0 || len(deviceGroupScopeIDs) > 0 || len(locationGroupScopeIDs) > 0 {
		var allConditions []string

		if len(orgScopeIDs) > 0 {
			orgPlaceholders := make([]string, len(orgScopeIDs))
			for i, orgID := range orgScopeIDs {
				orgPlaceholders[i] = fmt.Sprintf("$%d", argIndex)
				args = append(args, orgID)
				argIndex++
			}
			allConditions = append(allConditions, fmt.Sprintf("sg.OrganizationId IN (%s)", strings.Join(orgPlaceholders, ",")))
		}

		if len(deviceGroupScopeIDs) > 0 {
			dgPlaceholders := make([]string, len(deviceGroupScopeIDs))
			for i, groupID := range deviceGroupScopeIDs {
				dgPlaceholders[i] = fmt.Sprintf("$%d", argIndex)
				args = append(args, groupID)
				argIndex++
			}
			allConditions = append(allConditions, fmt.Sprintf(`EXISTS (
				SELECT 1 FROM {{DeviceGroupDevices}} dgd 
				WHERE dgd.DeviceID = d.Id 
				AND dgd.DeviceGroupId IN (%s)
			)`, strings.Join(dgPlaceholders, ",")))
		}

		if len(locationGroupScopeIDs) > 0 {
			lgPlaceholders := make([]string, len(locationGroupScopeIDs))
			for i, locationGroupID := range locationGroupScopeIDs {
				lgPlaceholders[i] = fmt.Sprintf("$%d", argIndex)
				args = append(args, locationGroupID)
				argIndex++
			}
			allConditions = append(allConditions, fmt.Sprintf(`EXISTS (
				SELECT 1 FROM {{LocationGroupLocations}} lgl 
				JOIN {{Location}} loc ON loc.Id = lgl.LocationId 
				WHERE loc.Id = d.LocationId 
				AND lgl.LocationGroupId IN (%s)
			)`, strings.Join(lgPlaceholders, ",")))
		}

		permissionCheck = strings.Join(allConditions, " OR ")
	} else {
		// User has no manage permissions, always return false
		permissionCheck = "false"
	}

	query := fmt.Sprintf(`SELECT DISTINCT
		COALESCE(df.MonitorTime, '1970-01-01 00:00:00'::timestamptz) AS MonitorTime,
		COALESCE(df.Fault, '') AS Fault,
		COALESCE(df.ChannelGreenStatus, '{}'::boolean[]) AS ChannelGreenStatus,
		COALESCE(df.ChannelYellowStatus, '{}'::boolean[]) AS ChannelYellowStatus,
		COALESCE(df.ChannelRedStatus, '{}'::boolean[]) AS ChannelRedStatus,
		d.OrigId as ID,
		d.Id as DeviceID,
		d.Type as DeviceType,
		d.IsEnabled as IsEnabled,
		COALESCE(l.Latitude::text, '') AS Latitude,
		COALESCE(l.Longitude::text, '') AS Longitude,
		COALESCE(
			CASE WHEN (%s) THEN d.IPAddress ELSE '***.***.***.***' END, 
			''
		) as IPAddress,
		COALESCE(
			CASE WHEN (%s) THEN d.Port::text ELSE '*****' END, 
			''
		) as Port,
		COALESCE(dmn.MonitorId, 0) AS MonitorID,
		COALESCE(dmn.MonitorName, '') AS MonitorName,
		COALESCE(dre.EngineVersion,'0') AS EngineVersion,
		COALESCE(dre.EngineRevision, '0') AS EngineRevision,
		COALESCE(dl.DateUploaded, '1970-01-01 00:00:00'::timestamptz) AS DateUploadedUTC,
		sg.MachineKey AS SoftwareGatewayIdentifier,
		COALESCE(sg.GatewayVersion, '') AS GatewayVersion
	FROM {{Device}} as d
	JOIN {{SoftwareGateway}} as sg
		ON sg.Id = d.SoftwareGatewayId
	LEFT JOIN {{DeviceMonitorName}} as dmn
		ON dmn.DeviceId = d.Id
	LEFT JOIN {{DeviceRMSEngine}} as dre
		ON dre.DeviceId = d.Id
	LEFT JOIN {{Location}} as l
		ON l.Id = d.LocationId
	LEFT JOIN {{DeviceFault}} as df
		ON df.DeviceId = d.Id
	LEFT JOIN {{DeviceLog}} as dl
		ON dl.DeviceId = d.Id
	WHERE d.Id in (%s)`, permissionCheck, permissionCheck, placeholder)

	devices := &[]pgDeviceInfo{}
	err := pg.QueryGenericSlice(devices, query, args...)
	if err != nil {
		return nil, nil, err
	}

	devs := *devices
	// get list of unique gateways
	gatewayIds := make([]string, 0, len(devs))
	for _, d := range devs {
		key := fmt.Sprintf("GatewayRMSData:%s", d.SoftwareGatewayIdentifier)
		// check against the few already collected
		if !slices.Contains(gatewayIds, key) {
			gatewayIds = append(gatewayIds, key)
		}
	}
	// Convert to dataPayload
	return convertPgDeviceInfos(devices), gatewayIds, nil
}

var getRedisDeviceStatus = func(ctx context.Context, rd *redis.Client, deviceInfo *[]dataPayload, gatewayIds []string) (*[]dataPayload, error) {
	if deviceInfo == nil || len(*deviceInfo) == 0 || len(gatewayIds) == 0 {
		return nil, nil
	}

	// build an index map for deviceId -> map for redis matching
	idxMap := make(map[string]int, len(*deviceInfo))
	for i, dp := range *deviceInfo {
		idxMap[dp.DeviceIdentifier] = i
	}

	vals, err := rd.MGet(ctx, gatewayIds...).Result()
	if err != nil {
		return nil, err
	}

	// pull rmsData for all gatewayIds
	for i, raw := range vals {
		// no data for gateway
		if raw == nil {
			logger.Warnf("no info for redis key: %s", gatewayIds[i])
			continue
		}
		// assert that it's a string
		str, ok := raw.(string)
		if !ok {
			logger.Errorf("unable to convert redis value to string: %s got type %v", gatewayIds[i], fmt.Sprintf("%T", raw))
			continue
		}
		var blob apiShared.RedisData
		err = json.Unmarshal([]byte(str), &blob)
		if err != nil {
			return nil, err
		}

		decode, err := base64.StdEncoding.DecodeString(blob.MsgData)
		if err != nil {
			logger.Errorf("%v: could not base64 decode redis message, %v", err, blob.MsgData)
			continue
		}
		msg := &gatewayv1.DeviceData{}
		if err = proto.Unmarshal(decode, msg); err != nil {
			logger.Errorf("%v: could not proto unmarshal redis message, %v", err, decode)
			continue
		}

		for i, d := range msg.GetMessages() {
			idx, found := idxMap[d.DeviceId]
			if !found {
				// non-requested device, continue to look for requested devices...
				continue
			}

			dp := &(*deviceInfo)[idx]

			logger.Debugf("/data/device endpoint, rms data from redis, processing record : %v, Deviceid : %v", i, d.DeviceId)
			pubsubheader := &pubsubdata.HeaderDetails{GatewayTimezone: blob.GatewayTimezone}
			rmsData, rmsHeader, err := deviceProcessRmsData(pubsubheader, d.GetMessage())
			if err != nil {
				// TODO: set status to error
				logger.Infof("Error parsing record: %v", err)
				continue
			}

			addRedisToPayload(dp, rmsHeader, rmsData)

		}

	}
	return deviceInfo, nil
}
