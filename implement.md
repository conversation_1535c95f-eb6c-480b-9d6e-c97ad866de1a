# Invite System Implementation Plan

## Overview

This document outlines the step-by-step implementation plan for the invite system as specified in `requirement.txt`. The system will allow organizations to create, manage, and track user invitations via email.

## Architecture Overview

The implementation follows the existing microservices architecture pattern:
- **Shared Components**: Core business logic and data models in `/shared`
- **Microservice Endpoints**: REST API handlers in `/microservices/onramp`
- **Database Schema**: PostgreSQL tables with BigQuery audit logging
- **Email Integration**: PubSub messaging for email delivery

## Phase 1: Database Schema Implementation

### 1.1 PostgreSQL Schema Updates

**File**: `schemas/data-core-pg/1.0/updates/invite_system.sql`

```sql
-- UserInvites table for storing invitation data
CREATE TABLE {{UserInvites}} (
  id UUID NOT NULL DEFAULT gen_random_uuid(),
  organizationidentifier UUID NOT NULL,
  tokenhash TEXT NOT NULL,
  email TEXT NOT NULL,
  inviterid UUID NOT NULL,
  customroleid UUID NOT NULL,
  status TEXT NOT NULL,
  message TEXT,
  requiresso BOOL DEFAULT FALSE,
  retrycount INT NOT NULL DEFAULT 0,
  retried TIMESTAMPTZ,
  expired TIMESTAMPTZ,
  created TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  sent TIMESTAMPTZ,
  updated TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  CONSTRAINT {{UserInvites_PK}} PRIMARY KEY (id),
  CONSTRAINT {{UserInvites_tokenhash_UQ}} UNIQUE (tokenhash),
  CONSTRAINT {{UserInvites_inviterid_FK}} FOREIGN KEY (inviterid) REFERENCES "User"(id),
  CONSTRAINT {{UserInvites_org_FK}} FOREIGN KEY (organizationidentifier) REFERENCES "Organization"(id),
  CONSTRAINT {{UserInvites_status_CHK}} CHECK (status IN ('pending','redeemed','revoked','expired','rejected')),
  CONSTRAINT {{UserInvites_CustomRole_FK}} FOREIGN KEY (customroleid) REFERENCES {{CustomRole}} (Id)
);

-- Indexes for performance
CREATE INDEX {{UserInvites_organizationidentifier_IDX}} ON {{UserInvites}} (organizationidentifier);
CREATE INDEX {{UserInvites_email_IDX}} ON {{UserInvites}} (email);
CREATE INDEX {{UserInvites_status_IDX}} ON {{UserInvites}} (status);
CREATE INDEX {{UserInvites_created_IDX}} ON {{UserInvites}} (created);
```

### 1.2 BigQuery Audit Schema

**File**: `schemas/data-core-bq/1.0/invite_events.sql`

```sql
-- Audit table for BigQuery ingestion
CREATE TABLE {{InviteEvents}} (
  userinviteid  STRING    {% OPTIONS(description="The UUID of the userinvite ") %},
  eventtype     STRING    {% OPTIONS(description=" create, retry, revoke, redeem, rejected") %},
  actor         STRING    {% OPTIONS(description="Either the UUID of the user making the change or the email of the person being invited") %},
  eventtime     TIMESTAMP {% OPTIONS(description="The timestamp the invite event") %},
  organizationidentifier STRING    {% OPTIONS(description="The UUID of the organization") %},
  tokenhash STRING    {% OPTIONS(description="The tokenhash for the email invite ") %},
  email STRING    {% OPTIONS(description="The Email of the person being invited") %},
  inviterid STRING    {% OPTIONS(description="The UUID of the user that did the invite") %},
  organizationrole STRING    {% OPTIONS(description="The UUID of the custom role used assigned to this user") %},
  status STRING    {% OPTIONS(description="The current status of the invite") %},
  message STRING    {% OPTIONS(description="The custom message added to the invite, this is OPTIONAL") %},
  requiresso BOOL {% OPTIONS(description="Flag to require SSO when creating account") %},
  retrycount INT64    {% OPTIONS(description="The count of retrys") %},
  retried TIMESTAMP {% OPTIONS(description="The timestamp of the latest retry") %},
  expired TIMESTAMP {% OPTIONS(description="The timestamp the invite event expires NULL means it wont expire") %},
  created TIMESTAMP {% OPTIONS(description="The timestamp the invite event was created") %},
  sent TIMESTAMP {% OPTIONS(description="The timestamp the invite event was sent") %},
  updated TIMESTAMP {% OPTIONS(description="The timestamp the invite event status was changed") %}
)
{% PARTITION BY DATE(eventtime) CLUSTER BY userinviteid %};
```

## Phase 2: Shared Components

### 2.1 Data Models

**File**: `shared/rest/onramp/invites/schemas.go`

```go
package invites

import (
    "time"
    "github.com/google/uuid"
)

// CreateInviteRequest represents the request body for creating an invite
type CreateInviteRequest struct {
    Email            string     `json:"email" validate:"required,email"`
    InviterID        uuid.UUID  `json:"inviterid" validate:"required"`
    Message          *string    `json:"message"`
    OrganizationRole uuid.UUID  `json:"organizationrole" validate:"required"`
    ExpiredDays      *int       `json:"expireddays"`
    RequireSSO       bool       `json:"requiresso"`
}

// ResendInviteRequest represents the request body for resending an invite
type ResendInviteRequest struct {
    Actor       uuid.UUID `json:"actor" validate:"required"`
    Message     *string   `json:"message"`
    ExpiredDays *int      `json:"expireddays"`
}

// RevokeInviteRequest represents the request body for revoking an invite
type RevokeInviteRequest struct {
    Actor uuid.UUID `json:"actor" validate:"required"`
}

// InviteResponse represents the response for invite operations
type InviteResponse struct {
    ID                     uuid.UUID  `json:"id"`
    OrganizationIdentifier uuid.UUID  `json:"organizationidentifier"`
    Email                  string     `json:"email"`
    InviterID              uuid.UUID  `json:"inviterid"`
    CustomRoleID           uuid.UUID  `json:"customroleid"`
    Status                 string     `json:"status"`
    Message                *string    `json:"message"`
    RequireSSO             bool       `json:"requiresso"`
    RetryCount             int        `json:"retrycount"`
    Retried                *time.Time `json:"retried"`
    Expired                *time.Time `json:"expired"`
    Created                time.Time  `json:"created"`
    Sent                   *time.Time `json:"sent"`
    Updated                time.Time  `json:"updated"`
}

// InviteEvent represents audit events for BigQuery
type InviteEvent struct {
    UserInviteID            string     `json:"userinviteid"`
    EventType               string     `json:"eventtype"`
    Actor                   string     `json:"actor"`
    EventTime               time.Time  `json:"eventtime"`
    OrganizationIdentifier  string     `json:"organizationidentifier"`
    TokenHash               string     `json:"tokenhash"`
    Email                   string     `json:"email"`
    InviterID               string     `json:"inviterid"`
    OrganizationRole        string     `json:"organizationrole"`
    Status                  string     `json:"status"`
    Message                 *string    `json:"message"`
    RequireSSO              bool       `json:"requiresso"`
    RetryCount              int        `json:"retrycount"`
    Retried                 *time.Time `json:"retried"`
    Expired                 *time.Time `json:"expired"`
    Created                 time.Time  `json:"created"`
    Sent                    *time.Time `json:"sent"`
    Updated                 time.Time  `json:"updated"`
}
```

### 2.2 Repository Interface

**File**: `shared/rest/onramp/invites/repository.go`

```go
package invites

import (
    "context"
    "time"
    "github.com/google/uuid"
    "synapse-its.com/shared/connect"
)

type Repository interface {
    CreateInvite(ctx context.Context, pg connect.DatabaseExecutor, invite *InviteData) (*InviteData, error)
    GetInviteByID(ctx context.Context, pg connect.DatabaseExecutor, id uuid.UUID) (*InviteData, error)
    GetInviteByToken(ctx context.Context, pg connect.DatabaseExecutor, tokenHash string) (*InviteData, error)
    GetInvitesByOrganization(ctx context.Context, pg connect.DatabaseExecutor, orgID uuid.UUID) ([]*InviteData, error)
    GetInvitesByUserEmail(ctx context.Context, pg connect.DatabaseExecutor, email string) ([]*InviteData, error)
    UpdateInviteStatus(ctx context.Context, pg connect.DatabaseExecutor, id uuid.UUID, status string, actor uuid.UUID) error
    UpdateInviteForResend(ctx context.Context, pg connect.DatabaseExecutor, id uuid.UUID, tokenHash string, message *string, expired *time.Time) error
    LogInviteEvent(ctx context.Context, batch connect.BigQueryExecutorInterface, event *InviteEvent) error
}

type InviteData struct {
    ID                     uuid.UUID
    OrganizationIdentifier uuid.UUID
    TokenHash              string
    Email                  string
    InviterID              uuid.UUID
    CustomRoleID           uuid.UUID
    Status                 string
    Message                *string
    RequireSSO             bool
    RetryCount             int
    Retried                *time.Time
    Expired                *time.Time
    Created                time.Time
    Sent                   *time.Time
    Updated                time.Time
}
```

### 2.3 Service Layer

**File**: `shared/rest/onramp/invites/service.go`

```go
package invites

import (
    "context"
    "crypto/rand"
    "encoding/hex"
    "fmt"
    "time"
    "github.com/google/uuid"
    "synapse-its.com/shared/connect"
    "synapse-its.com/shared/logger"
    "synapse-its.com/shared/pubsubdata"
)

type Service struct {
    repo Repository
}

func NewService(repo Repository) *Service {
    return &Service{repo: repo}
}

func (s *Service) CreateInvite(ctx context.Context, connections *connect.Connections, req *CreateInviteRequest, orgID uuid.UUID) (*InviteResponse, error) {
    // Generate secure token hash
    tokenHash, err := s.generateTokenHash()
    if err != nil {
        return nil, fmt.Errorf("failed to generate token: %w", err)
    }

    // Calculate expiration
    var expired *time.Time
    if req.ExpiredDays != nil && *req.ExpiredDays > 0 {
        exp := time.Now().AddDate(0, 0, *req.ExpiredDays)
        expired = &exp
    }

    // Create invite data
    invite := &InviteData{
        OrganizationIdentifier: orgID,
        TokenHash:              tokenHash,
        Email:                  req.Email,
        InviterID:              req.InviterID,
        CustomRoleID:           req.OrganizationRole,
        Status:                 "pending",
        Message:                req.Message,
        RequireSSO:             req.RequireSSO,
        RetryCount:             0,
        Created:                time.Now(),
        Updated:                time.Now(),
        Expired:                expired,
    }

    // Save to database
    createdInvite, err := s.repo.CreateInvite(ctx, connections.Postgres, invite)
    if err != nil {
        return nil, fmt.Errorf("failed to create invite: %w", err)
    }

    // Log to BigQuery
    event := s.createInviteEvent(createdInvite, "create", req.InviterID.String())
    if err := s.repo.LogInviteEvent(ctx, connections.Bigquery, event); err != nil {
        logger.Errorf("failed to log invite event: %v", err)
    }

    // Publish email notification
    if err := s.publishEmailNotification(ctx, connections.Pubsub, createdInvite, req.Message); err != nil {
        logger.Errorf("failed to publish email notification: %v", err)
    }

    return s.toResponse(createdInvite), nil
}

func (s *Service) generateTokenHash() (string, error) {
    bytes := make([]byte, 32)
    if _, err := rand.Read(bytes); err != nil {
        return "", err
    }
    return hex.EncodeToString(bytes), nil
}

func (s *Service) createInviteEvent(invite *InviteData, eventType, actor string) *InviteEvent {
    return &InviteEvent{
        UserInviteID:           invite.ID.String(),
        EventType:              eventType,
        Actor:                  actor,
        EventTime:              time.Now(),
        OrganizationIdentifier: invite.OrganizationIdentifier.String(),
        TokenHash:              invite.TokenHash,
        Email:                  invite.Email,
        InviterID:              invite.InviterID.String(),
        OrganizationRole:       invite.CustomRoleID.String(),
        Status:                 invite.Status,
        Message:                invite.Message,
        RequireSSO:             invite.RequireSSO,
        RetryCount:             invite.RetryCount,
        Retried:                invite.Retried,
        Expired:                invite.Expired,
        Created:                invite.Created,
        Sent:                   invite.Sent,
        Updated:                invite.Updated,
    }
}

func (s *Service) publishEmailNotification(ctx context.Context, pubsub connect.PsClient, invite *InviteData, message *string) error {
    // Generate email template
    emailHTML := s.generateEmailTemplate(invite, message)
    
    payload := pubsubdata.EmailNotification{
        Type:    "email",
        To:      invite.Email,
        Message: emailHTML,
    }

    return pubsub.Publish(ctx, "etl-notifications", payload)
}

func (s *Service) toResponse(invite *InviteData) *InviteResponse {
    return &InviteResponse{
        ID:                     invite.ID,
        OrganizationIdentifier: invite.OrganizationIdentifier,
        Email:                  invite.Email,
        InviterID:              invite.InviterID,
        CustomRoleID:           invite.CustomRoleID,
        Status:                 invite.Status,
        Message:                invite.Message,
        RequireSSO:             invite.RequireSSO,
        RetryCount:             invite.RetryCount,
        Retried:                invite.Retried,
        Expired:                invite.Expired,
        Created:                invite.Created,
        Sent:                   invite.Sent,
        Updated:                invite.Updated,
    }
}
```

## Phase 3: API Handlers

### 3.1 Handler Structure

**File**: `shared/rest/onramp/invites/handler.go`

```go
package invites

import (
    "context"
    "encoding/json"
    "net/http"
    "time"
    "github.com/google/uuid"
    "github.com/gorilla/mux"
    "synapse-its.com/shared/api/response"
    "synapse-its.com/shared/connect"
    "synapse-its.com/shared/logger"
)

type HandlerDeps struct {
    GetConnections func(ctx context.Context) (*connect.Connections, error)
    CreateInvite   func(ctx context.Context, connections *connect.Connections, req *CreateInviteRequest, orgID uuid.UUID) (*InviteResponse, error)
    ListInvites    func(ctx context.Context, connections *connect.Connections, orgID uuid.UUID) ([]*InviteResponse, error)
    ListUserInvites func(ctx context.Context, connections *connect.Connections, userID uuid.UUID) ([]*InviteResponse, error)
    RevokeInvite   func(ctx context.Context, connections *connect.Connections, inviteID uuid.UUID, actor uuid.UUID) error
    RejectInvite   func(ctx context.Context, connections *connect.Connections, inviteID uuid.UUID, userID uuid.UUID) error
    ResendInvite   func(ctx context.Context, connections *connect.Connections, inviteID uuid.UUID, req *ResendInviteRequest) error
    ValidateInvite func(ctx context.Context, connections *connect.Connections, token string) (*InviteResponse, error)
    RedeemInvite   func(ctx context.Context, connections *connect.Connections, inviteID uuid.UUID, userID uuid.UUID) error
}

// CreateInviteHandler handles POST /api/organization/{orgid}/invites
func CreateInviteHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
    return func(w http.ResponseWriter, r *http.Request) {
        ctx := r.Context()

        // Get organization ID from URL
        vars := mux.Vars(r)
        orgID, err := uuid.Parse(vars["orgid"])
        if err != nil {
            logger.Errorf("invalid organization ID: %v", err)
            response.CreateBadRequestResponse(w)
            return
        }

        // Get connections
        connections, err := deps.GetConnections(ctx)
        if err != nil {
            logger.Errorf("failed to get connections: %v", err)
            response.CreateInternalErrorResponse(w)
            return
        }

        // Parse request
        var req CreateInviteRequest
        if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
            logger.Errorf("failed to parse request: %v", err)
            response.CreateBadRequestResponse(w)
            return
        }

        // Create invite
        invite, err := deps.CreateInvite(ctx, connections, &req, orgID)
        if err != nil {
            logger.Errorf("failed to create invite: %v", err)
            response.CreateInternalErrorResponse(w)
            return
        }

        response.CreateSuccessResponse(invite, w)
    }
}

// ListOrganizationInvitesHandler handles GET /api/organization/{orgid}/invites
func ListOrganizationInvitesHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
    return func(w http.ResponseWriter, r *http.Request) {
        ctx := r.Context()

        vars := mux.Vars(r)
        orgID, err := uuid.Parse(vars["orgid"])
        if err != nil {
            logger.Errorf("invalid organization ID: %v", err)
            response.CreateBadRequestResponse(w)
            return
        }

        connections, err := deps.GetConnections(ctx)
        if err != nil {
            logger.Errorf("failed to get connections: %v", err)
            response.CreateInternalErrorResponse(w)
            return
        }

        invites, err := deps.ListInvites(ctx, connections, orgID)
        if err != nil {
            logger.Errorf("failed to list invites: %v", err)
            response.CreateInternalErrorResponse(w)
            return
        }

        response.CreateSuccessResponse(invites, w)
    }
}

// ListUserInvitesHandler handles GET /api/user/{userid}/invites
func ListUserInvitesHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
    return func(w http.ResponseWriter, r *http.Request) {
        ctx := r.Context()

        vars := mux.Vars(r)
        userID, err := uuid.Parse(vars["userid"])
        if err != nil {
            logger.Errorf("invalid user ID: %v", err)
            response.CreateBadRequestResponse(w)
            return
        }

        connections, err := deps.GetConnections(ctx)
        if err != nil {
            logger.Errorf("failed to get connections: %v", err)
            response.CreateInternalErrorResponse(w)
            return
        }

        invites, err := deps.ListUserInvites(ctx, connections, userID)
        if err != nil {
            logger.Errorf("failed to list user invites: %v", err)
            response.CreateInternalErrorResponse(w)
            return
        }

        response.CreateSuccessResponse(invites, w)
    }
}

// RevokeInviteHandler handles DELETE /api/organization/{orgid}/invites/{id}
func RevokeInviteHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
    return func(w http.ResponseWriter, r *http.Request) {
        ctx := r.Context()

        vars := mux.Vars(r)
        inviteID, err := uuid.Parse(vars["id"])
        if err != nil {
            logger.Errorf("invalid invite ID: %v", err)
            response.CreateBadRequestResponse(w)
            return
        }

        var req RevokeInviteRequest
        if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
            logger.Errorf("failed to parse request: %v", err)
            response.CreateBadRequestResponse(w)
            return
        }

        connections, err := deps.GetConnections(ctx)
        if err != nil {
            logger.Errorf("failed to get connections: %v", err)
            response.CreateInternalErrorResponse(w)
            return
        }

        if err := deps.RevokeInvite(ctx, connections, inviteID, req.Actor); err != nil {
            logger.Errorf("failed to revoke invite: %v", err)
            response.CreateInternalErrorResponse(w)
            return
        }

        response.CreateSuccessResponse(nil, w)
    }
}

// RejectInviteHandler handles DELETE /api/user/{userid}/invites/{id}
func RejectInviteHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
    return func(w http.ResponseWriter, r *http.Request) {
        ctx := r.Context()

        vars := mux.Vars(r)
        inviteID, err := uuid.Parse(vars["id"])
        if err != nil {
            logger.Errorf("invalid invite ID: %v", err)
            response.CreateBadRequestResponse(w)
            return
        }

        userID, err := uuid.Parse(vars["userid"])
        if err != nil {
            logger.Errorf("invalid user ID: %v", err)
            response.CreateBadRequestResponse(w)
            return
        }

        connections, err := deps.GetConnections(ctx)
        if err != nil {
            logger.Errorf("failed to get connections: %v", err)
            response.CreateInternalErrorResponse(w)
            return
        }

        if err := deps.RejectInvite(ctx, connections, inviteID, userID); err != nil {
            logger.Errorf("failed to reject invite: %v", err)
            response.CreateInternalErrorResponse(w)
            return
        }

        response.CreateSuccessResponse(nil, w)
    }
}

// ResendInviteHandler handles POST /api/organization/{orgid}/invites/{id}/resend
func ResendInviteHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
    return func(w http.ResponseWriter, r *http.Request) {
        ctx := r.Context()

        vars := mux.Vars(r)
        inviteID, err := uuid.Parse(vars["id"])
        if err != nil {
            logger.Errorf("invalid invite ID: %v", err)
            response.CreateBadRequestResponse(w)
            return
        }

        var req ResendInviteRequest
        if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
            logger.Errorf("failed to parse request: %v", err)
            response.CreateBadRequestResponse(w)
            return
        }

        connections, err := deps.GetConnections(ctx)
        if err != nil {
            logger.Errorf("failed to get connections: %v", err)
            response.CreateInternalErrorResponse(w)
            return
        }

        if err := deps.ResendInvite(ctx, connections, inviteID, &req); err != nil {
            logger.Errorf("failed to resend invite: %v", err)
            response.CreateInternalErrorResponse(w)
            return
        }

        response.CreateSuccessResponse(nil, w)
    }
}

// ValidateInviteHandler handles GET /api/user/{userid}/invites/validate?token=
func ValidateInviteHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
    return func(w http.ResponseWriter, r *http.Request) {
        ctx := r.Context()

        token := r.URL.Query().Get("token")
        if token == "" {
            logger.Error("missing token parameter")
            response.CreateBadRequestResponse(w)
            return
        }

        connections, err := deps.GetConnections(ctx)
        if err != nil {
            logger.Errorf("failed to get connections: %v", err)
            response.CreateInternalErrorResponse(w)
            return
        }

        invite, err := deps.ValidateInvite(ctx, connections, token)
        if err != nil {
            logger.Errorf("failed to validate invite: %v", err)
            response.CreateUnauthorizedResponse(w)
            return
        }

        response.CreateSuccessResponse(invite, w)
    }
}

// RedeemInviteHandler handles POST /api/user/{userid}/invites/{id}/redeem
func RedeemInviteHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
    return func(w http.ResponseWriter, r *http.Request) {
        ctx := r.Context()

        vars := mux.Vars(r)
        inviteID, err := uuid.Parse(vars["id"])
        if err != nil {
            logger.Errorf("invalid invite ID: %v", err)
            response.CreateBadRequestResponse(w)
            return
        }

        userID, err := uuid.Parse(vars["userid"])
        if err != nil {
            logger.Errorf("invalid user ID: %v", err)
            response.CreateBadRequestResponse(w)
            return
        }

        connections, err := deps.GetConnections(ctx)
        if err != nil {
            logger.Errorf("failed to get connections: %v", err)
            response.CreateInternalErrorResponse(w)
            return
        }

        if err := deps.RedeemInvite(ctx, connections, inviteID, userID); err != nil {
            logger.Errorf("failed to redeem invite: %v", err)
            response.CreateInternalErrorResponse(w)
            return
        }

        response.CreateSuccessResponse(nil, w)
    }
}
```

## Phase 4: Route Registration

### 4.1 Update Organization Handler

**File**: `microservices/onramp/modules/organization/handler.go`

```go
package organization

import (
    "net/http"
    "github.com/gorilla/mux"
    RestOrganization "synapse-its.com/shared/rest/onramp/organization"
    RestPermissions "synapse-its.com/shared/rest/onramp/permissions"
    RestRoles "synapse-its.com/shared/rest/onramp/roles"
    RestInvites "synapse-its.com/shared/rest/onramp/invites"
)

type Handler struct{}

func NewHandler() *Handler {
    return &Handler{}
}

func (h *Handler) RegisterRoutes(router *mux.Router) {
    // Existing routes
    router.HandleFunc("/organizations", RestOrganization.CreateHandler).Methods(http.MethodPost)
    router.HandleFunc("/organizations", RestOrganization.GetAllHandler).Methods(http.MethodGet)
    router.HandleFunc("/organizations/{identifier}", RestOrganization.GetByIdentifierHandler).Methods(http.MethodGet)
    router.HandleFunc("/organizations/{identifier}", RestOrganization.UpdateHandler).Methods(http.MethodPatch)
    router.HandleFunc("/organizations/{identifier}", RestOrganization.DeleteHandler).Methods(http.MethodDelete)
    router.HandleFunc("/organizations/{organizationId}/roles", RestRoles.GetRolesHandler).Methods(http.MethodGet)
    router.HandleFunc("/organizations/{organizationId}/roles", RestRoles.CreateRoleHandler).Methods(http.MethodPost)
    router.HandleFunc("/organizations/{organizationId}/roles/{roleId}", RestRoles.DeleteRoleHandler).Methods(http.MethodDelete)
    router.HandleFunc("/organizations/{organizationId}/roles/{roleId}/role-templates", RestRoles.GetRoleTemplatesHandler).Methods(http.MethodGet)
    router.HandleFunc("/organizations/{organizationId}/roles/{roleId}/permissions", RestPermissions.GetPermissionsHandler).Methods(http.MethodGet)
    router.HandleFunc("/organizations/{organizationId}/permissions/{permissionId}/roles/{roleId}", RestPermissions.UpdatePermissionHandler).Methods(http.MethodPatch)
    
    // New invite routes
    router.HandleFunc("/organizations/{orgid}/invites", RestInvites.CreateInviteHandler).Methods(http.MethodPost)
    router.HandleFunc("/organizations/{orgid}/invites", RestInvites.ListOrganizationInvitesHandler).Methods(http.MethodGet)
    router.HandleFunc("/organizations/{orgid}/invites/{id}", RestInvites.RevokeInviteHandler).Methods(http.MethodDelete)
    router.HandleFunc("/organizations/{orgid}/invites/{id}/resend", RestInvites.ResendInviteHandler).Methods(http.MethodPost)
}

func (h *Handler) RegisterUserRoutes(router *mux.Router) {
    // User-specific invite routes
    router.HandleFunc("/user/{userid}/invites", RestInvites.ListUserInvitesHandler).Methods(http.MethodGet)
    router.HandleFunc("/user/{userid}/invites/{id}", RestInvites.RejectInviteHandler).Methods(http.MethodDelete)
    router.HandleFunc("/user/{userid}/invites/validate", RestInvites.ValidateInviteHandler).Methods(http.MethodGet)
    router.HandleFunc("/user/{userid}/invites/{id}/redeem", RestInvites.RedeemInviteHandler).Methods(http.MethodPost)
}
```

## Phase 5: Email Template System

### 5.1 Email Template Service

**File**: `shared/rest/onramp/invites/email.go`

```go
package invites

import (
    "bytes"
    "fmt"
    "html/template"
    "strings"
)

const emailTemplate = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>You're Invited to {{.AppName}}!</title>
</head>
<body style="margin:0;padding:0;background-color:#f4f4f4;">
  <table width="100%" cellpadding="0" cellspacing="0" role="presentation">
    <tr>
      <td align="center" style="padding:20px 0;">
        <table width="600" cellpadding="0" cellspacing="0" style="background-color:#ffffff;border-radius:8px;overflow:hidden;">
          <tr>
            <td style="padding:40px;text-align:center;font-family:Arial,sans-serif;">
              <h1 style="margin:0;color:#333333;font-size:24px;">You're Invited!</h1>
            </td>
          </tr>
          <tr>
            <td style="padding:0 40px 20px;font-family:Arial,sans-serif;color:#555555;font-size:16px;line-height:1.5;">
              <p>{{.Message}}</p>
              <p style="text-align:center;padding:20px 0;">
                <a href="{{.InviteLink}}" style="background-color:#007BFF;color:#ffffff;padding:14px 28px;text-decoration:none;border-radius:4px;display:inline-block;font-size:16px;">
                  Accept Invitation
                </a>
              </p>
              <p>If the button above doesn't work, copy and paste this link into your browser:</p>
              <p style="word-break:break-all;"><a href="{{.InviteLink}}" style="color:#007BFF;">{{.InviteLink}}</a></p>
            </td>
          </tr>
          <tr>
            <td style="padding:20px 40px;background-color:#f9f9f9;font-family:Arial,sans-serif;color:#999999;font-size:12px;">
              <p style="margin:0;">Sent by {{.AppName}} on behalf of {{.OrganizationName}}</p>
            </td>
          </tr>
        </table>
      </td>
    </tr>
  </table>
</body>
</html>`

type EmailData struct {
    AppName          string
    Message          string
    InviteLink       string
    OrganizationName string
}

func generateEmailTemplate(invite *InviteData, message *string, orgName string, baseURL string) (string, error) {
    // Default message if none provided
    defaultMessage := "You've been invited to join our organization!"
    if message != nil && *message != "" {
        defaultMessage = *message
    }

    // Generate invite link
    inviteLink := fmt.Sprintf("%s/api/organization/%s/invites/validate?token=%s", 
        baseURL, invite.OrganizationIdentifier, invite.TokenHash)

    data := EmailData{
        AppName:          "Onramp",
        Message:          defaultMessage,
        InviteLink:       inviteLink,
        OrganizationName: orgName,
    }

    tmpl, err := template.New("email").Parse(emailTemplate)
    if err != nil {
        return "", err
    }

    var buf bytes.Buffer
    if err := tmpl.Execute(&buf, data); err != nil {
        return "", err
    }

    return buf.String(), nil
}
```

## Phase 6: Testing Strategy

### 6.1 Unit Tests

**File**: `shared/rest/onramp/invites/handler_test.go`

```go
package invites

import (
    "bytes"
    "context"
    "encoding/json"
    "net/http"
    "net/http/httptest"
    "testing"
    "time"
    "github.com/google/uuid"
    "github.com/gorilla/mux"
    "github.com/stretchr/testify/assert"
    "github.com/stretchr/testify/mock"
)

// Mock dependencies
type MockService struct {
    mock.Mock
}

func (m *MockService) CreateInvite(ctx context.Context, connections *connect.Connections, req *CreateInviteRequest, orgID uuid.UUID) (*InviteResponse, error) {
    args := m.Called(ctx, connections, req, orgID)
    return args.Get(0).(*InviteResponse), args.Error(1)
}

// Test cases for each handler
func TestCreateInviteHandler(t *testing.T) {
    tests := []struct {
        name           string
        requestBody    interface{}
        expectedStatus int
        setupMock      func(*MockService)
    }{
        {
            name: "successful_create",
            requestBody: CreateInviteRequest{
                Email:            "<EMAIL>",
                InviterID:        uuid.New(),
                OrganizationRole: uuid.New(),
            },
            expectedStatus: http.StatusOK,
            setupMock: func(m *MockService) {
                m.On("CreateInvite", mock.Anything, mock.Anything, mock.Anything, mock.Anything).
                    Return(&InviteResponse{}, nil)
            },
        },
        // Add more test cases
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            // Test implementation
        })
    }
}
```

## Phase 7: Integration and Deployment

### 7.1 Environment Configuration

Add to environment variables:
```bash
# Email configuration
EMAIL_BASE_URL=https://onramp.example.com
EMAIL_FROM=<EMAIL>

# Invite configuration
INVITE_DEFAULT_EXPIRY_DAYS=7
INVITE_RESEND_COOLDOWN_MINUTES=1
```

### 7.2 Database Migration

**File**: `scripts/migrate_invites.sql`

```sql
-- Run the invite system schema updates
\i schemas/data-core-pg/1.0/updates/invite_system.sql

-- Insert test data if needed
INSERT INTO {{UserInvites}} (
    organizationidentifier,
    tokenhash,
    email,
    inviterid,
    customroleid,
    status,
    message
) VALUES (
    '550e8400-e29b-41d4-a716-446655440010',
    'test_token_hash',
    '<EMAIL>',
    '550e8400-e29b-41d4-a716-446655440005',
    '550e8400-e29b-41d4-a716-446655440020',
    'pending',
    'Test invite message'
);
```

## Implementation Timeline

### Week 1: Foundation
- [ ] Database schema implementation
- [ ] Shared data models and interfaces
- [ ] Basic repository implementation

### Week 2: Core Logic
- [ ] Service layer implementation
- [ ] Email template system
- [ ] BigQuery audit logging

### Week 3: API Layer
- [ ] Handler implementation
- [ ] Route registration
- [ ] Request/response validation

### Week 4: Testing & Integration
- [ ] Unit tests for all components
- [ ] Integration tests
- [ ] Email notification integration

### Week 5: Deployment & Documentation
- [ ] Database migrations
- [ ] Environment configuration
- [ ] API documentation
- [ ] Deployment scripts

## Key Considerations

1. **Security**: Token generation uses cryptographically secure random bytes
2. **Audit Trail**: All operations logged to BigQuery for compliance
3. **Rate Limiting**: Resend cooldown prevents spam
4. **Email Integration**: Uses existing PubSub infrastructure
5. **Error Handling**: Comprehensive error responses and logging
6. **Validation**: Input validation at multiple layers
7. **Testing**: Full test coverage for all components

This implementation follows the existing codebase patterns and integrates seamlessly with the current architecture. 