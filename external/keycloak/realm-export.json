{"accessCodeLifespan": 60, "accessCodeLifespanLogin": 1800, "accessCodeLifespanUserAction": 300, "accessTokenLifespan": 300, "accessTokenLifespanForImplicitFlow": 900, "actionTokenGeneratedByAdminLifespan": 43200, "actionTokenGeneratedByUserLifespan": 300, "adminEventsDetailsEnabled": false, "adminEventsEnabled": false, "adminPermissionsEnabled": false, "attributes": {"cibaAuthRequestedUserHint": "login_hint", "cibaBackchannelTokenDeliveryMode": "poll", "cibaExpiresIn": "120", "cibaInterval": "5", "oauth2DeviceCodeLifespan": "600", "oauth2DevicePollingInterval": "5", "parRequestUriLifespan": "60", "realmReusableOtpCode": "false"}, "authenticationFlows": [{"alias": "Account verification options", "authenticationExecutions": [{"authenticator": "idp-email-verification", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 10, "requirement": "ALTERNATIVE", "userSetupAllowed": false}, {"authenticatorFlow": true, "autheticatorFlow": true, "flowAlias": "Verify Existing Account by Re-authentication", "priority": 20, "requirement": "ALTERNATIVE", "userSetupAllowed": false}], "builtIn": true, "description": "Method with which to verity the existing account", "id": "4b190c55-14e8-4946-b743-5b88f8b700e7", "providerId": "basic-flow", "topLevel": false}, {"alias": "Browser - Conditional 2FA", "authenticationExecutions": [{"authenticator": "auth-otp-form", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 20, "requirement": "ALTERNATIVE", "userSetupAllowed": false}, {"authenticator": "auth-recovery-authn-code-form", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 40, "requirement": "DISABLED", "userSetupAllowed": false}, {"authenticator": "conditional-user-configured", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 10, "requirement": "REQUIRED", "userSetupAllowed": false}, {"authenticator": "webauthn-authenticator", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 30, "requirement": "DISABLED", "userSetupAllowed": false}], "builtIn": true, "description": "Flow to determine if any 2FA is required for the authentication", "id": "a112a5d2-f33f-4b90-a8dc-1c9f33bacd3d", "providerId": "basic-flow", "topLevel": false}, {"alias": "Browser - Conditional Organization", "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 10, "requirement": "REQUIRED", "userSetupAllowed": false}, {"authenticator": "organization", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 20, "requirement": "ALTERNATIVE", "userSetupAllowed": false}], "builtIn": true, "description": "Flow to determine if the organization identity-first login is to be used", "id": "99f2bb35-2201-4a86-a98d-812676d5b171", "providerId": "basic-flow", "topLevel": false}, {"alias": "Direct Grant - Conditional OTP", "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 10, "requirement": "REQUIRED", "userSetupAllowed": false}, {"authenticator": "direct-grant-validate-otp", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 20, "requirement": "REQUIRED", "userSetupAllowed": false}], "builtIn": true, "description": "Flow to determine if the OTP is required for the authentication", "id": "d7bc563d-4657-46de-ad19-270b098cd861", "providerId": "basic-flow", "topLevel": false}, {"alias": "First Broker Login - Conditional Organization", "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 10, "requirement": "REQUIRED", "userSetupAllowed": false}, {"authenticator": "idp-add-organization-member", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 20, "requirement": "REQUIRED", "userSetupAllowed": false}], "builtIn": true, "description": "Flow to determine if the authenticator that adds organization members is to be used", "id": "bb429dd0-2285-4b3c-9035-e1fd9fa0d0fe", "providerId": "basic-flow", "topLevel": false}, {"alias": "First broker login - Conditional 2FA", "authenticationExecutions": [{"authenticator": "auth-otp-form", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 20, "requirement": "ALTERNATIVE", "userSetupAllowed": false}, {"authenticator": "auth-recovery-authn-code-form", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 40, "requirement": "DISABLED", "userSetupAllowed": false}, {"authenticator": "conditional-user-configured", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 10, "requirement": "REQUIRED", "userSetupAllowed": false}, {"authenticator": "webauthn-authenticator", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 30, "requirement": "DISABLED", "userSetupAllowed": false}], "builtIn": true, "description": "Flow to determine if any 2FA is required for the authentication", "id": "beb8ccf6-faf6-4be4-a8fd-08043e4460b3", "providerId": "basic-flow", "topLevel": false}, {"alias": "<PERSON><PERSON> Existing Account", "authenticationExecutions": [{"authenticator": "idp-confirm-link", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 10, "requirement": "REQUIRED", "userSetupAllowed": false}, {"authenticatorFlow": true, "autheticatorFlow": true, "flowAlias": "Account verification options", "priority": 20, "requirement": "REQUIRED", "userSetupAllowed": false}], "builtIn": true, "description": "Handle what to do if there is existing account with same email/username like authenticated identity provider", "id": "0c2e9f16-0fc7-48fd-9e53-47bac2f01101", "providerId": "basic-flow", "topLevel": false}, {"alias": "Reset - Conditional OTP", "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 10, "requirement": "REQUIRED", "userSetupAllowed": false}, {"authenticator": "reset-otp", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 20, "requirement": "REQUIRED", "userSetupAllowed": false}], "builtIn": true, "description": "Flow to determine if the OTP should be reset or not. Set to REQUIRED to force.", "id": "19ab8a50-7d34-4f7d-a881-06f8759cc073", "providerId": "basic-flow", "topLevel": false}, {"alias": "User creation or linking", "authenticationExecutions": [{"authenticator": "idp-create-user-if-unique", "authenticatorConfig": "create unique user config", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 10, "requirement": "ALTERNATIVE", "userSetupAllowed": false}, {"authenticatorFlow": true, "autheticatorFlow": true, "flowAlias": "<PERSON><PERSON> Existing Account", "priority": 20, "requirement": "ALTERNATIVE", "userSetupAllowed": false}], "builtIn": true, "description": "Flow for the existing/non-existing user alternatives", "id": "d94b2235-95cc-4d4e-8dd5-385741f2b64b", "providerId": "basic-flow", "topLevel": false}, {"alias": "Verify Existing Account by Re-authentication", "authenticationExecutions": [{"authenticator": "idp-username-password-form", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 10, "requirement": "REQUIRED", "userSetupAllowed": false}, {"authenticatorFlow": true, "autheticatorFlow": true, "flowAlias": "First broker login - Conditional 2FA", "priority": 20, "requirement": "CONDITIONAL", "userSetupAllowed": false}], "builtIn": true, "description": "Reauthentication of existing account", "id": "23f9d77e-2f8f-43d2-a44c-1b01624c5116", "providerId": "basic-flow", "topLevel": false}, {"alias": "browser", "authenticationExecutions": [{"authenticator": "auth-cookie", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 10, "requirement": "ALTERNATIVE", "userSetupAllowed": false}, {"authenticator": "auth-spnego", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 20, "requirement": "DISABLED", "userSetupAllowed": false}, {"authenticator": "identity-provider-redirector", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 25, "requirement": "ALTERNATIVE", "userSetupAllowed": false}, {"authenticatorFlow": true, "autheticatorFlow": true, "flowAlias": "Organization", "priority": 26, "requirement": "ALTERNATIVE", "userSetupAllowed": false}, {"authenticatorFlow": true, "autheticatorFlow": true, "flowAlias": "forms", "priority": 30, "requirement": "ALTERNATIVE", "userSetupAllowed": false}], "builtIn": true, "description": "Browser based authentication", "id": "dbecf723-f58a-4baf-97be-1be231f5e5ad", "providerId": "basic-flow", "topLevel": true}, {"alias": "clients", "authenticationExecutions": [{"authenticator": "client-jwt", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 20, "requirement": "ALTERNATIVE", "userSetupAllowed": false}, {"authenticator": "client-secret", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 10, "requirement": "ALTERNATIVE", "userSetupAllowed": false}, {"authenticator": "client-secret-jwt", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 30, "requirement": "ALTERNATIVE", "userSetupAllowed": false}, {"authenticator": "client-x509", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 40, "requirement": "ALTERNATIVE", "userSetupAllowed": false}], "builtIn": true, "description": "Base authentication for clients", "id": "a2fd98bc-954f-4b79-9fb9-9e4db8428b07", "providerId": "client-flow", "topLevel": true}, {"alias": "direct grant", "authenticationExecutions": [{"authenticator": "direct-grant-validate-password", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 20, "requirement": "REQUIRED", "userSetupAllowed": false}, {"authenticator": "direct-grant-validate-username", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 10, "requirement": "REQUIRED", "userSetupAllowed": false}, {"authenticatorFlow": true, "autheticatorFlow": true, "flowAlias": "Direct Grant - Conditional OTP", "priority": 30, "requirement": "CONDITIONAL", "userSetupAllowed": false}], "builtIn": true, "description": "OpenID Connect Resource Owner Grant", "id": "4a4af77c-2505-4ced-9db0-e234e95c3948", "providerId": "basic-flow", "topLevel": true}, {"alias": "docker auth", "authenticationExecutions": [{"authenticator": "docker-http-basic-authenticator", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 10, "requirement": "REQUIRED", "userSetupAllowed": false}], "builtIn": true, "description": "Used by Docker clients to authenticate against the IDP", "id": "03f54f7b-af6c-4326-be80-ab32ab7fb80e", "providerId": "basic-flow", "topLevel": true}, {"alias": "first broker login", "authenticationExecutions": [{"authenticator": "idp-review-profile", "authenticatorConfig": "review profile config", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 10, "requirement": "REQUIRED", "userSetupAllowed": false}, {"authenticatorFlow": true, "autheticatorFlow": true, "flowAlias": "First Broker Login - Conditional Organization", "priority": 50, "requirement": "CONDITIONAL", "userSetupAllowed": false}, {"authenticatorFlow": true, "autheticatorFlow": true, "flowAlias": "User creation or linking", "priority": 20, "requirement": "REQUIRED", "userSetupAllowed": false}], "builtIn": true, "description": "Actions taken after first broker login with identity provider account, which is not yet linked to any Keycloak account", "id": "b874ded5-5f16-4db0-8d84-8dc6e4e47572", "providerId": "basic-flow", "topLevel": true}, {"alias": "forms", "authenticationExecutions": [{"authenticator": "auth-username-password-form", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 10, "requirement": "REQUIRED", "userSetupAllowed": false}, {"authenticatorFlow": true, "autheticatorFlow": true, "flowAlias": "Browser - Conditional 2FA", "priority": 20, "requirement": "CONDITIONAL", "userSetupAllowed": false}], "builtIn": true, "description": "Username, password, otp and other auth forms.", "id": "7d3b2c33-05f8-489a-807b-168420f27c86", "providerId": "basic-flow", "topLevel": false}, {"alias": "registration", "authenticationExecutions": [{"authenticator": "registration-page-form", "authenticatorFlow": true, "autheticatorFlow": true, "flowAlias": "registration form", "priority": 10, "requirement": "REQUIRED", "userSetupAllowed": false}], "builtIn": true, "description": "Registration flow", "id": "64e6c428-a216-4ddd-aaa3-70099c524475", "providerId": "basic-flow", "topLevel": true}, {"alias": "registration form", "authenticationExecutions": [{"authenticator": "registration-password-action", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 50, "requirement": "REQUIRED", "userSetupAllowed": false}, {"authenticator": "registration-recaptcha-action", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 60, "requirement": "DISABLED", "userSetupAllowed": false}, {"authenticator": "registration-terms-and-conditions", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 70, "requirement": "DISABLED", "userSetupAllowed": false}, {"authenticator": "registration-user-creation", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 20, "requirement": "REQUIRED", "userSetupAllowed": false}], "builtIn": true, "description": "Registration form", "id": "34d69b29-46f1-4b37-be17-e245c347ab19", "providerId": "form-flow", "topLevel": false}, {"alias": "reset credentials", "authenticationExecutions": [{"authenticator": "reset-credential-email", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 20, "requirement": "REQUIRED", "userSetupAllowed": false}, {"authenticator": "reset-credentials-choose-user", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 10, "requirement": "REQUIRED", "userSetupAllowed": false}, {"authenticator": "reset-password", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 30, "requirement": "REQUIRED", "userSetupAllowed": false}, {"authenticatorFlow": true, "autheticatorFlow": true, "flowAlias": "Reset - Conditional OTP", "priority": 40, "requirement": "CONDITIONAL", "userSetupAllowed": false}], "builtIn": true, "description": "Reset credentials for a user if they forgot their password or something", "id": "130e15fa-16d4-499a-b3f2-b67556ebc2f2", "providerId": "basic-flow", "topLevel": true}, {"alias": "saml ecp", "authenticationExecutions": [{"authenticator": "http-basic-authenticator", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 10, "requirement": "REQUIRED", "userSetupAllowed": false}], "builtIn": true, "description": "SAML ECP Profile Authentication Flow", "id": "2df9aab7-0c11-4f19-ad00-3ef0c0ab1247", "providerId": "basic-flow", "topLevel": true}, {"alias": "Organization", "authenticationExecutions": [{"authenticatorFlow": true, "autheticatorFlow": true, "flowAlias": "Browser - Conditional Organization", "priority": 10, "requirement": "CONDITIONAL", "userSetupAllowed": false}], "builtIn": true, "id": "f5445231-733a-4e34-a110-e73105f30f4f", "providerId": "basic-flow", "topLevel": false}], "authenticatorConfig": [{"alias": "create unique user config", "config": {"require.password.update.after.registration": "false"}, "id": "58ec90c3-daab-41b9-8120-247811c7a5c9"}, {"alias": "review profile config", "config": {"update.profile.on.first.login": "missing"}, "id": "d80ce8ec-b96e-4eac-b1b6-3564e465ec6e"}], "browserFlow": "browser", "browserSecurityHeaders": {"contentSecurityPolicy": "frame-src 'self'; frame-ancestors 'self'; object-src 'none';", "contentSecurityPolicyReportOnly": "", "referrerPolicy": "no-referrer", "strictTransportSecurity": "max-age=********; includeSubDomains", "xContentTypeOptions": "nosniff", "xFrameOptions": "SAMEORIGIN", "xRobotsTag": "none"}, "bruteForceProtected": false, "bruteForceStrategy": "MULTIPLE", "clientAuthenticationFlow": "clients", "clientOfflineSessionIdleTimeout": 0, "clientOfflineSessionMaxLifespan": 0, "clientPolicies": {"policies": []}, "clientProfiles": {"profiles": []}, "clientScopeMappings": {"account": [{"client": "account-console", "roles": ["manage-account", "view-groups"]}]}, "clientScopes": [{"attributes": {"consent.screen.text": "${offlineAccessScopeConsentText}", "display.on.consent.screen": "true"}, "description": "OpenID Connect built-in scope: offline_access", "id": "d357b879-e41e-41b2-81a4-95cecf86214b", "name": "offline_access", "protocol": "openid-connect"}, {"attributes": {"consent.screen.text": "${samlRoleListScopeConsentText}", "display.on.consent.screen": "true"}, "description": "SAML role list", "id": "813d100d-0bbe-404a-aa3d-d87fdb77ceab", "name": "role_list", "protocol": "saml", "protocolMappers": [{"config": {"attribute.name": "Role", "attribute.nameformat": "Basic", "single": "false"}, "consentRequired": false, "id": "1563eb51-57da-4ec8-9d4f-5fd068d35344", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper"}]}, {"attributes": {"consent.screen.text": "", "display.on.consent.screen": "false", "include.in.token.scope": "false"}, "description": "OpenID Connect scope for add allowed web origins to the access token", "id": "89629d09-5288-4165-84ef-c1369932a1b3", "name": "web-origins", "protocol": "openid-connect", "protocolMappers": [{"config": {"access.token.claim": "true", "introspection.token.claim": "true"}, "consentRequired": false, "id": "ddb2af60-1b5b-4cb5-a6cd-68a3c362690b", "name": "allowed web origins", "protocol": "openid-connect", "protocolMapper": "oidc-allowed-origins-mapper"}]}, {"attributes": {"consent.screen.text": "${addressScopeConsentText}", "display.on.consent.screen": "true", "include.in.token.scope": "true"}, "description": "OpenID Connect built-in scope: address", "id": "295c2157-71a7-48d2-b0c3-a4df5e74ebf2", "name": "address", "protocol": "openid-connect", "protocolMappers": [{"config": {"access.token.claim": "true", "id.token.claim": "true", "introspection.token.claim": "true", "user.attribute.country": "country", "user.attribute.formatted": "formatted", "user.attribute.locality": "locality", "user.attribute.postal_code": "postal_code", "user.attribute.region": "region", "user.attribute.street": "street", "userinfo.token.claim": "true"}, "consentRequired": false, "id": "eb9fb930-21e1-433f-ac2b-b645b84b7fb7", "name": "address", "protocol": "openid-connect", "protocolMapper": "oidc-address-mapper"}]}, {"attributes": {"consent.screen.text": "${emailScopeConsentText}", "display.on.consent.screen": "true", "include.in.token.scope": "true"}, "description": "OpenID Connect built-in scope: email", "id": "349c46ae-692c-4af5-91d9-e9f3817e5770", "name": "email", "protocol": "openid-connect", "protocolMappers": [{"config": {"access.token.claim": "true", "claim.name": "email", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "String", "user.attribute": "email", "userinfo.token.claim": "true"}, "consentRequired": false, "id": "82c14599-50ca-45bd-b9a3-cd389a76802c", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper"}, {"config": {"access.token.claim": "true", "claim.name": "email_verified", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "boolean", "user.attribute": "emailVerified", "userinfo.token.claim": "true"}, "consentRequired": false, "id": "faef4d84-3040-4efa-b5a4-c9feed876b19", "name": "email verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper"}]}, {"attributes": {"consent.screen.text": "${organizationScopeConsentText}", "display.on.consent.screen": "true", "include.in.token.scope": "true"}, "description": "Additional claims about the organization a subject belongs to", "id": "c2a1dee3-eae6-4180-b12e-b849b9991c4a", "name": "organization", "protocol": "openid-connect", "protocolMappers": [{"config": {"access.token.claim": "true", "claim.name": "organization", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "String", "multivalued": "true"}, "consentRequired": false, "id": "1f4c1881-2947-4df0-8131-2fbf11db2601", "name": "organization", "protocol": "openid-connect", "protocolMapper": "oidc-organization-membership-mapper"}]}, {"attributes": {"consent.screen.text": "${phoneScopeConsentText}", "display.on.consent.screen": "true", "include.in.token.scope": "true"}, "description": "OpenID Connect built-in scope: phone", "id": "afdbd6b6-4f7f-49df-beaa-a9c8e0c057d0", "name": "phone", "protocol": "openid-connect", "protocolMappers": [{"config": {"access.token.claim": "true", "claim.name": "phone_number", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "String", "user.attribute": "phoneNumber", "userinfo.token.claim": "true"}, "consentRequired": false, "id": "ecc2dcef-ce5c-4a23-9006-15e50880ea31", "name": "phone number", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper"}, {"config": {"access.token.claim": "true", "claim.name": "phone_number_verified", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "boolean", "user.attribute": "phoneNumberVerified", "userinfo.token.claim": "true"}, "consentRequired": false, "id": "5eaa0208-9d36-43b3-a561-88214d1641a8", "name": "phone number verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper"}]}, {"attributes": {"consent.screen.text": "${profileScopeConsentText}", "display.on.consent.screen": "true", "include.in.token.scope": "true"}, "description": "OpenID Connect built-in scope: profile", "id": "ed0a90ef-3da8-48b5-bbd0-258253b4a814", "name": "profile", "protocol": "openid-connect", "protocolMappers": [{"config": {"access.token.claim": "true", "claim.name": "birthdate", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "String", "user.attribute": "birthdate", "userinfo.token.claim": "true"}, "consentRequired": false, "id": "b79fbce8-b913-44e1-9e57-6726cb2e865f", "name": "birthdate", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper"}, {"config": {"access.token.claim": "true", "claim.name": "family_name", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "String", "user.attribute": "lastName", "userinfo.token.claim": "true"}, "consentRequired": false, "id": "b6c19fbf-bf11-4a3d-9232-1e82d620077d", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper"}, {"config": {"access.token.claim": "true", "claim.name": "gender", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "String", "user.attribute": "gender", "userinfo.token.claim": "true"}, "consentRequired": false, "id": "5c245037-3aab-410d-928e-b547930f548e", "name": "gender", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper"}, {"config": {"access.token.claim": "true", "claim.name": "given_name", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "String", "user.attribute": "firstName", "userinfo.token.claim": "true"}, "consentRequired": false, "id": "0a9622e2-ead0-447c-9dfe-1f34cd7cd931", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper"}, {"config": {"access.token.claim": "true", "claim.name": "locale", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "String", "user.attribute": "locale", "userinfo.token.claim": "true"}, "consentRequired": false, "id": "39c3bbf3-9fba-4cec-aaaf-44f896949d6c", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper"}, {"config": {"access.token.claim": "true", "claim.name": "middle_name", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "String", "user.attribute": "middleName", "userinfo.token.claim": "true"}, "consentRequired": false, "id": "f87ed405-df55-4c56-bda5-57a0f0db7464", "name": "middle name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper"}, {"config": {"access.token.claim": "true", "claim.name": "nickname", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "String", "user.attribute": "nickname", "userinfo.token.claim": "true"}, "consentRequired": false, "id": "823db02d-9111-4a14-a018-bb590dc5101b", "name": "nickname", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper"}, {"config": {"access.token.claim": "true", "claim.name": "picture", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "String", "user.attribute": "picture", "userinfo.token.claim": "true"}, "consentRequired": false, "id": "32e294b8-b409-4db6-a1dd-ebbfad2ad358", "name": "picture", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper"}, {"config": {"access.token.claim": "true", "claim.name": "preferred_username", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "String", "user.attribute": "username", "userinfo.token.claim": "true"}, "consentRequired": false, "id": "17e10fc5-8169-4fee-87d1-beffde4ce5fc", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper"}, {"config": {"access.token.claim": "true", "claim.name": "profile", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "String", "user.attribute": "profile", "userinfo.token.claim": "true"}, "consentRequired": false, "id": "4325fc37-1523-42e5-9f3a-3252030c0da3", "name": "profile", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper"}, {"config": {"access.token.claim": "true", "claim.name": "updated_at", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "long", "user.attribute": "updatedAt", "userinfo.token.claim": "true"}, "consentRequired": false, "id": "64d65718-ef09-4df0-ac87-fc5646c82fcc", "name": "updated at", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper"}, {"config": {"access.token.claim": "true", "claim.name": "website", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "String", "user.attribute": "website", "userinfo.token.claim": "true"}, "consentRequired": false, "id": "6f871929-81fc-45bc-9af1-14345ad28012", "name": "website", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper"}, {"config": {"access.token.claim": "true", "claim.name": "zoneinfo", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "String", "user.attribute": "zoneinfo", "userinfo.token.claim": "true"}, "consentRequired": false, "id": "c5cf6555-8d80-4901-b061-9e67ceb4d9bf", "name": "zoneinfo", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper"}, {"config": {"access.token.claim": "true", "id.token.claim": "true", "introspection.token.claim": "true", "userinfo.token.claim": "true"}, "consentRequired": false, "id": "1ec189c9-f245-47e0-bf92-889b420a5725", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper"}]}, {"attributes": {"consent.screen.text": "${rolesScopeConsentText}", "display.on.consent.screen": "true", "include.in.token.scope": "false"}, "description": "OpenID Connect scope for add user roles to the access token", "id": "ec6eeba3-277b-4071-9e68-859c53ddc238", "name": "roles", "protocol": "openid-connect", "protocolMappers": [{"config": {"access.token.claim": "true", "claim.name": "realm_access.roles", "introspection.token.claim": "true", "jsonType.label": "String", "multivalued": "true", "user.attribute": "foo"}, "consentRequired": false, "id": "e526b29a-0edc-425f-ba07-f52905bf3211", "name": "realm roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper"}, {"config": {"access.token.claim": "true", "claim.name": "resource_access.${client_id}.roles", "introspection.token.claim": "true", "jsonType.label": "String", "multivalued": "true", "user.attribute": "foo"}, "consentRequired": false, "id": "2be453c1-367f-408c-97fd-63d0546c45a6", "name": "client roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-client-role-mapper"}, {"config": {"access.token.claim": "true", "introspection.token.claim": "true"}, "consentRequired": false, "id": "691479b0-9b2a-496c-9ed8-2e5855af0acf", "name": "audience resolve", "protocol": "openid-connect", "protocolMapper": "oidc-audience-resolve-mapper"}]}, {"attributes": {"display.on.consent.screen": "false"}, "description": "Organization Membership", "id": "13e4fec8-4877-4521-b5cc-e5ee38aa107b", "name": "saml_organization", "protocol": "saml", "protocolMappers": [{"config": {}, "consentRequired": false, "id": "f3306e4a-dbe3-4e93-b826-342f9fc38438", "name": "organization", "protocol": "saml", "protocolMapper": "saml-organization-membership-mapper"}]}, {"attributes": {"display.on.consent.screen": "false", "include.in.token.scope": "false"}, "description": "OpenID Connect scope for add acr (authentication context class reference) to the token", "id": "9b656d45-b30c-499c-a418-aa05bba3c561", "name": "acr", "protocol": "openid-connect", "protocolMappers": [{"config": {"access.token.claim": "true", "id.token.claim": "true", "introspection.token.claim": "true"}, "consentRequired": false, "id": "8be79691-700c-49c2-887b-b4d4b642b19c", "name": "acr loa level", "protocol": "openid-connect", "protocolMapper": "oidc-acr-mapper"}]}, {"attributes": {"display.on.consent.screen": "false", "include.in.token.scope": "false"}, "description": "OpenID Connect scope for add all basic claims to the token", "id": "b59f10b9-bc57-40d7-a984-aee8c6107c61", "name": "basic", "protocol": "openid-connect", "protocolMappers": [{"config": {"access.token.claim": "true", "claim.name": "auth_time", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "long", "user.session.note": "AUTH_TIME"}, "consentRequired": false, "id": "0caabe85-5579-4f2c-943c-55ed288b4425", "name": "auth_time", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper"}, {"config": {"access.token.claim": "true", "introspection.token.claim": "true"}, "consentRequired": false, "id": "0149fee4-e9d2-4147-869d-5f1ca05d89b9", "name": "sub", "protocol": "openid-connect", "protocolMapper": "oidc-sub-mapper"}]}, {"attributes": {"display.on.consent.screen": "false", "include.in.token.scope": "false"}, "description": "Specific scope for a client enabled for service accounts", "id": "8452b0e1-de0d-4a22-ad93-0e3e29b3c059", "name": "service_account", "protocol": "openid-connect", "protocolMappers": [{"config": {"access.token.claim": "true", "claim.name": "clientAddress", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "String", "user.session.note": "clientAddress"}, "consentRequired": false, "id": "0eb1e317-b410-492f-a2d2-5754ecf4245f", "name": "Client IP Address", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper"}, {"config": {"access.token.claim": "true", "claim.name": "clientHost", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "String", "user.session.note": "clientHost"}, "consentRequired": false, "id": "96be7109-b36a-40c6-9c85-86932e69bc37", "name": "Client Host", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper"}, {"config": {"access.token.claim": "true", "claim.name": "client_id", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "String", "user.session.note": "client_id"}, "consentRequired": false, "id": "ae20c0d7-e69d-4951-a815-31fb6c5106b3", "name": "Client ID", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper"}]}, {"attributes": {"display.on.consent.screen": "false", "include.in.token.scope": "true"}, "description": "Microprofile - JWT built-in scope", "id": "e88491a8-37ba-494a-94de-694c6ee7a39c", "name": "microprofile-jwt", "protocol": "openid-connect", "protocolMappers": [{"config": {"access.token.claim": "true", "claim.name": "groups", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "String", "multivalued": "true", "user.attribute": "foo"}, "consentRequired": false, "id": "b77739ad-9f12-43bb-967e-0109470f68e6", "name": "groups", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper"}, {"config": {"access.token.claim": "true", "claim.name": "upn", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "String", "user.attribute": "username", "userinfo.token.claim": "true"}, "consentRequired": false, "id": "7399de4e-575c-4b56-ac7f-521537a7f561", "name": "upn", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper"}]}], "clientSessionIdleTimeout": 0, "clientSessionMaxLifespan": 0, "clients": [{"alwaysDisplayInConsole": false, "attributes": {"client.use.lightweight.access.token.enabled": "true", "pkce.code.challenge.method": "S256", "post.logout.redirect.uris": "+", "realm_client": "false"}, "authenticationFlowBindingOverrides": {}, "baseUrl": "/admin/onramp-dev/console/", "bearerOnly": false, "clientAuthenticatorType": "client-secret", "clientId": "security-admin-console", "consentRequired": false, "defaultClientScopes": ["acr", "basic", "email", "profile", "roles", "web-origins"], "directAccessGrantsEnabled": false, "enabled": true, "frontchannelLogout": false, "fullScopeAllowed": true, "id": "d97d6c26-e5eb-428f-90d1-a18a0aa12ca3", "implicitFlowEnabled": false, "name": "${client_security-admin-console}", "nodeReRegistrationTimeout": 0, "notBefore": 0, "optionalClientScopes": ["address", "microprofile-jwt", "offline_access", "organization", "phone"], "protocol": "openid-connect", "protocolMappers": [{"config": {"access.token.claim": "true", "claim.name": "locale", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "String", "user.attribute": "locale", "userinfo.token.claim": "true"}, "consentRequired": false, "id": "6b1e10ff-700a-450a-a70b-1411b6b0cef0", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper"}], "publicClient": true, "redirectUris": ["/admin/onramp-dev/console/*"], "rootUrl": "${authAdminUrl}", "serviceAccountsEnabled": false, "standardFlowEnabled": true, "surrogateAuthRequired": false, "webOrigins": ["+"]}, {"alwaysDisplayInConsole": false, "attributes": {"pkce.code.challenge.method": "S256", "post.logout.redirect.uris": "+", "realm_client": "false"}, "authenticationFlowBindingOverrides": {}, "baseUrl": "/realms/onramp-dev/account/", "bearerOnly": false, "clientAuthenticatorType": "client-secret", "clientId": "account-console", "consentRequired": false, "defaultClientScopes": ["acr", "basic", "email", "profile", "roles", "web-origins"], "directAccessGrantsEnabled": false, "enabled": true, "frontchannelLogout": false, "fullScopeAllowed": false, "id": "eb84688a-d2b8-4205-b341-9d64aea39701", "implicitFlowEnabled": false, "name": "${client_account-console}", "nodeReRegistrationTimeout": 0, "notBefore": 0, "optionalClientScopes": ["address", "microprofile-jwt", "offline_access", "organization", "phone"], "protocol": "openid-connect", "protocolMappers": [{"config": {}, "consentRequired": false, "id": "afd0d12d-317a-4750-8190-c9d34defa37c", "name": "audience resolve", "protocol": "openid-connect", "protocolMapper": "oidc-audience-resolve-mapper"}], "publicClient": true, "redirectUris": ["/realms/onramp-dev/account/*"], "rootUrl": "${authBaseUrl}", "serviceAccountsEnabled": false, "standardFlowEnabled": true, "surrogateAuthRequired": false, "webOrigins": []}, {"alwaysDisplayInConsole": false, "attributes": {"post.logout.redirect.uris": "+", "realm_client": "false"}, "authenticationFlowBindingOverrides": {}, "baseUrl": "/realms/onramp-dev/account/", "bearerOnly": false, "clientAuthenticatorType": "client-secret", "clientId": "account", "consentRequired": false, "defaultClientScopes": ["acr", "basic", "email", "profile", "roles", "web-origins"], "directAccessGrantsEnabled": false, "enabled": true, "frontchannelLogout": false, "fullScopeAllowed": false, "id": "5b2bbcfc-c78b-42af-a512-065623d8bd89", "implicitFlowEnabled": false, "name": "${client_account}", "nodeReRegistrationTimeout": 0, "notBefore": 0, "optionalClientScopes": ["address", "microprofile-jwt", "offline_access", "organization", "phone"], "protocol": "openid-connect", "publicClient": true, "redirectUris": ["/realms/onramp-dev/account/*"], "rootUrl": "${authBaseUrl}", "serviceAccountsEnabled": false, "standardFlowEnabled": true, "surrogateAuthRequired": false, "webOrigins": []}, {"alwaysDisplayInConsole": false, "attributes": {"client.use.lightweight.access.token.enabled": "true", "realm_client": "false"}, "authenticationFlowBindingOverrides": {}, "bearerOnly": false, "clientAuthenticatorType": "client-secret", "clientId": "admin-cli", "consentRequired": false, "defaultClientScopes": ["acr", "basic", "email", "profile", "roles", "web-origins"], "directAccessGrantsEnabled": true, "enabled": true, "frontchannelLogout": false, "fullScopeAllowed": true, "id": "ab42252b-c51e-4b63-9c9a-6d59586ff4fa", "implicitFlowEnabled": false, "name": "${client_admin-cli}", "nodeReRegistrationTimeout": 0, "notBefore": 0, "optionalClientScopes": ["address", "microprofile-jwt", "offline_access", "organization", "phone"], "protocol": "openid-connect", "publicClient": true, "redirectUris": [], "serviceAccountsEnabled": false, "standardFlowEnabled": false, "surrogateAuthRequired": false, "webOrigins": []}, {"alwaysDisplayInConsole": false, "attributes": {"realm_client": "true"}, "authenticationFlowBindingOverrides": {}, "bearerOnly": true, "clientAuthenticatorType": "client-secret", "clientId": "broker", "consentRequired": false, "defaultClientScopes": ["acr", "basic", "email", "profile", "roles", "web-origins"], "directAccessGrantsEnabled": false, "enabled": true, "frontchannelLogout": false, "fullScopeAllowed": false, "id": "4f29cc14-9c67-4125-8bd5-720c5ee208c6", "implicitFlowEnabled": false, "name": "${client_broker}", "nodeReRegistrationTimeout": 0, "notBefore": 0, "optionalClientScopes": ["address", "microprofile-jwt", "offline_access", "organization", "phone"], "protocol": "openid-connect", "publicClient": false, "redirectUris": [], "serviceAccountsEnabled": false, "standardFlowEnabled": true, "surrogateAuthRequired": false, "webOrigins": []}, {"alwaysDisplayInConsole": false, "attributes": {"realm_client": "true"}, "authenticationFlowBindingOverrides": {}, "bearerOnly": true, "clientAuthenticatorType": "client-secret", "clientId": "realm-management", "consentRequired": false, "defaultClientScopes": ["acr", "basic", "email", "profile", "roles", "web-origins"], "directAccessGrantsEnabled": false, "enabled": true, "frontchannelLogout": false, "fullScopeAllowed": false, "id": "********-ae40-43d9-8342-c6e615679288", "implicitFlowEnabled": false, "name": "${client_realm-management}", "nodeReRegistrationTimeout": 0, "notBefore": 0, "optionalClientScopes": ["address", "microprofile-jwt", "offline_access", "organization", "phone"], "protocol": "openid-connect", "publicClient": false, "redirectUris": [], "serviceAccountsEnabled": false, "standardFlowEnabled": true, "surrogateAuthRequired": false, "webOrigins": []}], "components": {"org.keycloak.keys.KeyProvider": [{"config": {"algorithm": ["RSA-OAEP"], "certificate": ["MIICozCCAYsCBgGYWkW+BTANBgkqhkiG9w0BAQsFADAVMRMwEQYDVQQDDApvbnJhbXAtZGV2MB4XDTI1MDczMDA3Mzc1NloXDTM1MDczMDA3MzkzNlowFTETMBEGA1UEAwwKb25yYW1wLWRldjCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAL3tZ6OLIitXdCW4YlKN5cYGsISpb+vKFjgiswnQJtkradYQoejJ2DMyU9yYVu6FEJagnR/TgArpOZ01fqRymNDb/rfq3eGoArGJUgmLjFjWJlYVTqLWH/Wm6Z2vSjhlkud4dqjkOFOLNhdvdoSSEOUKrlk0rJ+aClvj8+CvjFejrh2wFABeMv7X+qiu9Kacj2Ecs+WIwVCgnVj4yg3/gRJ1n+zQaD+7L9jofrDlXkmcpJW9TFebwf2styRLHmioZPTvuZXTOd0Q8URFtOp+g+FgfRfMEf08a99oRU4VtL6jkWKlYzNBcUZELukxoKLORoASaqTfkY5XRAKTauW9v+8CAwEAATANBgkqhkiG9w0BAQsFAAOCAQEAPwDASbhEjbVrAdtadH78HwjrunmNjoICJnWJmFLw3xKNakcQArn7XwNfMgcocbymHN1N0Ti24sla/MfXfy63XWFi5J5cO5cXC1twqwNSoKNYtRyKuRGUR+63LKgn0bh12f4Yda1USeb5PIjwKqeB5CBqZQZhNllfSgF5xCDzT5RKfQRpfTi8jWtaHKI+dJC1wPa8UQB42qVl3IXVB7o5i/G3Y6qTiYC/qNE2VjVHDS6uRk1IFCAMmIaP8ytR+EuA1MUj4V8oPs4AmdFZDlqmrobs/8mEZOMvQRQ64Q6LzpxH2H1FnCkrWxar3DJ7oiBfZSt+e2jZ8YUe4yPvK/bF3A=="], "keyUse": ["ENC"], "priority": ["100"], "privateKey": ["MIIEpAIBAAKCAQEAve1no4siK1d0JbhiUo3lxgawhKlv68oWOCKzCdAm2Stp1hCh6MnYMzJT3JhW7oUQlqCdH9OACuk5nTV+pHKY0Nv+t+rd4agCsYlSCYuMWNYmVhVOotYf9abpna9KOGWS53h2qOQ4U4s2F292hJIQ5QquWTSsn5oKW+Pz4K+MV6OuHbAUAF4y/tf6qK70ppyPYRyz5YjBUKCdWPjKDf+BEnWf7NBoP7sv2Oh+sOVeSZyklb1MV5vB/ay3JEseaKhk9O+5ldM53RDxREW06n6D4WB9F8wR/Txr32hFThW0vqORYqVjM0FxRkQu6TGgos5GgBJqpN+RjldEApNq5b2/7wIDAQABAoIBABEq+IdL+Fa6nQEb3adgtDhmOmaDel5x8fg5Cr4EVr6isfprnncQZ76Y+gOisZj15j33XryVvak+07pmL4Bl63JVsHynek7/hN7Pbt72AkW1ddmqK2WEvXbTE6LxSWA3rkPGG9PAbWb/2m/b33a0TVqm0IO3wzLaFb2gqVbSB3mUZhggqylRVuG4ePhMu/pbFO8x/PAWQ5jVVtydoTqtOwPTd+zBptksHv+QnpqlUX3aIEC4u005BGSBUCb+SxXBaol976g2iUFbYN2PtRr+GfrOp4g9rNZ01AkXdjDHWClvEM9W4JV+merM2/TiS4DhWmhj+DWMD56VYizRTpOB3yECgYEA7oS2wCf0yy9QAnOQmxjtux22L8qQqUJgB+usN0Q77MmJDMYDcK0aKjis/qaAMzXtWkaNJ28IqckjFpyeapzSAD3IZDRDo8kR2+6vX01NhIc1mXKnRJ2DsgrWDKrJZz23Pc/Z6P0nQ/30tI3z5W8syTEenkwymaMF42iCbomJJgUCgYEAy9j783qHeU9MvInq06EVXRElV+/2YQo8csVvPN2JQOwqd9SgNuOR+eKgLnv3DY3pGytdQjWnmDnSi0Fjqs48XazvR0lwaJ8eRjZGiFErQE6PC3Mi2q2blBCS8ZVnOkhuvGSn31DuH7020DDLSKI4xPfnJ2BFCA2sRFTF/g21nGMCgYA1dbeEUDBUzjDvq7lQmgDGvL0V7yAHX+IoRFFgPcH0q6M1Irioq5O+vN9BYE5BrCeb4U5H9WIKtBNSQR5cpPOIrFBa7T/v2ORQ2Cmkga4LM0b3MrR885UN9dHOGwVzR2jnyMt9pigD3ErZoSt2VDnEimbjcg6xUtHCC62YWulLhQKBgQCDWZS3hcuHkl562k+TACfY6TRr3naefs8GkUulJ4bKa6LNxhdaZvbn0myupzhA5qE+3YlhIVuVjiSpkH/dTri9D7K694pUD7udOFnoeozpCfXIRe8+g0FNfgSBYGi8uHGKOT2meuqFTxHjwp0qd0UnPioXSEQS9WR8Pze5BTJxywKBgQCR9pyZoUcCm/FykGIGCEqPNoLjkei8ex1EUn3VbTu8CIUphT38hh9RMQeZBK9kb4n4pYd4xjuyQUHQc1tM0jhXW0Fgxid4SAbw+8J5YZa+PXUpUUWoWOvVk/secAciAI0fPJiE5xSAbkBVM2f5cn+um1IKIxYZw/AYqz6WWgQHLg=="]}, "id": "ea1692bc-a875-40ed-a657-bec91f58b1fd", "name": "rsa-enc-generated", "providerId": "rsa-enc-generated", "subComponents": {}}, {"config": {"algorithm": ["HS512"], "kid": ["ac7eae01-5be5-40a1-9801-3301831f1fc9"], "priority": ["100"], "secret": ["6Q0Kn-jz_zM3UNGs_AdUxJuimF_oR3NaUp119IzkKBNlDLXC8q1SBBS0qUR2x993E3QLrrHUfg_saqtrNkVNbO0iZBQgUWOJG_gUR1bYr--Z2w7Jt7YvZ7d9fvo5lSuD2s_b3ZDlyFxRMU--lxNmP_zix3qqIDmB_iKwd6b9M9s"]}, "id": "aadf3abe-3b37-4774-bbbd-198a477d44f1", "name": "hmac-generated-hs512", "providerId": "hmac-generated", "subComponents": {}}, {"config": {"certificate": ["MIICozCCAYsCBgGYWkW9ujANBgkqhkiG9w0BAQsFADAVMRMwEQYDVQQDDApvbnJhbXAtZGV2MB4XDTI1MDczMDA3Mzc1NloXDTM1MDczMDA3MzkzNlowFTETMBEGA1UEAwwKb25yYW1wLWRldjCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAM7Yi5fuTtBSHLlEguQB6hVgdjgtf0e1Se3RCh4Tm25XJJqpmRfL1+OKLvHRNACx0XsJ7FNLYfMD096R/hjVMic6sNimmOx84dnN7+HCwhEQjugEzMuTCSdcwJ45w+/nst3CX7vxLmBUHsl3eYf626LJYoMmiXBhjvRpEPZuoFl20oY82mg+gxQhpDla05STQqGxK6sbACkNL98np756RI9JWrH6PxCV+6oN5bpy4R1DaGI9bU+WYNQA0HipKwaWZEcAxsvkQQjlQhuv6igYz8d5X2cPFBxm4haw9iG6xKFjIU1n+lQCV2WDClky7sQ7P1lpDuZ4gE11+XyFvK7NbokCAwEAATANBgkqhkiG9w0BAQsFAAOCAQEADMeVMXV4CXNrvmtMlk3P7aTuWWD2Z6B/71B80BU4TWB3CXQ5kmuudUGphw18um+RMeKhsLvpbJNTRdvGsk1okrju9HBycI/ja2DhiqYGZX2qePQNJ5AoPJ9OmjOoxZ6R9d/r+M4KlfNMiBswwQ5Oyialmkj6kmGLuoMbcdjouHxMen4Jb0EHa0TEdZaeaeC66pOHzOwBiwmRaRT97/5ybhC5nUYiHhJsEiUhK/+O5kwkERvDz+UswG8h2TH76gL6EQtbshNe0XtXxk+4Yzic9Eyi6XnvgzC3moBb133EK61J07YaVDQuUG05nbUCrsDrjqrlZNdIOaWpNYXpVWRNgw=="], "keyUse": ["SIG"], "priority": ["100"], "privateKey": ["MIIEpQIBAAKCAQEAztiLl+5O0FIcuUSC5AHqFWB2OC1/R7VJ7dEKHhObblckmqmZF8vX44ou8dE0ALHRewnsU0th8wPT3pH+GNUyJzqw2KaY7Hzh2c3v4cLCERCO6ATMy5MJJ1zAnjnD7+ey3cJfu/EuYFQeyXd5h/rbosligyaJcGGO9GkQ9m6gWXbShjzaaD6DFCGkOVrTlJNCobErqxsAKQ0v3yenvnpEj0lasfo/EJX7qg3lunLhHUNoYj1tT5Zg1ADQeKkrBpZkRwDGy+RBCOVCG6/qKBjPx3lfZw8UHGbiFrD2IbrEoWMhTWf6VAJXZYMKWTLuxDs/WWkO5niATXX5fIW8rs1uiQIDAQABAoIBAFaLMujYHCC8Ymk/2ZYFmQflqsD5hEof93Up49ZOtNc3XcjaUgQrVUJOVDL4KY5q1+9naPo2zZ4siU6T/eEs/oV8YHbdSZmU7LhBGm92mdUi/O/l5jv84rUV8T+igrETW/q982lC0XkbaSA5PV+gHZpgMTgXfSTIpNPtS9wX0D5toEMBMcCArL0GKTxVLTS9rUpFtGGL5TSFBHu01VK7iIEmqXn4y30tqzw6gEXB21O1P349MHkdVIXTVS1J1FvyKCvExcJFH6of9HDCurgar30CwI5BdU/Fv7kheldTKGC+Vh+amzszdsDoUpJCwu0Nl7H4xaL9sFM1sJChWiX5g7sCgYEA9sHq/LXc4/XnADbg3MREBBfU34O29MFLRc+9S6uY18RW1ovZnJYk8uCY/GuHQMwRdUJs1TjtXWF+FVqxDrdWSlMJ8eB7V70UhzP29EvG0vhhvD2XEW0OX/oJhgwOduMg0Au430t50d34S2H4dxN+j71vQ2eYPCkBbQ0qLEvG0LcCgYEA1pftWMD70GMsG0v9Uolr+c1WTMlQLgA92co90lVRRN2D5rCk9cGh4apA2BJLnUcVXFny6o0gqKJAh1yC82MPuIDwhTBlLD4smeefdYYUQHhp9litMpAY6TFWFV9aechMSiI6hdg1ObskS/L5ArJvoNkskEF3BbLwDsSzFlCD+r8CgYEAh0RqYrn3KufkB9QNlDfq6St/QD/eDXHgp5ZnuJGKqc0xpU7ordQI0TOZ6dWwQS8c6uDM9F3OkQAes+oRYXXTUc8Pho5+TuyR0kQEPotDpOcP/Lbul1jnJmnUyqzVSQrdM+8ZGx8u2oMJ15kPMYd4O6em1JZ3d5Mq+vLaiYnIpSUCgYEA1dFDeK+xOI3YIMqyek7a+2gRmSjtbyhmeF7/AlhoRYnCJ1XSx9xLhCKzwZdZKDjvm7k0RWihJeWlDjqrb/9AZsJy5QK3jEHXBvK2+87A1wwj6IWq7TqO16uE7n64E6fz+9nnHf2z6vYA0HwkiP9qIobB1GqmQVNtxglPt+g52NkCgYEA0YsfYpM7dJRb4Smu4ycJ4t14LjbM3qmFu+n2k3mpp19m8n1oHkxoC9+akcUgv0jzq78Fxpc7KXIWVh0XisyhqwzritN0F0kx/1Ie5UXq0cpVtRjMryN708v1tRBtabD/WF4Tr1V/1YiZxEEoIY5g1AYFiVX/Bb3+Sz4PMTj6fs4="]}, "id": "0678fb69-58a2-4d80-9e7d-377d1a205138", "name": "rsa-generated", "providerId": "rsa-generated", "subComponents": {}}, {"config": {"kid": ["3fe01f62-14a2-4465-8ca1-21f1fc0f6f0d"], "priority": ["100"], "secret": ["4UBY3IqDj1y1Nwtj-dJrcQ"]}, "id": "808ba6dd-90da-4c4c-a54b-311baf6147e1", "name": "aes-generated", "providerId": "aes-generated", "subComponents": {}}], "org.keycloak.services.clientregistration.policy.ClientRegistrationPolicy": [{"config": {}, "id": "75f3518b-e73a-4f14-a0bb-d78f4399ee61", "name": "Full Scope Disabled", "providerId": "scope", "subComponents": {}, "subType": "anonymous"}, {"config": {}, "id": "bd38edb4-bcdd-4c6e-8521-8d26c0f042f1", "name": "Consent Required", "providerId": "consent-required", "subComponents": {}, "subType": "anonymous"}, {"config": {"allow-default-scopes": ["true"]}, "id": "95892ac5-ff34-4071-a4d3-545b5822e78c", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subComponents": {}, "subType": "anonymous"}, {"config": {"allow-default-scopes": ["true"]}, "id": "d83c4a02-93d8-4e5f-b8a9-f674f32d732d", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subComponents": {}, "subType": "authenticated"}, {"config": {"allowed-protocol-mapper-types": ["oidc-address-mapper", "oidc-full-name-mapper", "oidc-sha256-pairwise-sub-mapper", "oidc-usermodel-attribute-mapper", "oidc-usermodel-property-mapper", "saml-role-list-mapper", "saml-user-attribute-mapper", "saml-user-property-mapper"]}, "id": "1e123edf-9012-4a27-adf8-221adcad5f2b", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subComponents": {}, "subType": "anonymous"}, {"config": {"allowed-protocol-mapper-types": ["oidc-address-mapper", "oidc-full-name-mapper", "oidc-sha256-pairwise-sub-mapper", "oidc-usermodel-attribute-mapper", "oidc-usermodel-property-mapper", "saml-role-list-mapper", "saml-user-attribute-mapper", "saml-user-property-mapper"]}, "id": "c615e69b-5c2f-4957-9ceb-43a03d81c27e", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subComponents": {}, "subType": "authenticated"}, {"config": {"client-uris-must-match": ["true"], "host-sending-registration-request-must-match": ["true"]}, "id": "438d95a8-a480-4d4e-8f8d-27e4e5f4425c", "name": "Trusted Hosts", "providerId": "trusted-hosts", "subComponents": {}, "subType": "anonymous"}, {"config": {"max-clients": ["200"]}, "id": "462a2f1e-abbc-4f60-b6df-081b769ad03e", "name": "Max Clients Limit", "providerId": "max-clients", "subComponents": {}, "subType": "anonymous"}]}, "defaultDefaultClientScopes": ["acr", "basic", "email", "profile", "role_list", "roles", "saml_organization", "web-origins"], "defaultOptionalClientScopes": ["address", "microprofile-jwt", "offline_access", "organization", "phone"], "defaultRole": {"clientRole": false, "composite": true, "containerId": "06350ad2-3a1c-4759-a673-964977102e43", "description": "${role_default-roles}", "id": "6b6a43c6-e45b-422b-9a3b-deee5e61c71d", "name": "default-roles-onramp-dev"}, "defaultSignatureAlgorithm": "RS256", "directGrantFlow": "direct grant", "dockerAuthenticationFlow": "docker auth", "duplicateEmailsAllowed": false, "editUsernameAllowed": false, "enabled": true, "enabledEventTypes": [], "eventsEnabled": false, "eventsListeners": ["jboss-logging"], "failureFactor": 30, "firstBrokerLoginFlow": "first broker login", "groups": [], "id": "06350ad2-3a1c-4759-a673-964977102e43", "identityProviderMappers": [], "identityProviders": [], "internationalizationEnabled": false, "keycloakVersion": "26.3.2", "localizationTexts": {}, "loginWithEmailAllowed": true, "maxDeltaTimeSeconds": 43200, "maxFailureWaitSeconds": 900, "maxTemporaryLockouts": 0, "minimumQuickLoginWaitSeconds": 60, "notBefore": 0, "oauth2DeviceCodeLifespan": 600, "oauth2DevicePollingInterval": 5, "offlineSessionIdleTimeout": 2592000, "offlineSessionMaxLifespan": 5184000, "offlineSessionMaxLifespanEnabled": false, "organizationsEnabled": false, "otpPolicyAlgorithm": "HmacSHA1", "otpPolicyCodeReusable": false, "otpPolicyDigits": 6, "otpPolicyInitialCounter": 0, "otpPolicyLookAheadWindow": 1, "otpPolicyPeriod": 30, "otpPolicyType": "totp", "otpSupportedApplications": ["totpAppFreeOTPName", "totpAppGoogleName", "totpAppMicrosoftAuthenticatorName"], "permanentLockout": false, "quickLoginCheckMilliSeconds": 1000, "realm": "onramp-dev", "refreshTokenMaxReuse": 0, "registrationAllowed": false, "registrationEmailAsUsername": false, "registrationFlow": "registration", "rememberMe": false, "requiredActions": [{"alias": "CONFIGURE_RECOVERY_AUTHN_CODES", "config": {}, "defaultAction": false, "enabled": true, "name": "Recovery Authentication Codes", "priority": 120, "providerId": "CONFIGURE_RECOVERY_AUTHN_CODES"}, {"alias": "CONFIGURE_TOTP", "config": {}, "defaultAction": false, "enabled": true, "name": "Configure OTP", "priority": 10, "providerId": "CONFIGURE_TOTP"}, {"alias": "TERMS_AND_CONDITIONS", "config": {}, "defaultAction": false, "enabled": false, "name": "Terms and Conditions", "priority": 20, "providerId": "TERMS_AND_CONDITIONS"}, {"alias": "UPDATE_PASSWORD", "config": {}, "defaultAction": false, "enabled": true, "name": "Update Password", "priority": 30, "providerId": "UPDATE_PASSWORD"}, {"alias": "UPDATE_PROFILE", "config": {}, "defaultAction": false, "enabled": true, "name": "Update Profile", "priority": 40, "providerId": "UPDATE_PROFILE"}, {"alias": "VERIFY_EMAIL", "config": {}, "defaultAction": false, "enabled": true, "name": "<PERSON><PERSON><PERSON>", "priority": 50, "providerId": "VERIFY_EMAIL"}, {"alias": "VERIFY_PROFILE", "config": {}, "defaultAction": false, "enabled": true, "name": "Verify Profile", "priority": 90, "providerId": "VERIFY_PROFILE"}, {"alias": "delete_account", "config": {}, "defaultAction": false, "enabled": false, "name": "Delete Account", "priority": 60, "providerId": "delete_account"}, {"alias": "delete_credential", "config": {}, "defaultAction": false, "enabled": true, "name": "Delete Credential", "priority": 100, "providerId": "delete_credential"}, {"alias": "idp_link", "config": {}, "defaultAction": false, "enabled": true, "name": "Linking Identity Provider", "priority": 110, "providerId": "idp_link"}, {"alias": "update_user_locale", "config": {}, "defaultAction": false, "enabled": true, "name": "Update User Locale", "priority": 1000, "providerId": "update_user_locale"}, {"alias": "webauthn-register", "config": {}, "defaultAction": false, "enabled": true, "name": "Webauthn Register", "priority": 70, "providerId": "webauthn-register"}, {"alias": "webauthn-register-passwordless", "config": {}, "defaultAction": false, "enabled": true, "name": "Webauthn Register Passwordless", "priority": 80, "providerId": "webauthn-register-passwordless"}], "requiredCredentials": ["password"], "resetCredentialsFlow": "reset credentials", "resetPasswordAllowed": false, "revokeRefreshToken": false, "roles": {"client": {"account": [{"attributes": {}, "clientRole": true, "composite": true, "composites": {"client": {"account": ["manage-account-links"]}}, "containerId": "5b2bbcfc-c78b-42af-a512-065623d8bd89", "description": "${role_manage-account}", "id": "6e280a2a-aa03-4c17-81f1-0b2c1393eb06", "name": "manage-account"}, {"attributes": {}, "clientRole": true, "composite": true, "composites": {"client": {"account": ["view-consent"]}}, "containerId": "5b2bbcfc-c78b-42af-a512-065623d8bd89", "description": "${role_manage-consent}", "id": "093479ba-22a2-4302-9bea-95a6bd52c874", "name": "manage-consent"}, {"attributes": {}, "clientRole": true, "composite": false, "containerId": "5b2bbcfc-c78b-42af-a512-065623d8bd89", "description": "${role_delete-account}", "id": "278c14e8-ecc2-47f5-a0d0-34468e96cf9e", "name": "delete-account"}, {"attributes": {}, "clientRole": true, "composite": false, "containerId": "5b2bbcfc-c78b-42af-a512-065623d8bd89", "description": "${role_manage-account-links}", "id": "a0697c30-ece0-42bc-a3f3-a60f01270d03", "name": "manage-account-links"}, {"attributes": {}, "clientRole": true, "composite": false, "containerId": "5b2bbcfc-c78b-42af-a512-065623d8bd89", "description": "${role_view-applications}", "id": "81b3c88a-e044-4c21-ad06-3e974a54c9d8", "name": "view-applications"}, {"attributes": {}, "clientRole": true, "composite": false, "containerId": "5b2bbcfc-c78b-42af-a512-065623d8bd89", "description": "${role_view-consent}", "id": "c723edd8-e8ab-4920-b40b-f5e55ee101b3", "name": "view-consent"}, {"attributes": {}, "clientRole": true, "composite": false, "containerId": "5b2bbcfc-c78b-42af-a512-065623d8bd89", "description": "${role_view-groups}", "id": "e9563ef1-80c9-4d37-baf9-1d1870626c93", "name": "view-groups"}, {"attributes": {}, "clientRole": true, "composite": false, "containerId": "5b2bbcfc-c78b-42af-a512-065623d8bd89", "description": "${role_view-profile}", "id": "f45d0351-4f87-486b-8f91-323cc63141ca", "name": "view-profile"}], "account-console": [], "admin-cli": [], "broker": [{"attributes": {}, "clientRole": true, "composite": false, "containerId": "4f29cc14-9c67-4125-8bd5-720c5ee208c6", "description": "${role_read-token}", "id": "30d54fa1-4857-4b35-a864-cb512603a5a0", "name": "read-token"}], "realm-management": [{"attributes": {}, "clientRole": true, "composite": true, "composites": {"client": {"realm-management": ["create-client", "impersonation", "manage-authorization", "manage-clients", "manage-events", "manage-identity-providers", "manage-realm", "manage-users", "query-clients", "query-groups", "query-realms", "query-users", "view-authorization", "view-clients", "view-events", "view-identity-providers", "view-realm", "view-users"]}}, "containerId": "********-ae40-43d9-8342-c6e615679288", "description": "${role_realm-admin}", "id": "a2e10fe3-c1b4-4337-8fa0-fe5e835e9368", "name": "realm-admin"}, {"attributes": {}, "clientRole": true, "composite": true, "composites": {"client": {"realm-management": ["query-clients"]}}, "containerId": "********-ae40-43d9-8342-c6e615679288", "description": "${role_view-clients}", "id": "a231cd56-471e-4b77-a1d1-403df5fc9833", "name": "view-clients"}, {"attributes": {}, "clientRole": true, "composite": true, "composites": {"client": {"realm-management": ["query-groups", "query-users"]}}, "containerId": "********-ae40-43d9-8342-c6e615679288", "description": "${role_view-users}", "id": "00728a3a-1850-4df8-a0b5-b9c7c87c4e80", "name": "view-users"}, {"attributes": {}, "clientRole": true, "composite": false, "containerId": "********-ae40-43d9-8342-c6e615679288", "description": "${role_create-client}", "id": "08b08b7d-af71-4122-91fa-caff44e7d6a0", "name": "create-client"}, {"attributes": {}, "clientRole": true, "composite": false, "containerId": "********-ae40-43d9-8342-c6e615679288", "description": "${role_impersonation}", "id": "9a328a5a-317c-43ad-a167-63689c63b247", "name": "impersonation"}, {"attributes": {}, "clientRole": true, "composite": false, "containerId": "********-ae40-43d9-8342-c6e615679288", "description": "${role_manage-authorization}", "id": "b6628cd4-8d4e-4043-a314-b9abd0ac7e74", "name": "manage-authorization"}, {"attributes": {}, "clientRole": true, "composite": false, "containerId": "********-ae40-43d9-8342-c6e615679288", "description": "${role_manage-clients}", "id": "24124f0b-57c1-4675-be4f-a6a8421ef0a7", "name": "manage-clients"}, {"attributes": {}, "clientRole": true, "composite": false, "containerId": "********-ae40-43d9-8342-c6e615679288", "description": "${role_manage-events}", "id": "0e8ba41a-11bf-44bf-ab8a-29fb39ff4ba8", "name": "manage-events"}, {"attributes": {}, "clientRole": true, "composite": false, "containerId": "********-ae40-43d9-8342-c6e615679288", "description": "${role_manage-identity-providers}", "id": "0837b89e-702d-4ee7-bad4-50babd6933ba", "name": "manage-identity-providers"}, {"attributes": {}, "clientRole": true, "composite": false, "containerId": "********-ae40-43d9-8342-c6e615679288", "description": "${role_manage-realm}", "id": "763eb092-edcd-45be-ae25-eb069f1eb89e", "name": "manage-realm"}, {"attributes": {}, "clientRole": true, "composite": false, "containerId": "********-ae40-43d9-8342-c6e615679288", "description": "${role_manage-users}", "id": "b6c26f0a-5fc5-42ea-a761-08965ae7af19", "name": "manage-users"}, {"attributes": {}, "clientRole": true, "composite": false, "containerId": "********-ae40-43d9-8342-c6e615679288", "description": "${role_query-clients}", "id": "91198347-5d2a-45ea-92fd-22bebf36a310", "name": "query-clients"}, {"attributes": {}, "clientRole": true, "composite": false, "containerId": "********-ae40-43d9-8342-c6e615679288", "description": "${role_query-groups}", "id": "a709c581-6a13-45bc-be90-21a6f1c19f2a", "name": "query-groups"}, {"attributes": {}, "clientRole": true, "composite": false, "containerId": "********-ae40-43d9-8342-c6e615679288", "description": "${role_query-realms}", "id": "a8567b2a-eb07-40ad-bc0b-6c16aae46fec", "name": "query-realms"}, {"attributes": {}, "clientRole": true, "composite": false, "containerId": "********-ae40-43d9-8342-c6e615679288", "description": "${role_query-users}", "id": "122158ce-0e75-4bbe-8bcb-821946097204", "name": "query-users"}, {"attributes": {}, "clientRole": true, "composite": false, "containerId": "********-ae40-43d9-8342-c6e615679288", "description": "${role_view-authorization}", "id": "5b72f5c9-5e21-4e03-bfb6-b7482a31025c", "name": "view-authorization"}, {"attributes": {}, "clientRole": true, "composite": false, "containerId": "********-ae40-43d9-8342-c6e615679288", "description": "${role_view-events}", "id": "1ed52321-0e10-4ddf-a012-44dfc96b9683", "name": "view-events"}, {"attributes": {}, "clientRole": true, "composite": false, "containerId": "********-ae40-43d9-8342-c6e615679288", "description": "${role_view-identity-providers}", "id": "1086caf8-4ff7-4420-a7b2-30d1cd787cee", "name": "view-identity-providers"}, {"attributes": {}, "clientRole": true, "composite": false, "containerId": "********-ae40-43d9-8342-c6e615679288", "description": "${role_view-realm}", "id": "6e671c60-2227-4ad9-b99a-af4895820d21", "name": "view-realm"}], "security-admin-console": []}, "realm": [{"attributes": {}, "clientRole": false, "composite": true, "composites": {"client": {"account": ["manage-account", "view-profile"]}, "realm": ["offline_access", "uma_authorization"]}, "containerId": "06350ad2-3a1c-4759-a673-964977102e43", "description": "${role_default-roles}", "id": "6b6a43c6-e45b-422b-9a3b-deee5e61c71d", "name": "default-roles-onramp-dev"}, {"attributes": {}, "clientRole": false, "composite": false, "containerId": "06350ad2-3a1c-4759-a673-964977102e43", "description": "${role_offline-access}", "id": "bc0900f4-0a1b-4542-b833-637e70e31bd7", "name": "offline_access"}, {"attributes": {}, "clientRole": false, "composite": false, "containerId": "06350ad2-3a1c-4759-a673-964977102e43", "description": "${role_uma_authorization}", "id": "c7d97458-38a8-4bca-8447-0ecf6f79dfdb", "name": "uma_authorization"}]}, "scopeMappings": [{"clientScope": "offline_access", "roles": ["offline_access"]}], "smtpServer": {}, "sslRequired": "external", "ssoSessionIdleTimeout": 1800, "ssoSessionIdleTimeoutRememberMe": 0, "ssoSessionMaxLifespan": 36000, "ssoSessionMaxLifespanRememberMe": 0, "userManagedAccessAllowed": false, "verifiableCredentialsEnabled": false, "verifyEmail": false, "waitIncrementSeconds": 60, "webAuthnPolicyAcceptableAaguids": [], "webAuthnPolicyAttestationConveyancePreference": "not specified", "webAuthnPolicyAuthenticatorAttachment": "not specified", "webAuthnPolicyAvoidSameAuthenticatorRegister": false, "webAuthnPolicyCreateTimeout": 0, "webAuthnPolicyExtraOrigins": [], "webAuthnPolicyPasswordlessAcceptableAaguids": [], "webAuthnPolicyPasswordlessAttestationConveyancePreference": "not specified", "webAuthnPolicyPasswordlessAuthenticatorAttachment": "not specified", "webAuthnPolicyPasswordlessAvoidSameAuthenticatorRegister": false, "webAuthnPolicyPasswordlessCreateTimeout": 0, "webAuthnPolicyPasswordlessExtraOrigins": [], "webAuthnPolicyPasswordlessRequireResidentKey": "not specified", "webAuthnPolicyPasswordlessRpEntityName": "keycloak", "webAuthnPolicyPasswordlessRpId": "", "webAuthnPolicyPasswordlessSignatureAlgorithms": ["ES256", "RS256"], "webAuthnPolicyPasswordlessUserVerificationRequirement": "not specified", "webAuthnPolicyRequireResidentKey": "not specified", "webAuthnPolicyRpEntityName": "keycloak", "webAuthnPolicyRpId": "", "webAuthnPolicySignatureAlgorithms": ["ES256", "RS256"], "webAuthnPolicyUserVerificationRequirement": "not specified"}