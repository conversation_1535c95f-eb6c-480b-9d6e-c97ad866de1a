#!/usr/bin/env bash
set -euo pipefail

KC_HOME=/opt/keycloak
APP_DIR=/app
REALM_NAME=${REALM_NAME:-onramp-dev}
REALM_FILE=$APP_DIR/realm-export.json
TMP_EXPORT=$APP_DIR/realm-export.tmp.json

DB_HOST=${KC_DB_HOST:-kc-postgres}
DB_NAME=${KC_DB_NAME:-keycloak}
DB_USER=${KC_DB_USERNAME:-keycloak}
DB_PASSWORD=${KC_DB_PASSWORD:-keycloak}

REALM_ID="$REALM_NAME"
TABLE_NAME="realm_import_meta"

export PGPASSWORD="$DB_PASSWORD"

echo "Waiting for Postgres to become ready..."
until pg_isready -h "$DB_HOST" -U "$DB_USER" > /dev/null 2>&1; do
  sleep 1
done
echo "Postgres is ready."

# Get realm file stats
if [[ -f "$REALM_FILE" ]]; then
  REALM_MTIME=$(stat -c %Y "$REALM_FILE")
  REALM_SIZE=$(stat -c %s "$REALM_FILE")
else
  echo "Realm file not found at $REALM_FILE"
  exit 1
fi

# Ensure metadata table exists
psql -h "$DB_HOST" -U "$DB_USER" -d "$DB_NAME" -c "
CREATE TABLE IF NOT EXISTS $TABLE_NAME (
  realm_id      VARCHAR PRIMARY KEY,
  file_size     BIGINT,
  file_mtime    BIGINT,
  imported_at   TIMESTAMP DEFAULT now()
);
" > /dev/null

# Get previous import metadata
IFS="|" read -r DB_MTIME DB_SIZE <<< "$(psql -h "$DB_HOST" -U "$DB_USER" -d "$DB_NAME" -tAc \
  "SELECT COALESCE(file_mtime, 0), COALESCE(file_size, 0) FROM $TABLE_NAME WHERE realm_id = '$REALM_ID';" | tr -d '[:space:]')"


DB_MTIME=${DB_MTIME:-0}
DB_SIZE=${DB_SIZE:-0}

# Compare them, if local file is different, override realm
if [[ "$REALM_MTIME" -gt "$DB_MTIME" ]] || [[ "$REALM_SIZE" -ne "$DB_SIZE" ]]; then
  echo "Realm file has changed - importing..."
  "$KC_HOME/bin/kc.sh" import --file "$REALM_FILE" --override || true

  echo "Updating import metadata in DB..."
  psql -h "$DB_HOST" -U "$DB_USER" -d "$DB_NAME" -c "
    INSERT INTO $TABLE_NAME (realm_id, file_size, file_mtime)
    VALUES ('$REALM_ID', $REALM_SIZE, $REALM_MTIME)
    ON CONFLICT (realm_id)
    DO UPDATE SET
      file_size = EXCLUDED.file_size,
      file_mtime = EXCLUDED.file_mtime,
      imported_at = now();
  "
else
  echo "Realm has not changed - skipping import."
fi

# 2) Define cleanup (will run on SIGTERM)
cleanup() {
  # echo "SIGTERM received; stopping Keycloak..."
  kill -- -"$KC_PID"   || true
  wait "$KC_PID"       || true

  # echo "Raw export of realm '$REALM_NAME' to $TMP_EXPORT"
  "$KC_HOME/bin/kc.sh" export \
    --realm     "$REALM_NAME" \
    --file      "$TMP_EXPORT" \
    --users     same_file \
    --optimized

  # echo "Canonicalizing JSON"
  jq -S 'walk(if type=="array" then sort else . end)' "$TMP_EXPORT" \
     > "${TMP_EXPORT}.canon"
  mv "${TMP_EXPORT}.canon" "$TMP_EXPORT"

  # echo "Comparing to existing $REALM_FILE"
  if cmp -s "$TMP_EXPORT" "$REALM_FILE"; then
    # echo "no real changes"
    :
  else
    mv "$TMP_EXPORT" "$REALM_FILE"
    # echo "realm-export.json updated"
  fi
}
trap cleanup SIGTERM SIGINT

# Starting Keycloak dev server
echo "Starting Keycloak..."
setsid "$KC_HOME/bin/kc.sh" start-dev &
KC_PID=$!

# now *this* shell stays alive, waiting for Keycloak or a SIGTERM
wait "$KC_PID"
