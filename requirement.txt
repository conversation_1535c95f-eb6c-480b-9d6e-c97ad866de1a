add an invite system


1. System needs to be able to:

Create and update an user invite for an organization to be sent via email

Store that invite in Postgres

Log each create and update into bigquery

2. Data Model (Postgres)

CREATE TABLE {{UserInvites}} (
  id UUID NOT NULL DEFAULT gen_random_uuid(),
  organizationidentifier UUID NOT NULL,
  tokenhash TEXT NOT NULL,
  email TEXT NOT NULL,
  inviterid UUID NOT NULL,
  customroleid UUID NOT NULL,
  status TEXT NOT NULL,
  message TEXT,
  requiresso BOOL FALSE,
  retrycount INT NOT NULL DEFAULT 0,
  retried TIMESTAMPTZ,
  expired TIMESTAMPTZ,
  created TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  sent TIMESTAMPTZ,
  updated TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  CONSTRAINT {{UserInvites_PK}} PRIMARY KEY (id),
  CONSTRAINT {{UserInvites_tokenhash_UQ}} UNIQUE (tokenhash),
  CONSTRAINT {{UserInvites_inviterid_FK}} FOREIGN KEY (inviterid) REFERENCES "User"(id),
  CONSTRAINT {{UserInvites_org_FK}} FOREIGN KEY (organizationidentifier) REFERENCES "Organization"(id),
  CONSTRAINT {{UserInvites_status_CHK}} CHECK (status IN ('pending','redeemed','revoked','expired','rejected')),
  CONSTRAINT {{UserInvites_CustomRole_FK}} FOREIGN KEY (customroleid) REFERENCES {{CustomRole}} (Id)
);


2.1 Data Model (Bigquery)

-- Audit table (for BigQuery ingestion)
CREATE TABLE {{InviteEvents}} (
  userinviteid  STRING    {% OPTIONS(description="The UUID of the userinvite ") %},
  eventtype     STRING    {% OPTIONS(description=" create, retry, revoke, redeem, rejected") %},
  actor         STRING    {% OPTIONS(description="Either the UUID of the user making the change or the email of the person being invited") %},
  eventtime     TIMESTAMP {% OPTIONS(description="The timestamp the invite event") %},
  organizationidentifier STRING    {% OPTIONS(description="The UUID of the organization") %},
  tokenhash STRING    {% OPTIONS(description="The tokenhas for the email invite ") %},
  email STRING    {% OPTIONS(description="The Email of the person being invited") %},
  inviterid STRING    {% OPTIONS(description="The UUID of the user that did the invite") %},
  organizationrole STRING    {% OPTIONS(description="The UUID of the custom role used assigned to this user") %},
  status STRING    {% OPTIONS(description="The current status of the invite") %},
  message STRING    {% OPTIONS(description="The custom message added to the invite, this is OPTIONAL") %},
  requiresso BOOL {% OPTIONS(description="Flag to require SSO when creating account") %},
  retrycount INT64    {% OPTIONS(description="The count of retrys") %},
  retried TIMESTAMP {% OPTIONS(description="The timestamp of the lastest retry") %},
  expired TIMESTAMP {% OPTIONS(description="The timestamp the invite event expires NULL means it wont expire") %},
  created TIMESTAMP {% OPTIONS(description="The timestamp the invite event was created") %},
  sent TIMESTAMP {% OPTIONS(description="The timestamp the invite event was sent") %},
  updated TIMESTAMP {% OPTIONS(description="The timestamp the invite event status was changed") %}
)
{% PARTITION BY DATE(eventtime) CLUSTER BY userinviteid %};

3. API Endpoints 

Create Invite endpoint

The base code needs to be in the /shared directory (because it will be used by other microservices in the future)

The onramp endpoints need to be in /microservices/onramp

POST /api/organization/{orgid}/invites

Body Example
{
  "email": "<EMAIL>",
  "inviterid": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "message": "Hi Jane! We’d love to have you join onramp.",
  "organizationrole": "5b9a1d2f-4d3c-4e50-9d73-7f5da8b42a10"
  "expireddays": ""
}

-email, inviterid, and organizationrole are required fields

func CreateInvites() {
  // generate secure tokens hash
  // insert into Postgres
  // insert log to BigQuery
  // publish message to the etl-notifications topic with the payload
      type: set to "email" 
      to: set to the email of the person being invited
      message: set to the html template
  }


Default email template

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>You're Invited to {{appName}}!</title>
</head>
<body style="margin:0;padding:0;background-color:#f4f4f4;">
  <table width="100%" cellpadding="0" cellspacing="0" role="presentation">
    <tr>
      <td align="center" style="padding:20px 0;">
        <table width="600" cellpadding="0" cellspacing="0" style="background-color:#ffffff;border-radius:8px;overflow:hidden;">
          <tr>
            <td style="padding:40px;text-align:center;font-family:Arial,sans-serif;">
              <h1 style="margin:0;color:#333333;font-size:24px;">You’re Invited!</h1>
            </td>
          </tr>
          <tr>
            <td style="padding:0 40px 20px;font-family:Arial,sans-serif;color:#555555;font-size:16px;line-height:1.5;">
              <p>{{message}}</p>
              <p style="text-align:center;padding:20px 0;">
                <a href="{{inviteLink}}" style="background-color:#007BFF;color:#ffffff;padding:14px 28px;text-decoration:none;border-radius:4px;display:inline-block;font-size:16px;">
                  Accept Invitation
                </a>
              </p>
              <p>If the button above doesn’t work, copy and paste this link into your browser:</p>
              <p style="word-break:break-all;"><a href="{{inviteLink}}" style="color:#007BFF;">{{inviteLink}}</a></p>
            </td>
          </tr>
          <tr>
            <td style="padding:20px 40px;background-color:#f9f9f9;font-family:Arial,sans-serif;color:#999999;font-size:12px;">
              <p style="margin:0;">Sent by {{appName}} on behalf of {{organizationName}}</p>
            </td>
          </tr>
        </table>
      </td>
    </tr>
  </table>
</body>
</html>

Default Values for the variables in html

appName= “Onramp”

message = the message field that is passed in the body of the request (can be empty)

inviteLink = /api/organization/{orgid}/invites/validate?token=generated secure hash token

organizationName = the name of the organization based on the orgid passed in the url



List User Invites For User Endpoint

GET /api/user/{userid}/invites

func ListInvites() {
// query postgres to get the list of invites that match any email the user has in the Authmethod table to the UserInvites table
}



List User Invites For Organization Endpoint

GET /api/organization/{orgid}/invites

func ListInvites() {
// query postgres to get the list of invites that have the organizationidentifier from the url
}



Reject User Invites Endpoint

DELETE /api/user/{userid}/invites/{id}

func RevokeInvite() {
// update the status of the invite event to rejected and the updated timestamp in postgres
// insert log to BigQuery
}



Delete User Invites Endpoint

DELETE /api/organization/{orgid}/invites/{id}

Body Example
{
  "actor": "3fa85f64-5717-4562-b3fc-2c963f66afa6"
}

func RevokeInvite() {
// update the status of the invite event to revoked and the updated timestamp in postgres
// insert log to BigQuery
}



Resend User Invites Endpoint

POST /api/organization/{orgid}/invites/{id}/resend

Body Example
{
  "actor": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "message": "Hi Jane! We’d love to have you join onramp.",
  "expireddays": ""
}

-Needs to have a cooldown of 1 min to resend based on the retried timestamp. So return a too soon message in the response message

func ResendInvite() {
  // generate secure tokens hash
  // update Postgres tokenhash, retrycount, retried timestamp, message, expired timestamp, updated timestamp 
  // insert log to BigQuery
  // publish message to the etl-notifications topic with the payload
      type: set to "email" 
      to: set to the email of the person being invited
      message: set to the html template
}



Validate User Invites Endpoint

GET /api/user/{userid}/invites/validate?token=

func ValidateInvite() {
// Checks the token to see if it is a valid token in the database for the org and returns a success if true and unauthorized if false
}



Redeem User Invites Endpoint

POST /api/user/{userid}/invites/{id}/redeem

func RedeemInvite() {
// update the status of the invite event to redeemed and the updated timestamp in postgres
// insert log to BigQuery
}

