---
description: 
globs: *_test.go,**/*_test.go
alwaysApply: false
---
# Code Coverage Rules for Go Projects

## Error Definition Rules

1. **Centralized Error Definitions**

   - All errors should be defined in a dedicated `errors.go` file
   - Use `var` declarations with descriptive variable names prefixed with `Err`
   - Group related errors with comments indicating which function they belong to
   - Example:

     ```go
     // ApplyMigrations() errors
     ErrSchemaTable = errors.New("failed to ensure schema migrations table")
     ErrVersionDirs = errors.New("failed to list version directories")
     ```

2. **Error Wrapping**

   - When returning errors from functions, wrap them with additional context
   - Use `fmt.Errorf("%w: %v", ErrXxx, err)` to preserve the error chain
   - This allows for both error inspection and detailed error messages
   - Example:

     ```go
     if err := executor.EnsureSchemaMigrationsTable(); err != nil {
         return fmt.Errorf("%w: %v", ErrSchemaTable, err)
     }
     ```

3. **Error Checking**

   - Use `errors.Is()` to check for specific error types
   - Use `errors.As()` to extract error details when needed
   - Example:

     ```go
     if errors.Is(err, sql.ErrNoRows) {
         // Handle no rows case
     }
     ```

4. **Mock struct**

  -  Use mocks from `/shared/mocks` first
  
5. **Function Structure**

  - Use `t.Parallel()` at the beginning of fuction to enable concurrent execution

## File Structure Rules

1. **Package Organization**

   - Each package should have a clear, single responsibility
   - Related functionality should be grouped in the same package
   - Example: `schema_mgmt` package handles database schema management

2. **File Organization**

   - **Main Implementation File**: Core functionality in a file named after the package (e.g., `schema_mgmt.go`)
   - **Error Definitions**: All errors in `errors.go`
   - **SQL Queries**: SQL statements in `sql.go`
   - **Mock Implementations**: Test mocks in `mocks.go`
   - **Tests**: Test files with `_test.go` suffix

3. **Code Structure Within Files**
   - Start with package declaration and imports
   - Follow with constants and types
   - Define interfaces before their implementations
   - Group related functions together
   - Use comments to separate logical sections

## Testing Rules

1. **Test Coverage Requirements**

   - Aim for 100% test coverage for all packages
   - All error paths must be tested
   - Use table-driven tests for similar test cases
   - Mock external dependencies for unit tests
   - Verify coverage in `/microservices/testing/coverage/final_coverage.out`
   - Address lines showing 0 coverage

2. **Test File Naming**

   - Test files must be named `{source_file}_test.go`
   - Example: For `foo.go`, create `foo_test.go`

3. **Test Function Naming**

   - Format: `Test_{function_name}_{scenario}`
   - Example: `Test_absJoin_fail`, `Test_absJoin_success`

4. **Test Organization**

   - Group tests by function being tested
   - Use subtests for different test cases
   - Test both happy paths and error paths
   - Use descriptive test names and assertions
   - Use table-driven tests when testing multiple scenarios:

     ```go
     tests := []struct {
         name     string
         input    type
         expected type
     }{
         {"scenario1", input1, expected1},
         {"scenario2", input2, expected2},
     }
     ```

5. **Table-Driven Tests Requirements**

   - MUST use table-driven tests for functions with multiple test scenarios
   - Each test case in the table MUST have:
     - Descriptive name that explains the scenario
     - All necessary input parameters
     - Expected output or error
     - Any mock setup functions if required
   - Table structure MUST be clear and consistent:
     ```go
     tests := []struct {
         name          string        // Required: descriptive test name
         input         interface{}   // Input parameters
         setupFn       func()        // Optional: setup function
         mockFn        func()        // Optional: mock setup
         expected      interface{}   // Expected output
         expectedErr   error         // Expected error if any
         wantErr       bool         // Whether error is expected
     }
     ```
   - Execute test cases in a loop using t.Run():
     ```go
     for _, tt := range tests {
         t.Run(tt.name, func(t *testing.T) {
             // Enable parallel test execution when tests are independent
             t.Parallel()
             
             // Optional: Setup
             if tt.setupFn != nil {
                 tt.setupFn()
             }

             // Optional: Mock setup
             if tt.mockFn != nil {
                 tt.mockFn()
             }

             // Execute test
             result, err := functionUnderTest(tt.input)

             // Assert results
             if tt.wantErr {
                 assert.Error(t, err)
                 assert.Equal(t, tt.expectedErr, err)
             } else {
                 assert.NoError(t, err)
                 assert.Equal(t, tt.expected, result)
             }
         })
     }
     ```
   - Benefits that MUST be achieved:
     - Consistent test structure across the codebase
     - Easy to add new test cases
     - Clear documentation of test scenarios
     - Reduced code duplication
     - Better maintainability

6. **Test Steps**

   - Add comments for each test step
   - Comments must start with capital letter
   - End complete sentences with period
   - Example:

     ```go
     // Mock the absJoin_filepath_Abs function to simulate a failure
     old_func := absJoin_filepath_Abs
     defer func() { absJoin_filepath_Abs = old_func }()
     ```

7. **Assertion Style**

   - Use `assert` package for cleaner assertions
   - Include descriptive messages in assertions
   - Use appropriate assertion types (Equal, ErrorIs, etc.)
   - Example:

     ```go
     assert.ErrorIs(err, ErrSchemaTable, "should return error for failure to write schema migrations table")
     ```

8. **Test Implementation Order**

   - Start with functions that have no dependencies
   - Move to functions that depend on already tested functions
   - Test helper/utility functions before testing main functions
   - Organize test cases in this order:
     1. Basic functionality tests
     2. Edge case tests
     3. Error condition tests
     4. Special case tests

9. **Test Case Categories**

   - **Input Validation**: Test with valid inputs, invalid inputs, boundary values, empty/zero values
   - **Error Handling**: Test error conditions, verify error messages, test nil input handling
   - **Edge Cases**: Test maximum/minimum values, boundary conditions, special cases

10. **Assertion Best Practices**
    - Test one condition per assertion
    - Use `assert.Equal` for value comparisons
    - Use `assert.Error`/`assert.NoError` for error checking
    - Use `assert.Nil`/`assert.NotNil` for nil checks
    - Use `assert.Len` for slice/array length checks
    - Use `assert.Contains` for map/slice membership tests

11. **Parallel Test Execution**
    - Use `t.Parallel()` at the beginning of subtests to enable concurrent execution
    - Only use parallel execution when tests are independent and don't share mutable state
    - Ensure all test resources are properly isolated when using parallel execution
    - Example:
      ```go
      t.Run("subtest name", func(t *testing.T) {
          t.Parallel() // This subtest can run in parallel with others
          // Test implementation
      })
      ```
    - Benefits of parallel testing:
      - Faster test execution, especially for I/O-bound tests
      - Earlier detection of race conditions and concurrency issues
      - Better utilization of multi-core processors
    - When NOT to use parallel testing:
      - Tests that modify global state
      - Tests that depend on specific execution order
      - Tests that use shared resources without proper synchronization

## Mock Implementation

1. **Function Mocking**

   - Create mockable functions for testing:

     ```go
     var (
         // Mockable functions for testing
         ApplyMigrations_absJoin1 func(path ...string) (string, error) = absJoin
     )
     ```

   - Use deferred restoration of original functions
   - Example:

     ```go
     old_func := originalFunc
     defer func() { originalFunc = old_func }()
     originalFunc = mockFunc
     ```

2. **Database Mocking**

   - Use mocks from `/shared/mocks` for database operations
   - Create new mocks if needed with corresponding tests
   - Create mock structs that implement interfaces for testing
   - Example: Use `mocks.MockDatabaseExecutor`

3. **Built-in Function Testing**

   - Define package-level variables for built-in functions
   - Override during tests and restore after
   - Example:

     ```go
     var absJoin_filepath_Abs = filepath.Abs
     // In test:
     old_func := absJoin_filepath_Abs
     defer func() { absJoin_filepath_Abs = old_func }()
     ```

## Documentation Rules

1. **Comments**

   - Add package-level documentation
   - Document all exported functions, types, and constants
   - Use comments to explain complex logic
   - Group related constants and variables with explanatory comments
   - Start with capital letter (except for variable/function references)
   - End sentences with period
   - Use descriptive comments for test scenarios

2. **README**
   - Include a README.md file in each package
   - Describe the package's purpose and usage
   - Document any special considerations or requirements

## SQL Management Rules

1. **SQL Query Organization**

   - Define SQL queries as constants in a dedicated `sql.go` file
   - Use descriptive variable names
   - Add comments explaining the purpose of each query
   - Example:

     ```go
     // Get the version of the schema used to create the database
     SQLGetDatabaseSchemaVersionCreatedAt = `
         SELECT schema_version
         FROM {{schema_migrations}}
         ORDER BY applied_at ASC
         LIMIT 1`
     ```

2. **SQL Execution**
   - Use parameterized queries to prevent SQL injection
   - Handle SQL errors appropriately
   - Wrap SQL execution in functions that provide context on failure

## Linting

1. **Linter Compliance**
   - Run in dev container for correct linter settings
   - Address all linter warnings
   - Follow project's code style guidelines
