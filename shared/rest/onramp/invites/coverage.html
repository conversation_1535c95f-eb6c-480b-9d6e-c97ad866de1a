
<!DOCTYPE html>
<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
		<title>invites: Go Coverage Report</title>
		<style>
			body {
				background: black;
				color: rgb(80, 80, 80);
			}
			body, pre, #legend span {
				font-family: Menlo, monospace;
				font-weight: bold;
			}
			#topbar {
				background: black;
				position: fixed;
				top: 0; left: 0; right: 0;
				height: 42px;
				border-bottom: 1px solid rgb(80, 80, 80);
			}
			#content {
				margin-top: 50px;
			}
			#nav, #legend {
				float: left;
				margin-left: 10px;
			}
			#legend {
				margin-top: 12px;
			}
			#nav {
				margin-top: 10px;
			}
			#legend span {
				margin: 0 5px;
			}
			.cov0 { color: rgb(192, 0, 0) }
.cov1 { color: rgb(128, 128, 128) }
.cov2 { color: rgb(116, 140, 131) }
.cov3 { color: rgb(104, 152, 134) }
.cov4 { color: rgb(92, 164, 137) }
.cov5 { color: rgb(80, 176, 140) }
.cov6 { color: rgb(68, 188, 143) }
.cov7 { color: rgb(56, 200, 146) }
.cov8 { color: rgb(44, 212, 149) }
.cov9 { color: rgb(32, 224, 152) }
.cov10 { color: rgb(20, 236, 155) }

		</style>
	</head>
	<body>
		<div id="topbar">
			<div id="nav">
				<select id="files">
				
				<option value="file0">synapse-its.com/shared/rest/onramp/invites/email.go (60.0%)</option>
				
				<option value="file1">synapse-its.com/shared/rest/onramp/invites/invites.go (94.1%)</option>
				
				</select>
			</div>
			<div id="legend">
				<span>not tracked</span>
			
				<span class="cov0">not covered</span>
				<span class="cov8">covered</span>
			
			</div>
		</div>
		<div id="content">
		
		<pre class="file" id="file0" style="display: none">package invites

import (
        "bytes"
        "html/template"

        "synapse-its.com/shared/logger"
)

// Default email template HTML
const defaultEmailTemplate = `&lt;!DOCTYPE html&gt;
&lt;html lang="en"&gt;
&lt;head&gt;
  &lt;meta charset="UTF-8"&gt;
  &lt;meta name="viewport" content="width=device-width, initial-scale=1.0"&gt;
  &lt;title&gt;You're Invited to {{.AppName}}!&lt;/title&gt;
&lt;/head&gt;
&lt;body style="margin:0;padding:0;background-color:#f4f4f4;"&gt;
  &lt;table width="100%" cellpadding="0" cellspacing="0" role="presentation"&gt;
    &lt;tr&gt;
      &lt;td align="center" style="padding:20px 0;"&gt;
        &lt;table width="600" cellpadding="0" cellspacing="0" style="background-color:#ffffff;border-radius:8px;overflow:hidden;"&gt;
          &lt;tr&gt;
            &lt;td style="padding:40px;text-align:center;font-family:Arial,sans-serif;"&gt;
              &lt;h1 style="margin:0;color:#333333;font-size:24px;"&gt;You're Invited!&lt;/h1&gt;
            &lt;/td&gt;
          &lt;/tr&gt;
          &lt;tr&gt;
            &lt;td style="padding:0 40px 20px;font-family:Arial,sans-serif;color:#555555;font-size:16px;line-height:1.5;"&gt;
              {{if .Message}}&lt;p&gt;{{.Message}}&lt;/p&gt;{{end}}
              &lt;p style="text-align:center;padding:20px 0;"&gt;
                &lt;a href="{{.InviteLink}}" style="background-color:#007BFF;color:#ffffff;padding:14px 28px;text-decoration:none;border-radius:4px;display:inline-block;font-size:16px;"&gt;
                  Accept Invitation
                &lt;/a&gt;
              &lt;/p&gt;
              &lt;p&gt;If the button above doesn't work, copy and paste this link into your browser:&lt;/p&gt;
              &lt;p style="word-break:break-all;"&gt;&lt;a href="{{.InviteLink}}" style="color:#007BFF;"&gt;{{.InviteLink}}&lt;/a&gt;&lt;/p&gt;
            &lt;/td&gt;
          &lt;/tr&gt;
          &lt;tr&gt;
            &lt;td style="padding:20px 40px;background-color:#f9f9f9;font-family:Arial,sans-serif;color:#999999;font-size:12px;"&gt;
              &lt;p style="margin:0;"&gt;Sent by {{.AppName}} on behalf of {{.OrganizationName}}&lt;/p&gt;
            &lt;/td&gt;
          &lt;/tr&gt;
        &lt;/table&gt;
      &lt;/td&gt;
    &lt;/tr&gt;
  &lt;/table&gt;
&lt;/body&gt;
&lt;/html&gt;`

// renderEmailTemplate
func renderEmailTemplate(data EmailTemplateData) (string, error) <span class="cov8" title="1">{
        tmpl, err := template.New("invite").Parse(defaultEmailTemplate)
        if err != nil </span><span class="cov0" title="0">{
                logger.Errorf("failed to parse email template: %v", err)
                return "", ErrTemplateParsing
        }</span>

        <span class="cov8" title="1">var buf bytes.Buffer
        err = tmpl.Execute(&amp;buf, data)
        if err != nil </span><span class="cov0" title="0">{
                logger.Errorf("failed to execute email template: %v", err)
                return "", ErrTemplateExecution
        }</span>

        <span class="cov8" title="1">return buf.String(), nil</span>
}
</pre>
		
		<pre class="file" id="file1" style="display: none">package invites

import (
        "context"
        "database/sql"
        "encoding/json"
        "errors"
        "fmt"
        "net/http"
        "net/mail"
        "regexp"
        "time"

        "cloud.google.com/go/bigquery"
        "cloud.google.com/go/pubsub"
        "github.com/google/uuid"
        "synapse-its.com/shared/api/response"
        security "synapse-its.com/shared/api/security"
        "synapse-its.com/shared/bqbatch"
        "synapse-its.com/shared/connect"
        "synapse-its.com/shared/logger"
        "synapse-its.com/shared/pubsubdata"
        "synapse-its.com/shared/rest/onramp/helper"
        "synapse-its.com/shared/schemas"
)

type HandlerDeps struct {
        GetConnections            func(context.Context, ...bool) (*connect.Connections, error)
        GetBQBatch                func(ctx context.Context) (bqbatch.Batcher, error)
        CreateInvite              func(pg connect.DatabaseExecutor, orgID uuid.UUID, tokenHash string, req CreateInviteRequest) (*UserInvite, error)
        GenerateToken             func(length uint) (string, error)
        HashString                func(input string) string
        GetOrganizationName       func(pg connect.DatabaseExecutor, orgID uuid.UUID) (string, error)
        RenderEmailTemplate       func(data EmailTemplateData) (string, error)
        PublishEmailNotification  func(ctx context.Context, pubsubClient connect.PsClient, topicName string, message PubSubEmailMessage) error
        LogInviteEvent            func(batcher bqbatch.Batcher, eventType string, invite *UserInvite, actor string) error
        GetInvitesForUser         func(pg connect.DatabaseExecutor, userID uuid.UUID) (*[]UserInvite, error)
        GetInvitesForOrganization func(pg connect.DatabaseExecutor, orgID uuid.UUID) (*[]UserInvite, error)
        UpdateInviteStatus        func(pg connect.DatabaseExecutor, inviteID uuid.UUID, status string, now *time.Time) error
        GetInviteByID             func(pg connect.DatabaseExecutor, inviteID uuid.UUID) (*UserInvite, error)
        UpdateInviteToken         func(pg connect.DatabaseExecutor, inviteID uuid.UUID, tokenHash string, retryCount int, req *ResendInviteRequest) error
        ValidateInviteToken       func(pg connect.DatabaseExecutor, token string) (*UserInvite, error)
}

const (
        inviteTokenLength = 64
        appName           = "Onramp"
        pubSubTopic       = pubsubdata.TopicETLNotifications

        // Statuses
        StatusPending  = "pending"
        StatusRedeemed = "redeemed"
        StatusRevoked  = "revoked"
        StatusExpired  = "expired"
        StatusRejected = "rejected"

        // Event types
        EventTypeCreate   = "create"
        EventTypeRetry    = "retry"
        EventTypeRevoke   = "revoke"
        EventTypeRedeem   = "redeem"
        EventTypeRejected = "rejected"
)

// CreateInviteHandlerWithDeps creates a new invite with dependency injection
func CreateInviteHandlerWithDeps(deps HandlerDeps) http.HandlerFunc <span class="cov8" title="1">{
        return func(w http.ResponseWriter, r *http.Request) </span><span class="cov8" title="1">{
                ctx := r.Context()

                // Get the connections
                connections, err := deps.GetConnections(ctx)
                if err != nil </span><span class="cov8" title="1">{
                        logger.Errorf("Error getting connections: %v", err)
                        response.CreateInternalErrorResponse(w)
                        return
                }</span>
                <span class="cov8" title="1">pg := connections.Postgres

                // Get BigQuery batcher
                batcher, err := deps.GetBQBatch(ctx)
                if err != nil </span><span class="cov8" title="1">{
                        logger.Errorf("Error getting BigQuery batcher: %v", err)
                        response.CreateInternalErrorResponse(w)
                        return
                }</span>

                // Validate organization ID
                <span class="cov8" title="1">orgID, err := helper.ValidateOrganizationID(r)
                if err != nil </span><span class="cov8" title="1">{
                        logger.Errorf("Error validating organization ID: %v", err)
                        response.CreateBadRequestResponse(w)
                        return
                }</span>

                // Get organization name
                <span class="cov8" title="1">orgName, err := deps.GetOrganizationName(pg, orgID)
                if err != nil </span><span class="cov8" title="1">{
                        logger.Errorf("Error getting organization name: %v", err)
                        response.CreateInternalErrorResponse(w)
                        return
                }</span>

                // Parse and validate request body
                <span class="cov8" title="1">var req CreateInviteRequest
                if err = json.NewDecoder(r.Body).Decode(&amp;req); err != nil </span><span class="cov8" title="1">{
                        logger.Errorf("Error decoding request body: %v", err)
                        response.CreateBadRequestResponse(w)
                        return
                }</span>

                // Validate required fields
                <span class="cov8" title="1">if err = validateCreateInviteRequest(&amp;req); err != nil </span><span class="cov8" title="1">{
                        logger.Errorf("Error validating request body: %v", err)
                        response.CreateBadRequestResponse(w)
                        return
                }</span>

                // Create invite token
                <span class="cov8" title="1">token, err := deps.GenerateToken(inviteTokenLength)
                if err != nil </span><span class="cov8" title="1">{
                        logger.Errorf("Error generating invite token: %v", err)
                        response.CreateInternalErrorResponse(w)
                        return
                }</span>

                // Hash the token
                <span class="cov8" title="1">tokenHash := deps.HashString(token)

                // Create invite
                invite, err := deps.CreateInvite(pg, orgID, tokenHash, req)
                if err != nil </span><span class="cov8" title="1">{
                        logger.Errorf("Error creating invite: %v", err)
                        response.CreateInternalErrorResponse(w)
                        return
                }</span>

                // Add to BigQuery
                <span class="cov8" title="1">if err = deps.LogInviteEvent(batcher, EventTypeCreate, invite, req.InviterID.String()); err != nil </span><span class="cov8" title="1">{
                        logger.Errorf("failed to log invite event: %v", err)
                        response.CreateInternalErrorResponse(w)
                        return
                }</span>
                // Render email template
                <span class="cov8" title="1">var messageText string
                if req.Message != nil </span><span class="cov8" title="1">{
                        messageText = *req.Message
                }</span>
                <span class="cov8" title="1">emailTemplateData := EmailTemplateData{
                        AppName:          appName,
                        Message:          messageText,
                        InviteLink:       buildInviteLink(orgID.String(), token),
                        OrganizationName: orgName,
                }

                htmlContent, err := deps.RenderEmailTemplate(emailTemplateData)
                if err != nil </span><span class="cov8" title="1">{
                        logger.Errorf("failed to render email template: %v", err)
                        response.CreateInternalErrorResponse(w)
                        return
                }</span>

                // Publish email notification
                <span class="cov8" title="1">message := PubSubEmailMessage{
                        Type:    "email",
                        To:      invite.Email,
                        Message: htmlContent,
                }
                if err := deps.PublishEmailNotification(ctx, connections.Pubsub, pubSubTopic, message); err != nil </span><span class="cov8" title="1">{
                        logger.Errorf("failed to publish email notification: %v", err)
                        response.CreateInternalErrorResponse(w)
                        return
                }</span>

                <span class="cov8" title="1">response.CreateSuccessResponse(toInviteResponse(invite), w)</span>
        }
}

// ListUserInvitesHandlerWithDeps lists all invites for an organization
func ListUserInvitesForUserHandlerWithDeps(deps HandlerDeps) http.HandlerFunc <span class="cov8" title="1">{
        return func(w http.ResponseWriter, r *http.Request) </span><span class="cov8" title="1">{
                ctx := r.Context()

                // Get the connections
                connections, err := deps.GetConnections(ctx)
                if err != nil </span><span class="cov8" title="1">{
                        logger.Errorf("Error getting connections: %v", err)
                        response.CreateInternalErrorResponse(w)
                        return
                }</span>
                <span class="cov8" title="1">pg := connections.Postgres

                // Validate user ID
                userID, err := helper.ValidateUserID(r)
                if err != nil </span><span class="cov8" title="1">{
                        logger.Errorf("Error validating user ID: %v", err)
                        response.CreateBadRequestResponse(w)
                        return
                }</span>

                // Get invites from user
                <span class="cov8" title="1">invites, err := deps.GetInvitesForUser(pg, userID)
                if err != nil </span><span class="cov8" title="1">{
                        logger.Errorf("Error getting invites for user: %v", err)
                        response.CreateInternalErrorResponse(w)
                        return
                }</span>

                // Convert to invite response
                <span class="cov8" title="1">inviteResponses := toInviteResponseSlice(invites)

                // Return success response
                response.CreateSuccessResponse(inviteResponses, w)</span>
        }
}

// ListUserInvitesForOrganizationHandlerWithDeps lists all invites for an organization
func ListUserInvitesForOrganizationHandlerWithDeps(deps HandlerDeps) http.HandlerFunc <span class="cov8" title="1">{
        return func(w http.ResponseWriter, r *http.Request) </span><span class="cov8" title="1">{
                ctx := r.Context()

                // Get the connections
                connections, err := deps.GetConnections(ctx)
                if err != nil </span><span class="cov8" title="1">{
                        logger.Errorf("Error getting connections: %v", err)
                        response.CreateInternalErrorResponse(w)
                        return
                }</span>
                <span class="cov8" title="1">pg := connections.Postgres

                // Validate organization ID
                orgID, err := helper.ValidateOrganizationID(r)
                if err != nil </span><span class="cov8" title="1">{
                        logger.Errorf("Error validating organization ID: %v", err)
                        response.CreateBadRequestResponse(w)
                        return
                }</span>

                // Validate organization exists and is not deleted
                <span class="cov8" title="1">_, err = deps.GetOrganizationName(pg, orgID)
                if err != nil </span><span class="cov8" title="1">{
                        logger.Errorf("Error getting organization name: %v", err)
                        response.CreateInternalErrorResponse(w)
                        return
                }</span>

                // Get invites from organization
                <span class="cov8" title="1">invites, err := deps.GetInvitesForOrganization(pg, orgID)
                if err != nil </span><span class="cov8" title="1">{
                        logger.Errorf("Error getting invites for organization: %v", err)
                        response.CreateInternalErrorResponse(w)
                        return
                }</span>

                // Convert to invite response
                <span class="cov8" title="1">inviteResponses := toInviteResponseSlice(invites)

                // Return success response
                response.CreateSuccessResponse(inviteResponses, w)</span>
        }
}

// RejectInviteHandlerWithDeps rejects an invite
func RejectInviteHandlerWithDeps(deps HandlerDeps) http.HandlerFunc <span class="cov8" title="1">{
        return func(w http.ResponseWriter, r *http.Request) </span><span class="cov8" title="1">{
                ctx := r.Context()

                // Get the connections
                connections, err := deps.GetConnections(ctx)
                if err != nil </span><span class="cov8" title="1">{
                        logger.Errorf("Error getting connections: %v", err)
                        response.CreateInternalErrorResponse(w)
                        return
                }</span>
                <span class="cov8" title="1">pg := connections.Postgres

                // Get batcher
                batcher, err := deps.GetBQBatch(ctx)
                if err != nil </span><span class="cov8" title="1">{
                        logger.Errorf("Error getting BigQuery batcher: %v", err)
                        response.CreateInternalErrorResponse(w)
                        return
                }</span>

                // Validate user ID
                <span class="cov8" title="1">userID, err := helper.ValidateUserID(r)
                if err != nil </span><span class="cov8" title="1">{
                        logger.Errorf("Error validating user ID: %v", err)
                        response.CreateBadRequestResponse(w)
                        return
                }</span>

                // Validate invite ID
                <span class="cov8" title="1">inviteID, err := helper.ValidateInviteID(r)
                if err != nil </span><span class="cov8" title="1">{
                        logger.Errorf("Error validating invite ID: %v", err)
                        response.CreateBadRequestResponse(w)
                        return
                }</span>

                // Get invite by invite ID
                <span class="cov8" title="1">invite, err := deps.GetInviteByID(pg, inviteID)
                if err != nil </span><span class="cov8" title="1">{
                        logger.Errorf("Error getting invite by ID: %v", err)
                        response.CreateInternalErrorResponse(w)
                        return
                }</span>

                // Time now
                <span class="cov8" title="1">now := time.Now().UTC()

                // Reject invite
                if err := deps.UpdateInviteStatus(pg, inviteID, StatusRejected, &amp;now); err != nil </span><span class="cov8" title="1">{
                        if err == ErrInviteNotFound </span><span class="cov8" title="1">{
                                logger.Errorf("Invite not found: %v", err)
                                response.CreateNotFoundResponse(w)
                        }</span> else<span class="cov8" title="1"> {
                                logger.Errorf("Error rejecting invite: %v", err)
                                response.CreateInternalErrorResponse(w)
                        }</span>
                        <span class="cov8" title="1">return</span>
                }

                // Update invite status for logging
                <span class="cov8" title="1">invite.Status = StatusRejected
                invite.Updated = now

                // Log invite event
                if err := deps.LogInviteEvent(batcher, EventTypeRejected, invite, userID.String()); err != nil </span><span class="cov8" title="1">{
                        logger.Errorf("failed to log invite event: %v", err)
                        response.CreateInternalErrorResponse(w)
                        return
                }</span>
                // Return success response
                <span class="cov8" title="1">response.CreateSuccessResponse(toInviteResponse(invite), w)</span>
        }
}

// RevokeInviteHandlerWithDeps revokes an invite
func RevokeInviteHandlerWithDeps(deps HandlerDeps) http.HandlerFunc <span class="cov8" title="1">{
        return func(w http.ResponseWriter, r *http.Request) </span><span class="cov8" title="1">{
                ctx := r.Context()

                // Get the connections
                connections, err := deps.GetConnections(ctx)
                if err != nil </span><span class="cov8" title="1">{
                        logger.Errorf("Error getting connections: %v", err)
                        response.CreateInternalErrorResponse(w)
                        return
                }</span>
                <span class="cov8" title="1">pg := connections.Postgres

                // Get batcher
                batcher, err := deps.GetBQBatch(ctx)
                if err != nil </span><span class="cov8" title="1">{
                        logger.Errorf("Error getting BigQuery batcher: %v", err)
                        response.CreateInternalErrorResponse(w)
                        return
                }</span>

                // Validate organization ID
                <span class="cov8" title="1">orgID, err := helper.ValidateOrganizationID(r)
                if err != nil </span><span class="cov8" title="1">{
                        logger.Errorf("Error validating organization ID: %v", err)
                        response.CreateBadRequestResponse(w)
                        return
                }</span>

                // Validate invite ID
                <span class="cov8" title="1">inviteID, err := helper.ValidateInviteID(r)
                if err != nil </span><span class="cov8" title="1">{
                        logger.Errorf("Error validating invite ID: %v", err)
                        response.CreateBadRequestResponse(w)
                        return
                }</span>

                // Get invite by invite ID
                <span class="cov8" title="1">invite, err := deps.GetInviteByID(pg, inviteID)
                if err != nil </span><span class="cov8" title="1">{
                        if err == ErrInviteNotFound </span><span class="cov8" title="1">{
                                logger.Errorf("Invite not found: %v", err)
                                response.CreateNotFoundResponse(w)
                        }</span> else<span class="cov8" title="1"> {
                                logger.Errorf("Error getting invite by ID: %v", err)
                                response.CreateInternalErrorResponse(w)
                        }</span>
                        <span class="cov8" title="1">return</span>
                }
                // Parse request body
                <span class="cov8" title="1">var req RevokeInviteRequest
                if err = json.NewDecoder(r.Body).Decode(&amp;req); err != nil </span><span class="cov0" title="0">{
                        logger.Errorf("Error decoding request body: %v", err)
                        response.CreateBadRequestResponse(w)
                        return
                }</span>

                // Validate actor
                <span class="cov8" title="1">if req.Actor == "" </span><span class="cov8" title="1">{
                        logger.Errorf("Actor is required")
                        response.CreateBadRequestResponse(w)
                        return
                }</span>

                // Verify invite belongs to the organization
                <span class="cov8" title="1">if invite.OrganizationIdentifier != orgID </span><span class="cov8" title="1">{
                        logger.Errorf("Invite does not belong to the organization: %v", err)
                        response.CreateBadRequestResponse(w)
                        return
                }</span>

                // Time now
                <span class="cov8" title="1">now := time.Now().UTC()

                // Revoke invite
                if err := deps.UpdateInviteStatus(pg, inviteID, StatusRevoked, &amp;now); err != nil </span><span class="cov8" title="1">{
                        logger.Errorf("Error revoking invite: %v", err)
                        response.CreateInternalErrorResponse(w)
                        return
                }</span>

                // Update invite status for logging
                <span class="cov8" title="1">invite.Status = StatusRevoked
                invite.Updated = now

                // Log invite event
                if err := deps.LogInviteEvent(batcher, EventTypeRevoke, invite, req.Actor); err != nil </span><span class="cov8" title="1">{
                        logger.Errorf("failed to log invite event: %v", err)
                        response.CreateInternalErrorResponse(w)
                        return
                }</span>

                // Return success response
                <span class="cov8" title="1">response.CreateSuccessResponse(toInviteResponse(invite), w)</span>
        }
}

// ResendInviteHandlerWithDeps resends an invite
func ResendInviteHandlerWithDeps(deps HandlerDeps) http.HandlerFunc <span class="cov8" title="1">{
        return func(w http.ResponseWriter, r *http.Request) </span><span class="cov8" title="1">{
                ctx := r.Context()

                // Get the connections
                connections, err := deps.GetConnections(ctx)
                if err != nil </span><span class="cov8" title="1">{
                        logger.Errorf("Error getting connections: %v", err)
                        response.CreateInternalErrorResponse(w)
                        return
                }</span>
                <span class="cov8" title="1">pg := connections.Postgres

                // Get batcher
                batcher, err := deps.GetBQBatch(ctx)
                if err != nil </span><span class="cov8" title="1">{
                        logger.Errorf("Error getting BigQuery batcher: %v", err)
                        response.CreateInternalErrorResponse(w)
                        return
                }</span>

                // Validate organization ID
                <span class="cov8" title="1">orgID, err := helper.ValidateOrganizationID(r)
                if err != nil </span><span class="cov8" title="1">{
                        logger.Errorf("Error validating organization ID: %v", err)
                        response.CreateBadRequestResponse(w)
                        return
                }</span>

                // Validate invite ID
                <span class="cov8" title="1">inviteID, err := helper.ValidateInviteID(r)
                if err != nil </span><span class="cov8" title="1">{
                        logger.Errorf("Error validating invite ID: %v", err)
                        response.CreateBadRequestResponse(w)
                        return
                }</span>

                // Validate request body
                <span class="cov8" title="1">var req ResendInviteRequest
                if err = json.NewDecoder(r.Body).Decode(&amp;req); err != nil </span><span class="cov8" title="1">{
                        logger.Errorf("Error decoding request body: %v", err)
                        response.CreateBadRequestResponse(w)
                        return
                }</span>

                // Get organization name
                <span class="cov8" title="1">orgName, err := deps.GetOrganizationName(pg, orgID)
                if err != nil </span><span class="cov8" title="1">{
                        logger.Errorf("Error getting organization name: %v", err)
                        response.CreateInternalErrorResponse(w)
                        return
                }</span>

                // Validate actor
                <span class="cov8" title="1">if req.Actor == "" </span><span class="cov8" title="1">{
                        logger.Errorf("Actor is required")
                        response.CreateBadRequestResponse(w)
                        return
                }</span>

                // Get invite by invite ID
                <span class="cov8" title="1">invite, err := deps.GetInviteByID(pg, inviteID)
                if err != nil </span><span class="cov8" title="1">{
                        if err == ErrInviteNotFound </span><span class="cov8" title="1">{
                                logger.Errorf("Invite not found: %v", err)
                                response.CreateNotFoundResponse(w)
                        }</span> else<span class="cov8" title="1"> {
                                logger.Errorf("Error getting invite by ID: %v", err)
                                response.CreateInternalErrorResponse(w)
                        }</span>
                        <span class="cov8" title="1">return</span>
                }

                // Verify invite belongs to the organization
                <span class="cov8" title="1">if invite.OrganizationIdentifier != orgID </span><span class="cov8" title="1">{
                        logger.Errorf("Invite does not belong to the organization: %v", err)
                        response.CreateBadRequestResponse(w)
                        return
                }</span>

                // Check cooldown period
                <span class="cov8" title="1">if err = checkCooldownPeriod(invite); err != nil </span><span class="cov8" title="1">{
                        if err == ErrResendCooldown </span><span class="cov8" title="1">{
                                logger.Errorf("Resend cooldown period not passed: %v", err)
                                response.CreateCustomErrorResponse("Must wait 1 minute before resending", nil, http.StatusTooEarly, w)
                        }</span> else<span class="cov0" title="0"> {
                                logger.Errorf("Error checking cooldown period: %v", err)
                                response.CreateInternalErrorResponse(w)
                        }</span>
                        <span class="cov8" title="1">return</span>
                }

                // Create invite token
                <span class="cov8" title="1">token, err := deps.GenerateToken(inviteTokenLength)
                if err != nil </span><span class="cov8" title="1">{
                        logger.Errorf("Error generating invite token: %v", err)
                        response.CreateInternalErrorResponse(w)
                        return
                }</span>

                // Hash the token
                <span class="cov8" title="1">tokenHash := deps.HashString(token)

                // Update invite status for logging
                invite.Status = StatusPending
                invite.TokenHash = tokenHash
                invite.RetryCount++

                // Update invite with new token
                if err = deps.UpdateInviteToken(pg, inviteID, tokenHash, invite.RetryCount, &amp;req); err != nil </span><span class="cov8" title="1">{
                        logger.Errorf("Error updating invite token: %v", err)
                        response.CreateInternalErrorResponse(w)
                        return
                }</span>

                // Log invite event
                <span class="cov8" title="1">if err = deps.LogInviteEvent(batcher, EventTypeRetry, invite, req.Actor); err != nil </span><span class="cov8" title="1">{
                        logger.Errorf("failed to log invite event: %v", err)
                        response.CreateInternalErrorResponse(w)
                        return
                }</span>

                // Render email template
                <span class="cov8" title="1">var messageText string
                if req.Message != nil </span><span class="cov8" title="1">{
                        messageText = *req.Message
                }</span>
                <span class="cov8" title="1">emailTemplateData := EmailTemplateData{
                        AppName:          appName,
                        Message:          messageText,
                        InviteLink:       buildInviteLink(orgID.String(), token),
                        OrganizationName: orgName,
                }

                htmlContent, err := deps.RenderEmailTemplate(emailTemplateData)
                if err != nil </span><span class="cov8" title="1">{
                        logger.Errorf("failed to render email template: %v", err)
                        response.CreateInternalErrorResponse(w)
                        return
                }</span>

                // Publish email notification
                <span class="cov8" title="1">message := PubSubEmailMessage{
                        Type:    "email",
                        To:      invite.Email,
                        Message: htmlContent,
                }
                if err := deps.PublishEmailNotification(ctx, connections.Pubsub, pubSubTopic, message); err != nil </span><span class="cov8" title="1">{
                        logger.Errorf("failed to publish email notification: %v", err)
                        response.CreateInternalErrorResponse(w)
                        return
                }</span>

                // Return success response
                <span class="cov8" title="1">responseData := map[string]interface{}{
                        "id":     invite.ID,
                        "email":  invite.Email,
                        "status": invite.Status,
                }

                // Return success response
                response.CreateSuccessResponse(responseData, w)</span>
        }
}

// ValidateInviteHandlerWithDeps validates an invite
func ValidateInviteHandlerWithDeps(deps HandlerDeps) http.HandlerFunc <span class="cov8" title="1">{
        return func(w http.ResponseWriter, r *http.Request) </span><span class="cov8" title="1">{
                ctx := r.Context()

                // Get the connections
                connections, err := deps.GetConnections(ctx)
                if err != nil </span><span class="cov8" title="1">{
                        logger.Errorf("Error getting connections: %v", err)
                        response.CreateInternalErrorResponse(w)
                        return
                }</span>
                <span class="cov8" title="1">pg := connections.Postgres

                // Validate user ID
                userID, err := helper.ValidateUserID(r)
                if err != nil </span><span class="cov8" title="1">{
                        logger.Errorf("Error validating user ID: %v", err)
                        response.CreateBadRequestResponse(w)
                        return
                }</span>

                // Get token from query parameters
                <span class="cov8" title="1">token := r.URL.Query().Get("token")
                if token == "" </span><span class="cov8" title="1">{
                        logger.Errorf("Error getting token from query parameters: %v", err)
                        response.CreateBadRequestResponse(w)
                        return
                }</span>

                // Validate token
                <span class="cov8" title="1">invite, err := deps.ValidateInviteToken(pg, token)
                if err != nil </span><span class="cov8" title="1">{
                        logger.Errorf("Error validating invite token: %v", err)
                        response.CreateUnauthorizedResponse(w)
                        return
                }</span>

                // Validate user ID with invite
                <span class="cov8" title="1">if invite.InviterID != userID </span><span class="cov8" title="1">{
                        logger.Errorf("User ID does not match invite: %v", err)
                        response.CreateUnauthorizedResponse(w)
                        return
                }</span>

                // Return success response
                <span class="cov8" title="1">response.CreateSuccessResponse(toInviteResponse(invite), w)</span>
        }
}

// RedeemInviteHandlerWithDeps redeems an invite
func RedeemInviteHandlerWithDeps(deps HandlerDeps) http.HandlerFunc <span class="cov8" title="1">{
        return func(w http.ResponseWriter, r *http.Request) </span><span class="cov8" title="1">{
                ctx := r.Context()

                // Get the connections
                connections, err := deps.GetConnections(ctx)
                if err != nil </span><span class="cov8" title="1">{
                        logger.Errorf("Error getting connections: %v", err)
                        response.CreateInternalErrorResponse(w)
                        return
                }</span>
                <span class="cov8" title="1">pg := connections.Postgres

                // Get batcher
                batcher, err := deps.GetBQBatch(ctx)
                if err != nil </span><span class="cov8" title="1">{
                        logger.Errorf("Error getting BigQuery batcher: %v", err)
                        response.CreateInternalErrorResponse(w)
                        return
                }</span>

                // Validate user ID
                <span class="cov8" title="1">userID, err := helper.ValidateUserID(r)
                if err != nil </span><span class="cov8" title="1">{
                        logger.Errorf("Error validating user ID: %v", err)
                        response.CreateBadRequestResponse(w)
                        return
                }</span>

                // Validate invite ID
                <span class="cov8" title="1">inviteID, err := helper.ValidateInviteID(r)
                if err != nil </span><span class="cov8" title="1">{
                        logger.Errorf("Error validating invite ID: %v", err)
                        response.CreateBadRequestResponse(w)
                        return
                }</span>

                // Get invite by invite ID
                <span class="cov8" title="1">invite, err := deps.GetInviteByID(pg, inviteID)
                if err != nil </span><span class="cov8" title="1">{
                        logger.Errorf("Error getting invite by ID: %v", err)
                        response.CreateInternalErrorResponse(w)
                        return
                }</span>

                // Verify invite is in pending status
                <span class="cov8" title="1">if invite.Status != StatusPending </span><span class="cov8" title="1">{
                        response.CreateBadRequestResponse(w)
                        return
                }</span>

                // Time now
                <span class="cov8" title="1">now := time.Now().UTC()

                // Update invite status to redeemed
                if err := deps.UpdateInviteStatus(pg, inviteID, StatusRedeemed, &amp;now); err != nil </span><span class="cov8" title="1">{
                        logger.Errorf("Error updating invite status: %v", err)
                        response.CreateInternalErrorResponse(w)
                        return
                }</span>

                // Update invite status for logging
                <span class="cov8" title="1">invite.Status = StatusRedeemed
                invite.Updated = now

                // Log invite event
                if err := deps.LogInviteEvent(batcher, EventTypeRedeem, invite, userID.String()); err != nil </span><span class="cov8" title="1">{
                        logger.Errorf("failed to log invite event: %v", err)
                        response.CreateInternalErrorResponse(w)
                        return
                }</span>

                // Return success response
                <span class="cov8" title="1">response.CreateSuccessResponse(toInviteResponse(invite), w)</span>
        }
}

// validateInviteToken validates an invite token
func validateInviteToken(pg connect.DatabaseExecutor, token string) (*UserInvite, error) <span class="cov8" title="1">{
        // Validate token format
        if !validateTokenFormat(token) </span><span class="cov8" title="1">{
                return nil, ErrInvalidInviteToken
        }</span>

        <span class="cov8" title="1">tokenHash := security.CalculateSHA256(token)

        query := `
                SELECT
                        id,
                        organizationidentifier,
                        tokenhash,
                        email,
                        inviterid,
                        customroleid,
                        status,
                        message,
                        requiresso,
                        retrycount,
                        retried,
                        expired,
                        created,
                        sent,
                        updated
                FROM {{UserInvites}}
                WHERE tokenhash = $1
        `
        invite := &amp;UserInvite{}
        err := pg.QueryRowStruct(invite, query, tokenHash)
        if err != nil </span><span class="cov8" title="1">{
                if err == sql.ErrNoRows </span><span class="cov8" title="1">{
                        return nil, ErrInviteNotFound
                }</span>
                <span class="cov8" title="1">logger.Errorf("failed to validate invite token: %v", err)
                return nil, ErrInviteNotFound</span>
        }

        // Check if the invite is expired
        <span class="cov8" title="1">if invite.Expired != nil &amp;&amp; invite.Expired.Before(time.Now().UTC()) </span><span class="cov8" title="1">{
                return nil, ErrInviteExpired
        }</span>

        <span class="cov8" title="1">return invite, nil</span>
}

// updateInviteToken updates the invite token
func updateInviteToken(pg connect.DatabaseExecutor, inviteID uuid.UUID, tokenHash string, retryCount int, req *ResendInviteRequest) error <span class="cov8" title="1">{
        // Time now
        now := time.Now().UTC()

        // Parse expired days
        var expired *time.Time
        if req.ExpiredDays != nil &amp;&amp; *req.ExpiredDays &gt; 0 </span><span class="cov0" title="0">{
                exp := time.Now().UTC().AddDate(0, 0, *req.ExpiredDays)
                expired = &amp;exp
        }</span> else<span class="cov8" title="1"> {
                expired = nil
        }</span>

        // Parse message
        <span class="cov8" title="1">var message *string
        if req.Message != nil </span><span class="cov8" title="1">{
                message = req.Message
        }</span>

        // Build query
        <span class="cov8" title="1">query := `
                UPDATE {{UserInvites}}
                SET 
                        tokenhash = $1,
                        retrycount = $2,
                        retried = $3,
                        updated = $3,
                        expired = $4,
                        message = $5
                WHERE id = $6
        `

        // Execute query
        result, err := pg.Exec(query, tokenHash, retryCount, now, expired, message, inviteID)
        if err != nil </span><span class="cov8" title="1">{
                logger.Errorf("failed to update invite token: %v", err)
                return ErrDatabaseOperation
        }</span>

        // Check if the update was successful
        <span class="cov8" title="1">rowsAffected, err := result.RowsAffected()
        if err != nil </span><span class="cov0" title="0">{
                logger.Errorf("failed to get rows affected: %v", err)
                return ErrDatabaseOperation
        }</span>

        <span class="cov8" title="1">if rowsAffected == 0 </span><span class="cov8" title="1">{
                return ErrInviteNotFound
        }</span>

        <span class="cov8" title="1">return nil</span>
}

// createInvite creates a new invite
func createInvite(pg connect.DatabaseExecutor, orgID uuid.UUID, tokenHash string, req CreateInviteRequest) (*UserInvite, error) <span class="cov8" title="1">{
        // Time now
        now := time.Now().UTC()

        // Parse expired days
        var expired *time.Time
        if req.ExpiredDays != nil &amp;&amp; *req.ExpiredDays &gt; 0 </span><span class="cov8" title="1">{
                exp := time.Now().UTC().AddDate(0, 0, *req.ExpiredDays)
                expired = &amp;exp
        }</span> else<span class="cov8" title="1"> {
                expired = nil
        }</span>

        // Parse message
        <span class="cov8" title="1">var message *string
        if req.Message != nil </span><span class="cov0" title="0">{
                message = req.Message
        }</span>

        // Build query
        <span class="cov8" title="1">query := `
                INSERT INTO {{UserInvites}} (
                                organizationidentifier,
                                tokenhash,
                                email,
                                inviterid,
                                customroleid,
                                status,
                                message,
                                requiresso,
                                retrycount,
                                retried,
                                expired,
                                created,
                                sent,
                                updated
                        ) VALUES (
                                $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14
                        )
                        RETURNING id`

        // Execute query
        row, err := pg.QueryRow(
                query,
                orgID,
                tokenHash,
                req.Email,
                req.InviterID,
                req.OrganizationRole,
                StatusPending,
                message,
                false,
                0,   // retrycount
                nil, // retried
                expired,
                now,
                nil, // sent
                now,
        )
        if err != nil </span><span class="cov8" title="1">{
                logger.Errorf("failed to create invite: %v", err)
                return nil, ErrDatabaseOperation
        }</span>

        // Scan the result
        <span class="cov8" title="1">inviteIDInterface, ok := row["id"]
        if !ok </span><span class="cov8" title="1">{
                return nil, ErrInvalidInviteID
        }</span>

        // Convert string to UUID
        <span class="cov8" title="1">inviteIDStr, ok := inviteIDInterface.(string)
        if !ok </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("ID column is not a string: %T", inviteIDInterface)
        }</span>

        <span class="cov8" title="1">inviteID, err := uuid.Parse(inviteIDStr)
        if err != nil </span><span class="cov8" title="1">{
                return nil, fmt.Errorf("failed to parse invite ID as UUID: %w", err)
        }</span>

        // Create invite
        <span class="cov8" title="1">invite := &amp;UserInvite{
                ID:                     inviteID,
                OrganizationIdentifier: orgID,
                TokenHash:              tokenHash,
                Email:                  req.Email,
                InviterID:              req.InviterID,
                CustomRoleID:           req.OrganizationRole,
                Status:                 StatusPending,
                Message:                message,
                RequireSSO:             false,
                RetryCount:             0,
                Retried:                nil,
                Expired:                expired,
                Created:                now,
                Sent:                   nil,
                Updated:                now,
        }
        return invite, nil</span>
}

// getInvitesForUser gets all invites for a user
func getInvitesForUser(pg connect.DatabaseExecutor, userID uuid.UUID) (*[]UserInvite, error) <span class="cov8" title="1">{
        // Get email address for user from AuthMethod
        emailQuery := `SELECT email FROM {{AuthMethod}} WHERE id = $1 AND isdeleted = false AND email IS NOT NULL`

        row, err := pg.QueryRow(emailQuery, userID)
        if err != nil </span><span class="cov8" title="1">{
                // If no email address found, return error
                if err == sql.ErrNoRows </span><span class="cov8" title="1">{
                        return nil, ErrNoUserEmail
                }</span>
                <span class="cov8" title="1">logger.Errorf("failed to get email for user: %v", err)
                return nil, ErrDatabaseOperation</span>
        }

        <span class="cov8" title="1">email, ok := row["email"]
        if !ok </span><span class="cov8" title="1">{
                return nil, ErrInvalidRowData
        }</span>

        <span class="cov8" title="1">query := `
                SELECT
                        id,
                        organizationidentifier,
                        tokenhash,
                        email,
                        inviterid,
                        customroleid,
                        status,
                        message,
                        requiresso,
                        retrycount,
                        retried,
                        expired,
                        created,
                        sent,
                        updated
                FROM {{UserInvites}}
                WHERE email = $1
        `
        var invites []UserInvite
        err = pg.QueryGenericSlice(&amp;invites, query, email)
        if err != nil </span><span class="cov8" title="1">{
                if err == sql.ErrNoRows </span><span class="cov0" title="0">{
                        return nil, ErrNoUserEmail
                }</span>
                <span class="cov8" title="1">logger.Errorf("failed to get email for user: %v", err)
                return nil, ErrDatabaseOperation</span>
        }

        <span class="cov8" title="1">return &amp;invites, nil</span>
}

// getInvitesForOrganization gets all invites for an organization
func getInvitesForOrganization(pg connect.DatabaseExecutor, orgID uuid.UUID) (*[]UserInvite, error) <span class="cov8" title="1">{
        query := `
                SELECT
                        id,
                        organizationidentifier,
                        tokenhash,
                        email,
                        inviterid,
                        customroleid,
                        status,
                        message,
                        requiresso,
                        retrycount,
                        retried,
                        expired,
                        created,
                        sent,
                        updated
                FROM {{UserInvites}}
                WHERE organizationidentifier = $1
        `
        var invites []UserInvite
        err := pg.QueryGenericSlice(&amp;invites, query, orgID)
        if err != nil </span><span class="cov8" title="1">{
                if err == sql.ErrNoRows </span><span class="cov0" title="0">{
                        return nil, ErrNoInvitesFoundForOrganization
                }</span>
                <span class="cov8" title="1">logger.Errorf("failed to get invites from organization: %v", err)
                return nil, ErrDatabaseOperation</span>
        }
        <span class="cov8" title="1">return &amp;invites, nil</span>
}

// updateInviteStatus updates the status of an invite
func updateInviteStatus(pg connect.DatabaseExecutor, inviteID uuid.UUID, status string, now *time.Time) error <span class="cov8" title="1">{
        query := `
                UPDATE {{UserInvites}}
                SET status = $1, updated = $2
                WHERE id = $3
        `
        result, err := pg.Exec(query, status, now.UTC(), inviteID)
        if err != nil </span><span class="cov8" title="1">{
                logger.Errorf("failed to update invite status: %v", err)
                return ErrDatabaseOperation
        }</span>

        // Check if the update was successful
        <span class="cov8" title="1">rowsAffected, err := result.RowsAffected()
        if err != nil </span><span class="cov0" title="0">{
                logger.Errorf("failed to get rows affected: %v", err)
                return ErrDatabaseOperation
        }</span>
        <span class="cov8" title="1">if rowsAffected == 0 </span><span class="cov8" title="1">{
                return ErrInviteNotFound
        }</span>

        <span class="cov8" title="1">return nil</span>
}

// getInviteByID gets an invite by invite ID
func getInviteByID(pg connect.DatabaseExecutor, inviteID uuid.UUID) (*UserInvite, error) <span class="cov8" title="1">{
        query := `
                SELECT
                        id,
                        organizationidentifier,
                        tokenhash,
                        email,
                        inviterid,
                        customroleid,
                        status,
                        message,
                        requiresso,
                        retrycount,
                        retried,
                        expired,
                        created,
                        sent,
                        updated
                FROM {{UserInvites}}
                WHERE id = $1
        `
        invite := &amp;UserInvite{}
        err := pg.QueryRowStruct(invite, query, inviteID)
        if errors.Is(err, sql.ErrNoRows) </span><span class="cov8" title="1">{
                return nil, ErrInviteNotFound
        }</span>
        <span class="cov8" title="1">if err != nil </span><span class="cov8" title="1">{
                return nil, ErrDatabaseOperation
        }</span>
        <span class="cov8" title="1">return invite, nil</span>
}

// Convert to invite response
func toInviteResponse(invite *UserInvite) *InviteResponse <span class="cov8" title="1">{
        return &amp;InviteResponse{
                ID:                     invite.ID,
                OrganizationIdentifier: invite.OrganizationIdentifier,
                Email:                  invite.Email,
                InviterID:              invite.InviterID,
                CustomRoleID:           invite.CustomRoleID,
                Status:                 invite.Status,
                Message:                invite.Message,
                RequireSSO:             invite.RequireSSO,
                RetryCount:             invite.RetryCount,
                Retried:                invite.Retried,
                Expired:                invite.Expired,
                Created:                invite.Created,
                Sent:                   invite.Sent,
                Updated:                invite.Updated,
        }
}</span>

// validateCreateInviteRequest validates the create invite request
func validateCreateInviteRequest(req *CreateInviteRequest) error <span class="cov8" title="1">{
        // Validate email
        if req.Email == "" </span><span class="cov8" title="1">{
                return ErrInvalidEmail
        }</span>

        <span class="cov8" title="1">if _, err := mail.ParseAddress(req.Email); err != nil </span><span class="cov8" title="1">{
                return ErrInvalidEmail
        }</span>

        // Validate inviter ID
        <span class="cov8" title="1">if req.InviterID == uuid.Nil </span><span class="cov8" title="1">{
                return ErrInvalidUserID
        }</span>

        // Validate organization role
        <span class="cov8" title="1">if req.OrganizationRole == uuid.Nil </span><span class="cov8" title="1">{
                return ErrInvalidRequestBody
        }</span>

        <span class="cov8" title="1">return nil</span>
}

// buildInviteLink constructs the invite validation link
var buildInviteLink = func(orgID, token string) string <span class="cov8" title="1">{
        return fmt.Sprintf("/api/organization/%s/invites/validate?token=%s", orgID, token)
}</span>

// validateTokenFormat checks if a token has the correct format (64 hex characters)
var validateTokenFormat = func(token string) bool <span class="cov8" title="1">{
        if len(token) != 64 </span><span class="cov8" title="1">{
                return false
        }</span>

        // Check if it's valid hexadecimal
        <span class="cov8" title="1">matched, _ := regexp.MatchString("^[a-fA-F0-9]{64}$", token)
        return matched</span>
}

// Convert to invite response slice
var toInviteResponseSlice = func(invites *[]UserInvite) *[]InviteResponse <span class="cov8" title="1">{
        inviteResponses := make([]InviteResponse, len(*invites))
        for i, invite := range *invites </span><span class="cov8" title="1">{
                inviteResponses[i] = *toInviteResponse(&amp;invite)
        }</span>
        <span class="cov8" title="1">return &amp;inviteResponses</span>
}

// checkCooldownPeriod checks if the cooldown period has passed
var checkCooldownPeriod = func(invite *UserInvite) error <span class="cov8" title="1">{
        // Check if the invite has been sent
        if invite.Retried == nil </span><span class="cov8" title="1">{
                return nil
        }</span>

        // Set cooldown period (1 minute)
        <span class="cov8" title="1">cooldownPeriod := time.Minute
        timeSinceRetry := time.Now().UTC().Sub(*invite.Retried)

        // Check if the cooldown period has passed
        if timeSinceRetry &lt; cooldownPeriod </span><span class="cov8" title="1">{
                return ErrResendCooldown
        }</span>

        <span class="cov8" title="1">return nil</span>
}

// getOrganizationName retrieves the organization name from the database
var getOrganizationName = func(pg connect.DatabaseExecutor, orgID uuid.UUID) (string, error) <span class="cov8" title="1">{
        query := `SELECT name FROM {{Organization}} WHERE id = $1 AND isdeleted = false`

        row, err := pg.QueryRow(query, orgID)
        if err != nil </span><span class="cov8" title="1">{
                if err == sql.ErrNoRows </span><span class="cov8" title="1">{
                        return "", ErrOrganizationNotFound
                }</span>
                <span class="cov8" title="1">logger.Errorf("failed to get organization name: %v", err)
                return "", fmt.Errorf("%w: %v", ErrDatabaseOperation, err)</span>
        }

        <span class="cov8" title="1">nameInterface, ok := row["name"]
        if !ok </span><span class="cov8" title="1">{
                return "", ErrInvalidRowData
        }</span>

        <span class="cov8" title="1">name, ok := nameInterface.(string)
        if !ok </span><span class="cov0" title="0">{
                return "", ErrInvalidRowData
        }</span>

        <span class="cov8" title="1">return name, nil</span>
}

// logInviteEvent logs an invite event to BigQuery
var logInviteEvent = func(batcher bqbatch.Batcher, eventType string, invite *UserInvite, actor string) error <span class="cov8" title="1">{
        // Convert *string to bigquery.NullString
        var message bigquery.NullString
        if invite.Message != nil </span><span class="cov8" title="1">{
                message.StringVal = *invite.Message
                message.Valid = true
        }</span>

        // Handle nil time pointers by providing zero values
        <span class="cov8" title="1">var retriedTime, expiredTime, sentTime time.Time
        if invite.Retried != nil </span><span class="cov0" title="0">{
                retriedTime = *invite.Retried
        }</span>
        <span class="cov8" title="1">if invite.Expired != nil </span><span class="cov0" title="0">{
                expiredTime = *invite.Expired
        }</span>
        <span class="cov8" title="1">if invite.Sent != nil </span><span class="cov0" title="0">{
                sentTime = *invite.Sent
        }</span>

        <span class="cov8" title="1">event := &amp;schemas.InviteEvent{
                UserInviteID:           invite.ID.String(),
                EventType:              eventType,
                Actor:                  actor,
                EventTime:              time.Now().UTC(),
                OrganizationIdentifier: invite.OrganizationIdentifier.String(),
                TokenHash:              invite.TokenHash,
                Email:                  invite.Email,
                InviterID:              invite.InviterID.String(),
                OrganizationRole:       invite.CustomRoleID.String(),
                Status:                 invite.Status,
                Message:                message,
                RequireSSO:             invite.RequireSSO,
                RetryCount:             int64(invite.RetryCount),
                Retried:                retriedTime,
                Expired:                expiredTime,
                Created:                invite.Created,
                Sent:                   sentTime,
                Updated:                invite.Updated,
        }

        if err := batcher.Add(event); err != nil </span><span class="cov0" title="0">{
                logger.Errorf("failed to log invite event: %v", err)
                return ErrBigQueryOperation
        }</span>

        <span class="cov8" title="1">return nil</span>
}

// publishEmailNotification publishes an email notification to PubSub
var publishEmailNotification = func(ctx context.Context, pubsubClient connect.PsClient, topicName string, message PubSubEmailMessage) error <span class="cov0" title="0">{
        // Marshal the message
        jsonData, err := json.Marshal(message)
        if err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to marshal email message: %v", err)
        }</span>

        // Build attributes
        <span class="cov0" title="0">attributes := pubsubdata.BuildAttributes(
                pubsubdata.CommonAttributes{
                        Topic: topicName,
                },
                pubsubdata.HeaderDetails{})

        topic := pubsubClient.Topic(pubSubTopic)
        pubsubMsg := &amp;pubsub.Message{
                Data:       jsonData,
                Attributes: attributes,
        }
        result := topic.Publish(ctx, pubsubMsg)
        if _, err := result.Get(ctx); err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to publish email message: %v", err)
        }</span>

        <span class="cov0" title="0">logger.Infof("Published email notification to PubSub: %s", string(jsonData))

        return nil</span>
}

var CreateInviteHandler = CreateInviteHandlerWithDeps(HandlerDeps{
        GetConnections:           connect.GetConnections,
        GetBQBatch:               bqbatch.GetBatch,
        CreateInvite:             createInvite,
        GenerateToken:            helper.GenerateRandomTokenHex,
        HashString:               security.CalculateSHA256,
        GetOrganizationName:      getOrganizationName,
        RenderEmailTemplate:      renderEmailTemplate,
        LogInviteEvent:           logInviteEvent,
        PublishEmailNotification: publishEmailNotification,
})

var ListUserInvitesForUserHandler = ListUserInvitesForUserHandlerWithDeps(HandlerDeps{
        GetConnections:    connect.GetConnections,
        GetInvitesForUser: getInvitesForUser,
})

var ListUserInvitesForOrganizationHandler = ListUserInvitesForOrganizationHandlerWithDeps(HandlerDeps{
        GetConnections:            connect.GetConnections,
        GetInvitesForOrganization: getInvitesForOrganization,
        GetOrganizationName:       getOrganizationName,
})

var RejectInviteHandler = RejectInviteHandlerWithDeps(HandlerDeps{
        GetConnections:     connect.GetConnections,
        GetBQBatch:         bqbatch.GetBatch,
        UpdateInviteStatus: updateInviteStatus,
        GetInviteByID:      getInviteByID,
        LogInviteEvent:     logInviteEvent,
})

var RevokeInviteHandler = RevokeInviteHandlerWithDeps(HandlerDeps{
        GetConnections:     connect.GetConnections,
        GetBQBatch:         bqbatch.GetBatch,
        UpdateInviteStatus: updateInviteStatus,
        GetInviteByID:      getInviteByID,
        LogInviteEvent:     logInviteEvent,
})

var ResendInviteHandler = ResendInviteHandlerWithDeps(HandlerDeps{
        GetConnections:           connect.GetConnections,
        GetBQBatch:               bqbatch.GetBatch,
        GetInviteByID:            getInviteByID,
        UpdateInviteToken:        updateInviteToken,
        GenerateToken:            helper.GenerateRandomTokenHex,
        HashString:               security.CalculateSHA256,
        GetOrganizationName:      getOrganizationName,
        RenderEmailTemplate:      renderEmailTemplate,
        LogInviteEvent:           logInviteEvent,
        PublishEmailNotification: publishEmailNotification,
})

var ValidateInviteHandler = ValidateInviteHandlerWithDeps(HandlerDeps{
        GetConnections:      connect.GetConnections,
        ValidateInviteToken: validateInviteToken,
})

var RedeemInviteHandler = RedeemInviteHandlerWithDeps(HandlerDeps{
        GetConnections:     connect.GetConnections,
        GetBQBatch:         bqbatch.GetBatch,
        UpdateInviteStatus: updateInviteStatus,
        GetInviteByID:      getInviteByID,
        LogInviteEvent:     logInviteEvent,
})
</pre>
		
		</div>
	</body>
	<script>
	(function() {
		var files = document.getElementById('files');
		var visible;
		files.addEventListener('change', onChange, false);
		function select(part) {
			if (visible)
				visible.style.display = 'none';
			visible = document.getElementById(part);
			if (!visible)
				return;
			files.value = part;
			visible.style.display = 'block';
			location.hash = part;
		}
		function onChange() {
			select(files.value);
			window.scrollTo(0, 0);
		}
		if (location.hash != "") {
			select(location.hash.substr(1));
		}
		if (!visible) {
			select("file0");
		}
	})();
	</script>
</html>
