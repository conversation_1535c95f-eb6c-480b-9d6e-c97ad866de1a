package roles

import "errors"

var (
	ErrDatabaseOperation     = errors.New("database operation failed")
	ErrRoleNotFound          = errors.New("role not found")
	ErrOrganizationNotFound  = errors.New("organization not found")
	ErrTemplateRoleNotFound  = errors.New("template role not found")
	ErrInvalidOrganizationId = errors.New("invalid organization ID")
	ErrInvalidRoleId         = errors.New("invalid role ID")
	ErrInvalidRequestBody    = errors.New("invalid request body")
	ErrUnexpectedFields      = errors.New("unexpected fields in request body")
	ErrInvalidRoleName       = errors.New("role name cannot be empty")
	ErrInvalidDescription    = errors.New("description cannot be empty")
	ErrTemplateRoleMismatch  = errors.New("template role does not match organization type")
	ErrInvalidTemplateRole   = errors.New("template role identifier cannot be empty")
)
