package roles

import (
	"bytes"
	"context"
	"database/sql"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/google/uuid"
	"github.com/gorilla/mux"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/mocks/dbexecutor"
)

// MockHandlerDeps provides mock dependencies for testing
type MockHandlerDeps struct {
	mock.Mock
}

func (m *MockHandlerDeps) GetConnections(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
	args := m.Called(ctx, checkConnections)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*connect.Connections), args.Error(1)
}

func (m *MockHandlerDeps) GetRolesByOrganization(pg connect.DatabaseExecutor, orgId uuid.UUID) (*[]CustomRole, error) {
	args := m.Called(pg, orgId)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*[]CustomRole), args.Error(1)
}

func (m *MockHandlerDeps) DeleteRole(pg connect.DatabaseExecutor, orgId uuid.UUID, roleId uuid.UUID) error {
	args := m.Called(pg, orgId, roleId)
	return args.Error(0)
}

func (m *MockHandlerDeps) CreateRole(pg connect.DatabaseExecutor, orgId uuid.UUID, req *CreateRoleRequest) (*CustomRole, error) {
	args := m.Called(pg, orgId, req)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*CustomRole), args.Error(1)
}

func (m *MockHandlerDeps) GetRoleTemplates(pg connect.DatabaseExecutor, orgId uuid.UUID) (*[]TemplateRole, error) {
	args := m.Called(pg, orgId)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*[]TemplateRole), args.Error(1)
}

func (m *MockHandlerDeps) CheckRoleAssignments(pg connect.DatabaseExecutor, roleId uuid.UUID) (*[]RoleAssignment, error) {
	args := m.Called(pg, roleId)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*[]RoleAssignment), args.Error(1)
}

func Test_GetRolesHandlerWithDeps_Success(t *testing.T) {
	t.Parallel()

	// Setup
	mockDeps := &MockHandlerDeps{}
	mockDB := &dbexecutor.FakeDBExecutor{}

	orgId := uuid.New()
	expectedRoles := []CustomRole{
		{
			Id:                     uuid.New(),
			OrganizationId:         orgId,
			TemplateRoleIdentifier: "mun_admin",
			OrgTypeIdentifier:      "municipality",
			Name:                   "Test Admin Role",
			Description:            "Test admin role description",
		},
	}

	// Mock expectations
	mockDeps.On("GetConnections", mock.Anything, mock.Anything).Return(&connect.Connections{
		Postgres: mockDB,
	}, nil)
	mockDeps.On("GetRolesByOrganization", mockDB, orgId).Return(&expectedRoles, nil)

	// Create handler
	handler := GetRolesHandlerWithDeps(HandlerDeps{
		GetConnections:         mockDeps.GetConnections,
		GetRolesByOrganization: mockDeps.GetRolesByOrganization,
	})

	// Create request
	req := httptest.NewRequest("GET", "/api/organization/"+orgId.String()+"/roles", nil)
	req = mux.SetURLVars(req, map[string]string{"organizationId": orgId.String()})
	w := httptest.NewRecorder()

	// Execute
	handler(w, req)

	// Assert
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "success", response["status"])

	mockDeps.AssertExpectations(t)
}

func Test_DeleteRoleHandlerWithDeps_GetConnectionsError(t *testing.T) {
	t.Parallel()

	// Setup
	mockDeps := &MockHandlerDeps{}

	// Mock expectations
	mockDeps.On("GetConnections", mock.Anything, mock.Anything).Return(nil, ErrDatabaseOperation)

	// Create handler
	handler := DeleteRoleHandlerWithDeps(HandlerDeps{
		GetConnections:       mockDeps.GetConnections,
		DeleteRole:           mockDeps.DeleteRole,
		CheckRoleAssignments: mockDeps.CheckRoleAssignments,
	})

	// Create request
	orgId := uuid.New()
	roleId := uuid.New()
	req := httptest.NewRequest("DELETE", "/api/organization/"+orgId.String()+"/roles/"+roleId.String(), nil)
	req = mux.SetURLVars(req, map[string]string{
		"organizationId": orgId.String(),
		"roleId":         roleId.String(),
	})
	w := httptest.NewRecorder()

	// Execute
	handler(w, req)

	// Assert
	assert.Equal(t, http.StatusInternalServerError, w.Code)

	mockDeps.AssertExpectations(t)
}

func Test_DeleteRoleHandlerWithDeps_InvalidOrganizationId(t *testing.T) {
	t.Parallel()

	// Setup
	mockDeps := &MockHandlerDeps{}
	mockDB := &dbexecutor.FakeDBExecutor{}

	// Mock expectations
	mockDeps.On("GetConnections", mock.Anything, mock.Anything).Return(&connect.Connections{
		Postgres: mockDB,
	}, nil)

	// Create handler
	handler := DeleteRoleHandlerWithDeps(HandlerDeps{
		GetConnections:       mockDeps.GetConnections,
		DeleteRole:           mockDeps.DeleteRole,
		CheckRoleAssignments: mockDeps.CheckRoleAssignments,
	})

	// Create request with invalid UUID
	req := httptest.NewRequest("DELETE", "/api/organization/invalid-uuid/roles/invalid-role-id", nil)
	req = mux.SetURLVars(req, map[string]string{
		"organizationId": "invalid-uuid",
		"roleId":         "invalid-role-id",
	})
	w := httptest.NewRecorder()

	// Execute
	handler(w, req)

	// Assert
	assert.Equal(t, http.StatusBadRequest, w.Code)

	mockDeps.AssertExpectations(t)
}

func Test_DeleteRoleHandlerWithDeps_CheckRoleAssignmentsError(t *testing.T) {
	t.Parallel()

	// Setup
	mockDeps := &MockHandlerDeps{}
	mockDB := &dbexecutor.FakeDBExecutor{}

	orgId := uuid.New()
	roleId := uuid.New()

	// Mock expectations
	mockDeps.On("GetConnections", mock.Anything, mock.Anything).Return(&connect.Connections{
		Postgres: mockDB,
	}, nil)
	mockDeps.On("CheckRoleAssignments", mockDB, roleId).Return(nil, ErrDatabaseOperation)

	// Create handler
	handler := DeleteRoleHandlerWithDeps(HandlerDeps{
		GetConnections:       mockDeps.GetConnections,
		DeleteRole:           mockDeps.DeleteRole,
		CheckRoleAssignments: mockDeps.CheckRoleAssignments,
	})

	// Create request
	req := httptest.NewRequest("DELETE", "/api/organization/"+orgId.String()+"/roles/"+roleId.String(), nil)
	req = mux.SetURLVars(req, map[string]string{
		"organizationId": orgId.String(),
		"roleId":         roleId.String(),
	})
	w := httptest.NewRecorder()

	// Execute
	handler(w, req)

	// Assert
	assert.Equal(t, http.StatusInternalServerError, w.Code)

	mockDeps.AssertExpectations(t)
}

func Test_DeleteRoleHandlerWithDeps_RoleHasAssignments(t *testing.T) {
	t.Parallel()

	// Setup
	mockDeps := &MockHandlerDeps{}
	mockDB := &dbexecutor.FakeDBExecutor{}

	orgId := uuid.New()
	roleId := uuid.New()
	assignments := []RoleAssignment{
		{
			MembershipId: uuid.New(),
			Scope:        "organization",
		},
		{
			MembershipId: uuid.New(),
			Scope:        "devicegroup",
		},
	}

	// Mock expectations
	mockDeps.On("GetConnections", mock.Anything, mock.Anything).Return(&connect.Connections{
		Postgres: mockDB,
	}, nil)
	mockDeps.On("CheckRoleAssignments", mockDB, roleId).Return(&assignments, nil)

	// Create handler
	handler := DeleteRoleHandlerWithDeps(HandlerDeps{
		GetConnections:       mockDeps.GetConnections,
		DeleteRole:           mockDeps.DeleteRole,
		CheckRoleAssignments: mockDeps.CheckRoleAssignments,
	})

	// Create request
	req := httptest.NewRequest("DELETE", "/api/organization/"+orgId.String()+"/roles/"+roleId.String(), nil)
	req = mux.SetURLVars(req, map[string]string{
		"organizationId": orgId.String(),
		"roleId":         roleId.String(),
	})
	w := httptest.NewRecorder()

	// Execute
	handler(w, req)

	// Assert
	assert.Equal(t, http.StatusBadRequest, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Role cannot be deleted because it has active assignments", response["message"])

	mockDeps.AssertExpectations(t)
}

func Test_DeleteRoleHandlerWithDeps_CheckRoleAssignmentsReturnsNil(t *testing.T) {
	t.Parallel()

	// Setup
	mockDeps := &MockHandlerDeps{}
	mockDB := &dbexecutor.FakeDBExecutor{}

	orgId := uuid.New()
	roleId := uuid.New()

	// Mock expectations
	mockDeps.On("GetConnections", mock.Anything, mock.Anything).Return(&connect.Connections{
		Postgres: mockDB,
	}, nil)
	mockDeps.On("CheckRoleAssignments", mockDB, roleId).Return(nil, nil)
	mockDeps.On("DeleteRole", mockDB, orgId, roleId).Return(nil)

	// Create handler
	handler := DeleteRoleHandlerWithDeps(HandlerDeps{
		GetConnections:       mockDeps.GetConnections,
		DeleteRole:           mockDeps.DeleteRole,
		CheckRoleAssignments: mockDeps.CheckRoleAssignments,
	})

	// Create request
	req := httptest.NewRequest("DELETE", "/api/organization/"+orgId.String()+"/roles/"+roleId.String(), nil)
	req = mux.SetURLVars(req, map[string]string{
		"organizationId": orgId.String(),
		"roleId":         roleId.String(),
	})
	w := httptest.NewRecorder()

	// Execute
	handler(w, req)

	// Assert
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "success", response["status"])

	mockDeps.AssertExpectations(t)
}

// Table-driven tests for validateOrganizationId
func Test_validateOrganizationId(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name       string
		orgId      string
		wantErr    error
		wantResult uuid.UUID
	}{
		{
			name:       "valid uuid",
			orgId:      uuid.New().String(),
			wantErr:    nil,
			wantResult: uuid.Nil, // will check in test body
		},
		{
			name:       "invalid uuid",
			orgId:      "invalid-uuid",
			wantErr:    ErrInvalidOrganizationId,
			wantResult: uuid.Nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			req := httptest.NewRequest("GET", "/test", nil)
			req = mux.SetURLVars(req, map[string]string{"organizationId": tt.orgId})
			result, err := validateOrganizationId(req)
			if tt.wantErr != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.wantErr, err)
				assert.Equal(t, tt.wantResult, result)
			} else {
				assert.NoError(t, err)
				parsed, _ := uuid.Parse(tt.orgId)
				assert.Equal(t, parsed, result)
			}
		})
	}
}

// Table-driven tests for validateRoleId
func Test_validateRoleId(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name       string
		roleId     string
		wantErr    error
		wantResult uuid.UUID
	}{
		{
			name:       "valid uuid",
			roleId:     uuid.New().String(),
			wantErr:    nil,
			wantResult: uuid.Nil, // will check in test body
		},
		{
			name:       "invalid uuid",
			roleId:     "invalid-uuid",
			wantErr:    ErrInvalidRoleId,
			wantResult: uuid.Nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			req := httptest.NewRequest("GET", "/test", nil)
			req = mux.SetURLVars(req, map[string]string{"roleId": tt.roleId})
			result, err := validateRoleId(req)
			if tt.wantErr != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.wantErr, err)
				assert.Equal(t, tt.wantResult, result)
			} else {
				assert.NoError(t, err)
				parsed, _ := uuid.Parse(tt.roleId)
				assert.Equal(t, parsed, result)
			}
		})
	}
}

// Table-driven tests for parseCreateRoleRequest
func Test_parseCreateRoleRequest(t *testing.T) {
	t.Parallel()

	validReq := CreateRoleRequest{
		Name:                   "Test Role",
		TemplateRoleIdentifier: "mun_admin",
		Description:            "Test role description",
	}

	tests := []struct {
		name        string
		body        interface{}
		wantErr     error
		wantPartial *CreateRoleRequest
	}{
		{
			name:        "valid request",
			body:        validReq,
			wantErr:     nil,
			wantPartial: &validReq,
		},
		{
			name:        "empty name",
			body:        CreateRoleRequest{"", "mun_admin", "Test role description"},
			wantErr:     ErrInvalidRoleName,
			wantPartial: &CreateRoleRequest{"", "mun_admin", "Test role description"},
		},
		{
			name:        "empty description",
			body:        CreateRoleRequest{"Test Role", "mun_admin", ""},
			wantErr:     ErrInvalidDescription,
			wantPartial: &CreateRoleRequest{"Test Role", "mun_admin", ""},
		},
		{
			name:        "empty template role",
			body:        CreateRoleRequest{"Test Role", "", "Test role description"},
			wantErr:     ErrInvalidTemplateRole,
			wantPartial: &CreateRoleRequest{"Test Role", "", "Test role description"},
		},
		{
			name:        "whitespace only name",
			body:        CreateRoleRequest{"   ", "mun_admin", "Test role description"},
			wantErr:     ErrInvalidRoleName,
			wantPartial: &CreateRoleRequest{"   ", "mun_admin", "Test role description"},
		},
		{
			name:        "whitespace only description",
			body:        CreateRoleRequest{"Test Role", "mun_admin", "   "},
			wantErr:     ErrInvalidDescription,
			wantPartial: &CreateRoleRequest{"Test Role", "mun_admin", "   "},
		},
		{
			name:        "whitespace only template role",
			body:        CreateRoleRequest{"Test Role", "   ", "Test role description"},
			wantErr:     ErrInvalidTemplateRole,
			wantPartial: &CreateRoleRequest{"Test Role", "   ", "Test role description"},
		},
		{
			name:        "invalid json",
			body:        "invalid json",
			wantErr:     ErrInvalidRequestBody,
			wantPartial: &CreateRoleRequest{},
		},
		{
			name:        "unexpected fields",
			body:        map[string]interface{}{"name": "Test Role", "templateRoleIdentifier": "mun_admin", "description": "Test description", "unexpectedField": "value"},
			wantErr:     ErrUnexpectedFields,
			wantPartial: &CreateRoleRequest{Name: "Test Role", TemplateRoleIdentifier: "mun_admin", Description: "Test description"},
		},
		{
			name:        "unknown field error",
			body:        `{"name": "Test Role", "templateRoleIdentifier": "mun_admin", "description": "Test description", "unknownField": "value"}`,
			wantErr:     ErrUnexpectedFields,
			wantPartial: &CreateRoleRequest{Name: "Test Role", TemplateRoleIdentifier: "mun_admin", Description: "Test description"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			var req *http.Request
			if s, ok := tt.body.(string); ok {
				req = httptest.NewRequest("POST", "/test", bytes.NewBufferString(s))
			} else if m, ok := tt.body.(map[string]interface{}); ok {
				b, _ := json.Marshal(m)
				req = httptest.NewRequest("POST", "/test", bytes.NewBuffer(b))
			} else {
				b, _ := json.Marshal(tt.body)
				req = httptest.NewRequest("POST", "/test", bytes.NewBuffer(b))
			}
			req.Header.Set("Content-Type", "application/json")
			result, err := parseCreateRoleRequest(req)
			if tt.wantErr != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.wantErr, err)
				assert.NotNil(t, result)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.wantPartial.Name, result.Name)
				assert.Equal(t, tt.wantPartial.TemplateRoleIdentifier, result.TemplateRoleIdentifier)
				assert.Equal(t, tt.wantPartial.Description, result.Description)
			}
		})
	}
}

// Table-driven tests for GetRolesHandlerWithDeps
func Test_GetRolesHandlerWithDeps(t *testing.T) {
	t.Parallel()

	orgId := uuid.New()
	mockDB := &dbexecutor.FakeDBExecutor{}
	expectedRoles := []CustomRole{{Id: uuid.New(), OrganizationId: orgId, TemplateRoleIdentifier: "mun_admin", OrgTypeIdentifier: "municipality", Name: "Test Admin Role", Description: "Test admin role description"}}

	tests := []struct {
		name         string
		orgId        string
		mockSetup    func(*MockHandlerDeps)
		wantStatus   int
		wantResponse string
	}{
		{
			name:  "success",
			orgId: orgId.String(),
			mockSetup: func(m *MockHandlerDeps) {
				m.On("GetConnections", mock.Anything, mock.Anything).Return(&connect.Connections{Postgres: mockDB}, nil)
				m.On("GetRolesByOrganization", mockDB, orgId).Return(&expectedRoles, nil)
			},
			wantStatus:   http.StatusOK,
			wantResponse: "success",
		},
		{
			name:  "get connections error",
			orgId: orgId.String(),
			mockSetup: func(m *MockHandlerDeps) {
				m.On("GetConnections", mock.Anything, mock.Anything).Return(nil, ErrDatabaseOperation)
			},
			wantStatus:   http.StatusInternalServerError,
			wantResponse: "",
		},
		{
			name:  "invalid organization id",
			orgId: "invalid-uuid",
			mockSetup: func(m *MockHandlerDeps) {
				m.On("GetConnections", mock.Anything, mock.Anything).Return(&connect.Connections{Postgres: mockDB}, nil)
			},
			wantStatus:   http.StatusBadRequest,
			wantResponse: "",
		},
		{
			name:  "get roles error",
			orgId: orgId.String(),
			mockSetup: func(m *MockHandlerDeps) {
				m.On("GetConnections", mock.Anything, mock.Anything).Return(&connect.Connections{Postgres: mockDB}, nil)
				m.On("GetRolesByOrganization", mockDB, orgId).Return(nil, ErrDatabaseOperation)
			},
			wantStatus:   http.StatusInternalServerError,
			wantResponse: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			mockDeps := &MockHandlerDeps{}
			if tt.mockSetup != nil {
				tt.mockSetup(mockDeps)
			}
			handler := GetRolesHandlerWithDeps(HandlerDeps{
				GetConnections:         mockDeps.GetConnections,
				GetRolesByOrganization: mockDeps.GetRolesByOrganization,
			})
			req := httptest.NewRequest("GET", "/api/organization/"+tt.orgId+"/roles", nil)
			req = mux.SetURLVars(req, map[string]string{"organizationId": tt.orgId})
			w := httptest.NewRecorder()
			handler(w, req)
			assert.Equal(t, tt.wantStatus, w.Code)
			if tt.wantResponse != "" && w.Code == http.StatusOK {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.wantResponse, response["status"])
			}
			mockDeps.AssertExpectations(t)
		})
	}
}

// Table-driven tests for CreateRoleHandlerWithDeps
func Test_CreateRoleHandlerWithDeps(t *testing.T) {
	t.Parallel()

	orgId := uuid.New()
	mockDB := &dbexecutor.FakeDBExecutor{}
	roleId := uuid.New()
	createRequest := CreateRoleRequest{Name: "Test Role", TemplateRoleIdentifier: "mun_admin", Description: "Test role description"}
	expectedRole := CustomRole{Id: roleId, OrganizationId: orgId, TemplateRoleIdentifier: "mun_admin", OrgTypeIdentifier: "municipality", Name: "Test Role", Description: "Test role description"}
	validBody, _ := json.Marshal(createRequest)

	tests := []struct {
		name         string
		orgId        string
		body         []byte
		mockSetup    func(*MockHandlerDeps)
		wantStatus   int
		wantResponse string
	}{
		{
			name:  "success",
			orgId: orgId.String(),
			body:  validBody,
			mockSetup: func(m *MockHandlerDeps) {
				m.On("GetConnections", mock.Anything, mock.Anything).Return(&connect.Connections{Postgres: mockDB}, nil)
				m.On("CreateRole", mockDB, orgId, &createRequest).Return(&expectedRole, nil)
			},
			wantStatus:   http.StatusOK,
			wantResponse: "success",
		},
		{
			name:  "get connections error",
			orgId: orgId.String(),
			body:  validBody,
			mockSetup: func(m *MockHandlerDeps) {
				m.On("GetConnections", mock.Anything, mock.Anything).Return(nil, ErrDatabaseOperation)
			},
			wantStatus:   http.StatusInternalServerError,
			wantResponse: "",
		},
		{
			name:  "invalid organization id",
			orgId: "invalid-uuid",
			body:  validBody,
			mockSetup: func(m *MockHandlerDeps) {
				m.On("GetConnections", mock.Anything, mock.Anything).Return(&connect.Connections{Postgres: mockDB}, nil)
			},
			wantStatus:   http.StatusBadRequest,
			wantResponse: "",
		},
		{
			name:  "parse request error",
			orgId: orgId.String(),
			body:  []byte("invalid json"),
			mockSetup: func(m *MockHandlerDeps) {
				m.On("GetConnections", mock.Anything, mock.Anything).Return(&connect.Connections{Postgres: mockDB}, nil)
			},
			wantStatus:   http.StatusBadRequest,
			wantResponse: "",
		},
		{
			name:  "organization not found",
			orgId: orgId.String(),
			body:  validBody,
			mockSetup: func(m *MockHandlerDeps) {
				m.On("GetConnections", mock.Anything, mock.Anything).Return(&connect.Connections{Postgres: mockDB}, nil)
				m.On("CreateRole", mockDB, orgId, &createRequest).Return(nil, ErrOrganizationNotFound)
			},
			wantStatus:   http.StatusNotFound,
			wantResponse: "",
		},
		{
			name:  "template role not found",
			orgId: orgId.String(),
			body:  validBody,
			mockSetup: func(m *MockHandlerDeps) {
				m.On("GetConnections", mock.Anything, mock.Anything).Return(&connect.Connections{Postgres: mockDB}, nil)
				m.On("CreateRole", mockDB, orgId, &createRequest).Return(nil, ErrTemplateRoleNotFound)
			},
			wantStatus:   http.StatusBadRequest,
			wantResponse: "",
		},
		{
			name:  "template role mismatch",
			orgId: orgId.String(),
			body:  validBody,
			mockSetup: func(m *MockHandlerDeps) {
				m.On("GetConnections", mock.Anything, mock.Anything).Return(&connect.Connections{Postgres: mockDB}, nil)
				m.On("CreateRole", mockDB, orgId, &createRequest).Return(nil, ErrTemplateRoleMismatch)
			},
			wantStatus:   http.StatusBadRequest,
			wantResponse: "",
		},
		{
			name:  "database error",
			orgId: orgId.String(),
			body:  validBody,
			mockSetup: func(m *MockHandlerDeps) {
				m.On("GetConnections", mock.Anything, mock.Anything).Return(&connect.Connections{Postgres: mockDB}, nil)
				m.On("CreateRole", mockDB, orgId, &createRequest).Return(nil, ErrDatabaseOperation)
			},
			wantStatus:   http.StatusInternalServerError,
			wantResponse: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			mockDeps := &MockHandlerDeps{}
			if tt.mockSetup != nil {
				tt.mockSetup(mockDeps)
			}
			handler := CreateRoleHandlerWithDeps(HandlerDeps{
				GetConnections: mockDeps.GetConnections,
				CreateRole:     mockDeps.CreateRole,
			})
			req := httptest.NewRequest("POST", "/api/organization/"+tt.orgId+"/roles", bytes.NewBuffer(tt.body))
			req = mux.SetURLVars(req, map[string]string{"organizationId": tt.orgId})
			req.Header.Set("Content-Type", "application/json")
			w := httptest.NewRecorder()
			handler(w, req)
			assert.Equal(t, tt.wantStatus, w.Code)
			if tt.wantResponse != "" && w.Code == http.StatusOK {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.wantResponse, response["status"])
			}
			mockDeps.AssertExpectations(t)
		})
	}
}

// Table-driven tests for DeleteRoleHandlerWithDeps
func Test_DeleteRoleHandlerWithDeps(t *testing.T) {
	t.Parallel()

	orgId := uuid.New()
	roleId := uuid.New()
	mockDB := &dbexecutor.FakeDBExecutor{}

	tests := []struct {
		name         string
		orgId        string
		roleId       string
		mockSetup    func(*MockHandlerDeps)
		wantStatus   int
		wantResponse string
		urlVars      map[string]string
		method       string
	}{
		{
			name:   "delete specific role success",
			orgId:  orgId.String(),
			roleId: roleId.String(),
			mockSetup: func(m *MockHandlerDeps) {
				m.On("GetConnections", mock.Anything, mock.Anything).Return(&connect.Connections{Postgres: mockDB}, nil)
				m.On("CheckRoleAssignments", mockDB, roleId).Return(&[]RoleAssignment{}, nil)
				m.On("DeleteRole", mockDB, orgId, roleId).Return(nil)
			},
			wantStatus:   http.StatusOK,
			wantResponse: "success",
			urlVars:      map[string]string{"organizationId": orgId.String(), "roleId": roleId.String()},
			method:       "DELETE",
		},
		{
			name:   "delete specific role not found",
			orgId:  orgId.String(),
			roleId: roleId.String(),
			mockSetup: func(m *MockHandlerDeps) {
				m.On("GetConnections", mock.Anything, mock.Anything).Return(&connect.Connections{Postgres: mockDB}, nil)
				m.On("CheckRoleAssignments", mockDB, roleId).Return(&[]RoleAssignment{}, nil)
				m.On("DeleteRole", mockDB, orgId, roleId).Return(ErrRoleNotFound)
			},
			wantStatus:   http.StatusNotFound,
			wantResponse: "",
			urlVars:      map[string]string{"organizationId": orgId.String(), "roleId": roleId.String()},
			method:       "DELETE",
		},
		{
			name:   "delete specific role error",
			orgId:  orgId.String(),
			roleId: roleId.String(),
			mockSetup: func(m *MockHandlerDeps) {
				m.On("GetConnections", mock.Anything, mock.Anything).Return(&connect.Connections{Postgres: mockDB}, nil)
				m.On("CheckRoleAssignments", mockDB, roleId).Return(&[]RoleAssignment{}, nil)
				m.On("DeleteRole", mockDB, orgId, roleId).Return(ErrDatabaseOperation)
			},
			wantStatus:   http.StatusInternalServerError,
			wantResponse: "",
			urlVars:      map[string]string{"organizationId": orgId.String(), "roleId": roleId.String()},
			method:       "DELETE",
		},
		{
			name:   "delete specific role invalid role id",
			orgId:  orgId.String(),
			roleId: "invalid-uuid",
			mockSetup: func(m *MockHandlerDeps) {
				m.On("GetConnections", mock.Anything, mock.Anything).Return(&connect.Connections{Postgres: mockDB}, nil)
			},
			wantStatus:   http.StatusBadRequest,
			wantResponse: "",
			urlVars:      map[string]string{"organizationId": orgId.String(), "roleId": "invalid-uuid"},
			method:       "DELETE",
		},
		{
			name:   "get connections error",
			orgId:  orgId.String(),
			roleId: roleId.String(),
			mockSetup: func(m *MockHandlerDeps) {
				m.On("GetConnections", mock.Anything, mock.Anything).Return(nil, ErrDatabaseOperation)
			},
			wantStatus:   http.StatusInternalServerError,
			wantResponse: "",
			urlVars:      map[string]string{"organizationId": orgId.String(), "roleId": roleId.String()},
			method:       "DELETE",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			mockDeps := &MockHandlerDeps{}
			if tt.mockSetup != nil {
				tt.mockSetup(mockDeps)
			}
			handler := DeleteRoleHandlerWithDeps(HandlerDeps{
				GetConnections:       mockDeps.GetConnections,
				DeleteRole:           mockDeps.DeleteRole,
				CheckRoleAssignments: mockDeps.CheckRoleAssignments,
			})
			req := httptest.NewRequest(tt.method, "/api/organization/"+tt.orgId+"/roles/"+tt.roleId, nil)
			req = mux.SetURLVars(req, tt.urlVars)
			w := httptest.NewRecorder()
			handler(w, req)
			assert.Equal(t, tt.wantStatus, w.Code)
			if tt.wantResponse != "" && w.Code == http.StatusOK {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.wantResponse, response["status"])
			}
			mockDeps.AssertExpectations(t)
		})
	}
}

// Table-driven tests for GetRoleTemplatesHandlerWithDeps
func Test_GetRoleTemplatesHandlerWithDeps(t *testing.T) {
	t.Parallel()

	orgId := uuid.New()
	mockDB := &dbexecutor.FakeDBExecutor{}
	expectedTemplates := []TemplateRole{{Identifier: "mun_admin", Name: "Municipality Admin", OrgTypeIdentifier: "municipality", Description: "Administrator role for municipalities"}}

	tests := []struct {
		name         string
		orgId        string
		mockSetup    func(*MockHandlerDeps)
		wantStatus   int
		wantResponse string
	}{
		{
			name:  "success",
			orgId: orgId.String(),
			mockSetup: func(m *MockHandlerDeps) {
				m.On("GetConnections", mock.Anything, mock.Anything).Return(&connect.Connections{Postgres: mockDB}, nil)
				m.On("GetRoleTemplates", mockDB, orgId).Return(&expectedTemplates, nil)
			},
			wantStatus:   http.StatusOK,
			wantResponse: "success",
		},
		{
			name:  "get connections error",
			orgId: orgId.String(),
			mockSetup: func(m *MockHandlerDeps) {
				m.On("GetConnections", mock.Anything, mock.Anything).Return(nil, ErrDatabaseOperation)
			},
			wantStatus:   http.StatusInternalServerError,
			wantResponse: "",
		},
		{
			name:  "invalid organization id",
			orgId: "invalid-uuid",
			mockSetup: func(m *MockHandlerDeps) {
				m.On("GetConnections", mock.Anything, mock.Anything).Return(&connect.Connections{Postgres: mockDB}, nil)
			},
			wantStatus:   http.StatusBadRequest,
			wantResponse: "",
		},
		{
			name:  "organization not found",
			orgId: orgId.String(),
			mockSetup: func(m *MockHandlerDeps) {
				m.On("GetConnections", mock.Anything, mock.Anything).Return(&connect.Connections{Postgres: mockDB}, nil)
				m.On("GetRoleTemplates", mockDB, orgId).Return(nil, ErrOrganizationNotFound)
			},
			wantStatus:   http.StatusNotFound,
			wantResponse: "",
		},
		{
			name:  "database error",
			orgId: orgId.String(),
			mockSetup: func(m *MockHandlerDeps) {
				m.On("GetConnections", mock.Anything, mock.Anything).Return(&connect.Connections{Postgres: mockDB}, nil)
				m.On("GetRoleTemplates", mockDB, orgId).Return(nil, ErrDatabaseOperation)
			},
			wantStatus:   http.StatusInternalServerError,
			wantResponse: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			mockDeps := &MockHandlerDeps{}
			if tt.mockSetup != nil {
				tt.mockSetup(mockDeps)
			}
			handler := GetRoleTemplatesHandlerWithDeps(HandlerDeps{
				GetConnections:   mockDeps.GetConnections,
				GetRoleTemplates: mockDeps.GetRoleTemplates,
			})
			req := httptest.NewRequest("GET", "/api/organization/"+tt.orgId+"/role-templates", nil)
			req = mux.SetURLVars(req, map[string]string{"organizationId": tt.orgId})
			w := httptest.NewRecorder()
			handler(w, req)
			assert.Equal(t, tt.wantStatus, w.Code)
			if tt.wantResponse != "" && w.Code == http.StatusOK {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.wantResponse, response["status"])
			}
			mockDeps.AssertExpectations(t)
		})
	}
}

// Production handler tests

func Test_GetRolesHandler_Success(t *testing.T) {
	t.Parallel()

	// Setup
	orgId := uuid.New()

	// Create request
	req := httptest.NewRequest("GET", "/api/organization/"+orgId.String()+"/roles", nil)
	req = mux.SetURLVars(req, map[string]string{"organizationId": orgId.String()})
	w := httptest.NewRecorder()

	// Execute
	GetRolesHandler(w, req)

	// Assert - should return 500 since we don't have real connections
	assert.Equal(t, http.StatusInternalServerError, w.Code)
}

func Test_CreateRoleHandler_Success(t *testing.T) {
	t.Parallel()

	// Setup
	orgId := uuid.New()
	createRequest := CreateRoleRequest{
		Name:                   "Test Role",
		TemplateRoleIdentifier: "mun_admin",
		Description:            "Test role description",
	}

	// Create request body
	requestBody, _ := json.Marshal(createRequest)
	req := httptest.NewRequest("POST", "/api/organization/"+orgId.String()+"/roles", bytes.NewBuffer(requestBody))
	req = mux.SetURLVars(req, map[string]string{"organizationId": orgId.String()})
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()

	// Execute
	CreateRoleHandler(w, req)

	// Assert - should return 500 since we don't have real connections
	assert.Equal(t, http.StatusInternalServerError, w.Code)
}

func Test_DeleteRoleHandler_Success(t *testing.T) {
	t.Parallel()

	// Setup
	orgId := uuid.New()
	roleId := uuid.New()

	// Create request
	req := httptest.NewRequest("DELETE", "/api/organization/"+orgId.String()+"/roles/"+roleId.String(), nil)
	req = mux.SetURLVars(req, map[string]string{
		"organizationId": orgId.String(),
		"roleId":         roleId.String(),
	})
	w := httptest.NewRecorder()

	// Execute
	DeleteRoleHandler(w, req)

	// Assert - should return 500 since we don't have real connections
	assert.Equal(t, http.StatusInternalServerError, w.Code)
}

func Test_GetRoleTemplatesHandler_Success(t *testing.T) {
	t.Parallel()

	// Setup
	orgId := uuid.New()

	// Create request
	req := httptest.NewRequest("GET", "/api/organization/"+orgId.String()+"/role-templates", nil)
	req = mux.SetURLVars(req, map[string]string{"organizationId": orgId.String()})
	w := httptest.NewRecorder()

	// Execute
	GetRoleTemplatesHandler(w, req)

	// Assert - should return 500 since we don't have real connections
	assert.Equal(t, http.StatusInternalServerError, w.Code)
}

// Database function tests

// FakeResult implements sql.Result for testing
type FakeResult struct {
	affectedRows int64
	lastInsertId int64
	rowsErr      error
	insertErr    error
}

func (f *FakeResult) RowsAffected() (int64, error) { return f.affectedRows, f.rowsErr }
func (f *FakeResult) LastInsertId() (int64, error) { return f.lastInsertId, f.insertErr }

// Table-driven tests for getRolesByOrganization
func Test_getRolesByOrganization(t *testing.T) {
	t.Parallel()

	orgId := uuid.New()
	expectedRoles := []CustomRole{
		{
			Id:                     uuid.New(),
			OrganizationId:         orgId,
			TemplateRoleIdentifier: "mun_admin",
			OrgTypeIdentifier:      "municipality",
			Name:                   "Test Role",
			Description:            "Test Description",
		},
	}

	tests := []struct {
		name      string
		orgId     uuid.UUID
		mockSetup func(*dbexecutor.FakeDBExecutor)
		wantRoles *[]CustomRole
		wantErr   error
	}{
		{
			name:  "success",
			orgId: orgId,
			mockSetup: func(m *dbexecutor.FakeDBExecutor) {
				m.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
					roles := dest.(*[]CustomRole)
					*roles = expectedRoles
					return nil
				}
			},
			wantRoles: &expectedRoles,
			wantErr:   nil,
		},
		{
			name:  "database_error",
			orgId: orgId,
			mockSetup: func(m *dbexecutor.FakeDBExecutor) {
				m.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
					return ErrDatabaseOperation
				}
			},
			wantRoles: nil,
			wantErr:   ErrDatabaseOperation,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup mock
			mockDB := &dbexecutor.FakeDBExecutor{}
			tt.mockSetup(mockDB)

			// Execute
			result, err := getRolesByOrganization(mockDB, tt.orgId)

			// Assert
			if tt.wantErr != nil {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.wantErr.Error())
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.wantRoles, result)
			}

			// Check call count
			assert.Equal(t, 1, mockDB.QueryGenericSliceCallCount)
		})
	}
}

// Table-driven tests for deleteRole
func Test_deleteRole(t *testing.T) {
	t.Parallel()

	orgId := uuid.New()
	roleId := uuid.New()

	tests := []struct {
		name      string
		orgId     uuid.UUID
		roleId    uuid.UUID
		mockSetup func(*dbexecutor.FakeDBExecutor)
		wantErr   error
	}{
		{
			name:   "success",
			orgId:  orgId,
			roleId: roleId,
			mockSetup: func(m *dbexecutor.FakeDBExecutor) {
				m.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					return &FakeResult{affectedRows: 1}, nil
				}
			},
			wantErr: nil,
		},
		{
			name:   "role_not_found",
			orgId:  orgId,
			roleId: roleId,
			mockSetup: func(m *dbexecutor.FakeDBExecutor) {
				m.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					return &FakeResult{affectedRows: 0}, nil
				}
			},
			wantErr: ErrRoleNotFound,
		},
		{
			name:   "exec_error",
			orgId:  orgId,
			roleId: roleId,
			mockSetup: func(m *dbexecutor.FakeDBExecutor) {
				m.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					return nil, ErrDatabaseOperation
				}
			},
			wantErr: ErrDatabaseOperation,
		},
		{
			name:   "rows_affected_error",
			orgId:  orgId,
			roleId: roleId,
			mockSetup: func(m *dbexecutor.FakeDBExecutor) {
				m.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					return &FakeResult{affectedRows: 0, rowsErr: ErrDatabaseOperation}, nil
				}
			},
			wantErr: ErrDatabaseOperation,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup mock
			mockDB := &dbexecutor.FakeDBExecutor{}
			tt.mockSetup(mockDB)

			// Execute
			err := deleteRole(mockDB, tt.orgId, tt.roleId)

			// Assert
			if tt.wantErr != nil {
				assert.Error(t, err)
				if tt.wantErr == ErrRoleNotFound {
					assert.Equal(t, tt.wantErr, err)
				} else {
					assert.Contains(t, err.Error(), tt.wantErr.Error())
				}
			} else {
				assert.NoError(t, err)
			}

			// Check call count
			assert.Equal(t, 1, mockDB.ExecCallCount)
		})
	}
}

// Table-driven tests for getRoleTemplates
func Test_getRoleTemplates(t *testing.T) {
	t.Parallel()

	orgId := uuid.New()
	expectedTemplates := []TemplateRole{
		{
			Identifier:        "mun_admin",
			Name:              "Municipality Admin",
			OrgTypeIdentifier: "municipality",
			Description:       "Administrator role for municipalities",
		},
	}

	tests := []struct {
		name          string
		orgId         uuid.UUID
		mockSetup     func(*dbexecutor.FakeDBExecutor)
		wantTemplates *[]TemplateRole
		wantErr       error
	}{
		{
			name:  "success",
			orgId: orgId,
			mockSetup: func(m *dbexecutor.FakeDBExecutor) {
				m.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
					templates := dest.(*[]TemplateRole)
					*templates = expectedTemplates
					return nil
				}
			},
			wantTemplates: &expectedTemplates,
			wantErr:       nil,
		},
		{
			name:  "organization_not_found",
			orgId: orgId,
			mockSetup: func(m *dbexecutor.FakeDBExecutor) {
				m.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
					return sql.ErrNoRows
				}
			},
			wantTemplates: nil,
			wantErr:       ErrOrganizationNotFound,
		},
		{
			name:  "database_error",
			orgId: orgId,
			mockSetup: func(m *dbexecutor.FakeDBExecutor) {
				m.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
					return ErrDatabaseOperation
				}
			},
			wantTemplates: nil,
			wantErr:       ErrDatabaseOperation,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup mock
			mockDB := &dbexecutor.FakeDBExecutor{}
			tt.mockSetup(mockDB)

			// Execute
			result, err := getRoleTemplates(mockDB, tt.orgId)

			// Assert
			if tt.wantErr != nil {
				assert.Error(t, err)
				if tt.wantErr == ErrOrganizationNotFound {
					assert.Equal(t, tt.wantErr, err)
				} else {
					assert.Contains(t, err.Error(), tt.wantErr.Error())
				}
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.wantTemplates, result)
			}

			// Check call count
			assert.Equal(t, 1, mockDB.QueryGenericSliceCallCount)
		})
	}
}

// Table-driven tests for createRole
func Test_createRole(t *testing.T) {
	t.Parallel()

	orgId := uuid.New()
	roleId := uuid.New()
	request := &CreateRoleRequest{
		Name:                   "Test Role",
		TemplateRoleIdentifier: "mun_admin",
		Description:            "Test Description",
	}
	expectedRole := &CustomRole{
		Id:                     roleId,
		OrganizationId:         orgId,
		TemplateRoleIdentifier: "mun_admin",
		OrgTypeIdentifier:      "municipality",
		Name:                   "Test Role",
		Description:            "Test Description",
	}

	tests := []struct {
		name      string
		orgId     uuid.UUID
		request   *CreateRoleRequest
		mockSetup func(*dbexecutor.FakeDBExecutor)
		wantRole  *CustomRole
		wantErr   error
	}{
		{
			name:    "success",
			orgId:   orgId,
			request: request,
			mockSetup: func(m *dbexecutor.FakeDBExecutor) {
				m.QueryRowFunc = func(query string, args ...interface{}) (map[string]interface{}, error) {
					// Organization query - returns orgtypeidentifier
					return map[string]interface{}{"orgtypeidentifier": "municipality"}, nil
				}
				m.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					role := dest.(*CustomRole)
					*role = *expectedRole
					return nil
				}
			},
			wantRole: expectedRole,
			wantErr:  nil,
		},
		{
			name:    "organization_not_found",
			orgId:   orgId,
			request: request,
			mockSetup: func(m *dbexecutor.FakeDBExecutor) {
				m.QueryRowFunc = func(query string, args ...interface{}) (map[string]interface{}, error) {
					return nil, sql.ErrNoRows
				}
			},
			wantRole: nil,
			wantErr:  ErrOrganizationNotFound,
		},
		{
			name:    "organization_query_error",
			orgId:   orgId,
			request: request,
			mockSetup: func(m *dbexecutor.FakeDBExecutor) {
				m.QueryRowFunc = func(query string, args ...interface{}) (map[string]interface{}, error) {
					return nil, ErrDatabaseOperation
				}
			},
			wantRole: nil,
			wantErr:  ErrDatabaseOperation,
		},
		{
			name:    "org_type_column_extraction_error",
			orgId:   orgId,
			request: request,
			mockSetup: func(m *dbexecutor.FakeDBExecutor) {
				m.QueryRowFunc = func(query string, args ...interface{}) (map[string]interface{}, error) {
					// Return result without the orgtypeidentifier column to trigger GetColumn error
					return map[string]interface{}{"other_column": "value"}, nil
				}
			},
			wantRole: nil,
			wantErr:  ErrOrganizationNotFound,
		},
		{
			name:    "template_role_not_found",
			orgId:   orgId,
			request: request,
			mockSetup: func(m *dbexecutor.FakeDBExecutor) {
				m.QueryRowFunc = func(query string, args ...interface{}) (map[string]interface{}, error) {
					// Organization query - returns orgtypeidentifier
					return map[string]interface{}{"orgtypeidentifier": "municipality"}, nil
				}
				m.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					return ErrDatabaseOperation
				}
			},
			wantRole: nil,
			wantErr:  ErrDatabaseOperation,
		},
		{
			name:    "template_role_query_error",
			orgId:   orgId,
			request: request,
			mockSetup: func(m *dbexecutor.FakeDBExecutor) {
				m.QueryRowFunc = func(query string, args ...interface{}) (map[string]interface{}, error) {
					// Organization query - returns orgtypeidentifier
					return map[string]interface{}{"orgtypeidentifier": "municipality"}, nil
				}
				m.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					return ErrDatabaseOperation
				}
			},
			wantRole: nil,
			wantErr:  ErrDatabaseOperation,
		},
		{
			name:    "role_insertion_error",
			orgId:   orgId,
			request: request,
			mockSetup: func(m *dbexecutor.FakeDBExecutor) {
				callCount := 0
				m.QueryRowFunc = func(query string, args ...interface{}) (map[string]interface{}, error) {
					callCount++
					if callCount == 1 {
						// Organization query - returns orgtypeidentifier
						return map[string]interface{}{"orgtypeidentifier": "municipality"}, nil
					} else if callCount == 2 {
						// Template role query - should match the orgTypeId from first query
						return map[string]interface{}{"count": int64(1)}, nil
					}
					return nil, ErrDatabaseOperation
				}
				m.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					return ErrDatabaseOperation
				}
			},
			wantRole: nil,
			wantErr:  ErrDatabaseOperation,
		},
		{
			name:    "template_role_mismatch",
			orgId:   orgId,
			request: request,
			mockSetup: func(m *dbexecutor.FakeDBExecutor) {
				m.QueryRowFunc = func(query string, args ...interface{}) (map[string]interface{}, error) {
					// Organization query - returns orgtypeidentifier
					return map[string]interface{}{"orgtypeidentifier": "municipality"}, nil
				}
				m.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					return ErrDatabaseOperation
				}
			},
			wantRole: nil,
			wantErr:  ErrDatabaseOperation,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup mock
			mockDB := &dbexecutor.FakeDBExecutor{}
			tt.mockSetup(mockDB)

			// Execute
			result, err := createRole(mockDB, tt.orgId, tt.request)

			// Assert
			if tt.wantErr != nil {
				assert.Error(t, err)
				if tt.wantErr == ErrOrganizationNotFound || tt.wantErr == ErrTemplateRoleNotFound {
					assert.Equal(t, tt.wantErr, err)
				} else {
					assert.Contains(t, err.Error(), tt.wantErr.Error())
				}
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				if result != nil {
					assert.Equal(t, tt.wantRole.Id, result.Id)
					assert.Equal(t, tt.wantRole.OrganizationId, result.OrganizationId)
					assert.Equal(t, tt.wantRole.TemplateRoleIdentifier, result.TemplateRoleIdentifier)
					assert.Equal(t, tt.wantRole.Name, result.Name)
					assert.Equal(t, tt.wantRole.Description, result.Description)
				}
			}

			// Check call counts - at least organization query should be called
			assert.GreaterOrEqual(t, mockDB.QueryRowCallCount, 1)
		})
	}
}

// Table-driven tests for checkRoleAssignments
func Test_checkRoleAssignments(t *testing.T) {
	t.Parallel()

	roleId := uuid.New()
	expectedAssignments := []RoleAssignment{
		{
			MembershipId: uuid.New(),
			Scope:        "organization",
		},
		{
			MembershipId: uuid.New(),
			Scope:        "devicegroup",
		},
		{
			MembershipId: uuid.New(),
			Scope:        "locationgroup",
		},
	}

	tests := []struct {
		name            string
		roleId          uuid.UUID
		mockSetup       func(*dbexecutor.FakeDBExecutor)
		wantAssignments *[]RoleAssignment
		wantErr         error
	}{
		{
			name:   "success_with_assignments",
			roleId: roleId,
			mockSetup: func(m *dbexecutor.FakeDBExecutor) {
				m.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
					assignments := dest.(*[]RoleAssignment)
					*assignments = expectedAssignments
					return nil
				}
			},
			wantAssignments: &expectedAssignments,
			wantErr:         nil,
		},
		{
			name:   "success_no_assignments",
			roleId: roleId,
			mockSetup: func(m *dbexecutor.FakeDBExecutor) {
				m.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
					assignments := dest.(*[]RoleAssignment)
					*assignments = []RoleAssignment{}
					return nil
				}
			},
			wantAssignments: &[]RoleAssignment{},
			wantErr:         nil,
		},
		{
			name:   "database_error",
			roleId: roleId,
			mockSetup: func(m *dbexecutor.FakeDBExecutor) {
				m.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
					return ErrDatabaseOperation
				}
			},
			wantAssignments: nil,
			wantErr:         ErrDatabaseOperation,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup mock
			mockDB := &dbexecutor.FakeDBExecutor{}
			tt.mockSetup(mockDB)

			// Execute
			result, err := checkRoleAssignments(mockDB, tt.roleId)

			// Assert
			if tt.wantErr != nil {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.wantErr.Error())
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.wantAssignments, result)
			}

			// Check call count
			assert.Equal(t, 1, mockDB.QueryGenericSliceCallCount)
		})
	}
}
