package roles

import (
	"time"

	"github.com/google/uuid"
)

// CustomRole represents a custom role for an organization
type CustomRole struct {
	Id                     uuid.UUID `json:"id" db:"id"`
	OrganizationId         uuid.UUID `json:"organizationId" db:"organizationid"`
	TemplateRoleIdentifier string    `json:"templateRoleIdentifier" db:"templateroleidentifier"`
	OrgTypeIdentifier      string    `json:"orgTypeIdentifier" db:"orgtypeidentifier"`
	Name                   string    `json:"name" db:"name"`
	Description            string    `json:"description" db:"description"`
	IsDeleted              bool      `json:"-" db:"isdeleted"`
	CreatedAt              time.Time `json:"createdAt" db:"createdat"`
	UpdatedAt              time.Time `json:"updatedAt" db:"updatedat"`
}

// TemplateRole represents a role template for an organization type
type TemplateRole struct {
	Identifier        string `json:"identifier" db:"identifier"`
	Name              string `json:"name" db:"name"`
	OrgTypeIdentifier string `json:"orgTypeIdentifier" db:"orgtypeidentifier"`
	Description       string `json:"description" db:"description"`
}

// CreateRoleRequest represents the request body for creating a new custom role
type CreateRoleRequest struct {
	Name                   string `json:"name"`
	TemplateRoleIdentifier string `json:"templateRoleIdentifier"`
	Description            string `json:"description"`
}

// RoleAssignment represents a role assignment that prevents deletion
type RoleAssignment struct {
	MembershipId uuid.UUID `json:"membershipId" db:"membershipid"`
	Scope        string    `json:"scope" db:"scope"` // "organization", "devicegroup", or "locationgroup"
}
