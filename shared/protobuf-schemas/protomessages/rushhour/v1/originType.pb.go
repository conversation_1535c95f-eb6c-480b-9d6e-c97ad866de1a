// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.30.2
// source: rushhour/v1/originType.proto

package rushhourv1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// OriginType defines where the message originated from
type OriginType int32

const (
	OriginType_ORIGIN_UNKNOWN  OriginType = 0
	OriginType_ORIGIN_GATEWAY  OriginType = 1
	OriginType_ORIGIN_FSA      OriginType = 2
	OriginType_ORIGIN_RUSHHOUR OriginType = 3
	OriginType_ORIGIN_ONRAMP   OriginType = 4
)

// Enum value maps for OriginType.
var (
	OriginType_name = map[int32]string{
		0: "ORIGIN_UNKNOWN",
		1: "ORIGIN_GATEWAY",
		2: "ORIGIN_FSA",
		3: "ORIGIN_RUSHHOUR",
		4: "ORIGIN_ONRAMP",
	}
	OriginType_value = map[string]int32{
		"ORIGIN_UNKNOWN":  0,
		"ORIGIN_GATEWAY":  1,
		"ORIGIN_FSA":      2,
		"ORIGIN_RUSHHOUR": 3,
		"ORIGIN_ONRAMP":   4,
	}
)

func (x OriginType) Enum() *OriginType {
	p := new(OriginType)
	*p = x
	return p
}

func (x OriginType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OriginType) Descriptor() protoreflect.EnumDescriptor {
	return file_rushhour_v1_originType_proto_enumTypes[0].Descriptor()
}

func (OriginType) Type() protoreflect.EnumType {
	return &file_rushhour_v1_originType_proto_enumTypes[0]
}

func (x OriginType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OriginType.Descriptor instead.
func (OriginType) EnumDescriptor() ([]byte, []int) {
	return file_rushhour_v1_originType_proto_rawDescGZIP(), []int{0}
}

var File_rushhour_v1_originType_proto protoreflect.FileDescriptor

const file_rushhour_v1_originType_proto_rawDesc = "" +
	"\n" +
	"\x1crushhour/v1/originType.proto\x12\vrushhour.v1*l\n" +
	"\n" +
	"OriginType\x12\x12\n" +
	"\x0eORIGIN_UNKNOWN\x10\x00\x12\x12\n" +
	"\x0eORIGIN_GATEWAY\x10\x01\x12\x0e\n" +
	"\n" +
	"ORIGIN_FSA\x10\x02\x12\x13\n" +
	"\x0fORIGIN_RUSHHOUR\x10\x03\x12\x11\n" +
	"\rORIGIN_ONRAMP\x10\x04BGZEsynapse-its.com/protobuf-schemas/protomessages/rushhour/v1;rushhourv1b\x06proto3"

var (
	file_rushhour_v1_originType_proto_rawDescOnce sync.Once
	file_rushhour_v1_originType_proto_rawDescData []byte
)

func file_rushhour_v1_originType_proto_rawDescGZIP() []byte {
	file_rushhour_v1_originType_proto_rawDescOnce.Do(func() {
		file_rushhour_v1_originType_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_rushhour_v1_originType_proto_rawDesc), len(file_rushhour_v1_originType_proto_rawDesc)))
	})
	return file_rushhour_v1_originType_proto_rawDescData
}

var file_rushhour_v1_originType_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_rushhour_v1_originType_proto_goTypes = []any{
	(OriginType)(0), // 0: rushhour.v1.OriginType
}
var file_rushhour_v1_originType_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_rushhour_v1_originType_proto_init() }
func file_rushhour_v1_originType_proto_init() {
	if File_rushhour_v1_originType_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_rushhour_v1_originType_proto_rawDesc), len(file_rushhour_v1_originType_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_rushhour_v1_originType_proto_goTypes,
		DependencyIndexes: file_rushhour_v1_originType_proto_depIdxs,
		EnumInfos:         file_rushhour_v1_originType_proto_enumTypes,
	}.Build()
	File_rushhour_v1_originType_proto = out.File
	file_rushhour_v1_originType_proto_goTypes = nil
	file_rushhour_v1_originType_proto_depIdxs = nil
}
