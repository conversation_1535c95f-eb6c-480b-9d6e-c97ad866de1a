// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.30.2
// source: rushhour/v1/socketEnvelope.proto

package rushhourv1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// SocketEnvelope contains metadata for all Socket.IO messages
type SocketEnvelope struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Type           EnvelopeType           `protobuf:"varint,1,opt,name=type,proto3,enum=rushhour.v1.EnvelopeType" json:"type,omitempty"`
	RequestId      uint32                 `protobuf:"varint,2,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	UserId         string                 `protobuf:"bytes,3,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	DeviceId       string                 `protobuf:"bytes,4,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	OrganizationId string                 `protobuf:"bytes,5,opt,name=organization_id,json=organizationId,proto3" json:"organization_id,omitempty"`
	Origin         OriginType             `protobuf:"varint,6,opt,name=origin,proto3,enum=rushhour.v1.OriginType" json:"origin,omitempty"`
	Payload        []byte                 `protobuf:"bytes,7,opt,name=payload,proto3" json:"payload,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *SocketEnvelope) Reset() {
	*x = SocketEnvelope{}
	mi := &file_rushhour_v1_socketEnvelope_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SocketEnvelope) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SocketEnvelope) ProtoMessage() {}

func (x *SocketEnvelope) ProtoReflect() protoreflect.Message {
	mi := &file_rushhour_v1_socketEnvelope_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SocketEnvelope.ProtoReflect.Descriptor instead.
func (*SocketEnvelope) Descriptor() ([]byte, []int) {
	return file_rushhour_v1_socketEnvelope_proto_rawDescGZIP(), []int{0}
}

func (x *SocketEnvelope) GetType() EnvelopeType {
	if x != nil {
		return x.Type
	}
	return EnvelopeType_ENVELOPE_UNKNOWN
}

func (x *SocketEnvelope) GetRequestId() uint32 {
	if x != nil {
		return x.RequestId
	}
	return 0
}

func (x *SocketEnvelope) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *SocketEnvelope) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *SocketEnvelope) GetOrganizationId() string {
	if x != nil {
		return x.OrganizationId
	}
	return ""
}

func (x *SocketEnvelope) GetOrigin() OriginType {
	if x != nil {
		return x.Origin
	}
	return OriginType_ORIGIN_UNKNOWN
}

func (x *SocketEnvelope) GetPayload() []byte {
	if x != nil {
		return x.Payload
	}
	return nil
}

var File_rushhour_v1_socketEnvelope_proto protoreflect.FileDescriptor

const file_rushhour_v1_socketEnvelope_proto_rawDesc = "" +
	"\n" +
	" rushhour/v1/socketEnvelope.proto\x12\vrushhour.v1\x1a\x1erushhour/v1/envelopeType.proto\x1a\x1crushhour/v1/originType.proto\"\x88\x02\n" +
	"\x0eSocketEnvelope\x12-\n" +
	"\x04type\x18\x01 \x01(\x0e2\x19.rushhour.v1.EnvelopeTypeR\x04type\x12\x1d\n" +
	"\n" +
	"request_id\x18\x02 \x01(\rR\trequestId\x12\x17\n" +
	"\auser_id\x18\x03 \x01(\tR\x06userId\x12\x1b\n" +
	"\tdevice_id\x18\x04 \x01(\tR\bdeviceId\x12'\n" +
	"\x0forganization_id\x18\x05 \x01(\tR\x0eorganizationId\x12/\n" +
	"\x06origin\x18\x06 \x01(\x0e2\x17.rushhour.v1.OriginTypeR\x06origin\x12\x18\n" +
	"\apayload\x18\a \x01(\fR\apayloadBGZEsynapse-its.com/protobuf-schemas/protomessages/rushhour/v1;rushhourv1b\x06proto3"

var (
	file_rushhour_v1_socketEnvelope_proto_rawDescOnce sync.Once
	file_rushhour_v1_socketEnvelope_proto_rawDescData []byte
)

func file_rushhour_v1_socketEnvelope_proto_rawDescGZIP() []byte {
	file_rushhour_v1_socketEnvelope_proto_rawDescOnce.Do(func() {
		file_rushhour_v1_socketEnvelope_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_rushhour_v1_socketEnvelope_proto_rawDesc), len(file_rushhour_v1_socketEnvelope_proto_rawDesc)))
	})
	return file_rushhour_v1_socketEnvelope_proto_rawDescData
}

var file_rushhour_v1_socketEnvelope_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_rushhour_v1_socketEnvelope_proto_goTypes = []any{
	(*SocketEnvelope)(nil), // 0: rushhour.v1.SocketEnvelope
	(EnvelopeType)(0),      // 1: rushhour.v1.EnvelopeType
	(OriginType)(0),        // 2: rushhour.v1.OriginType
}
var file_rushhour_v1_socketEnvelope_proto_depIdxs = []int32{
	1, // 0: rushhour.v1.SocketEnvelope.type:type_name -> rushhour.v1.EnvelopeType
	2, // 1: rushhour.v1.SocketEnvelope.origin:type_name -> rushhour.v1.OriginType
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_rushhour_v1_socketEnvelope_proto_init() }
func file_rushhour_v1_socketEnvelope_proto_init() {
	if File_rushhour_v1_socketEnvelope_proto != nil {
		return
	}
	file_rushhour_v1_envelopeType_proto_init()
	file_rushhour_v1_originType_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_rushhour_v1_socketEnvelope_proto_rawDesc), len(file_rushhour_v1_socketEnvelope_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_rushhour_v1_socketEnvelope_proto_goTypes,
		DependencyIndexes: file_rushhour_v1_socketEnvelope_proto_depIdxs,
		MessageInfos:      file_rushhour_v1_socketEnvelope_proto_msgTypes,
	}.Build()
	File_rushhour_v1_socketEnvelope_proto = out.File
	file_rushhour_v1_socketEnvelope_proto_goTypes = nil
	file_rushhour_v1_socketEnvelope_proto_depIdxs = nil
}
