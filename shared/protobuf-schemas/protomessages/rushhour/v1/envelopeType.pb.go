// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.30.2
// source: rushhour/v1/envelopeType.proto

package rushhourv1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// EnvelopeType defines the type of message envelope
type EnvelopeType int32

const (
	EnvelopeType_ENVELOPE_UNKNOWN          EnvelopeType = 0
	EnvelopeType_ENVELOPE_WRAPPER_COMMAND  EnvelopeType = 1
	EnvelopeType_ENVELOPE_WRAPPER_RESPONSE EnvelopeType = 2
)

// Enum value maps for EnvelopeType.
var (
	EnvelopeType_name = map[int32]string{
		0: "ENVELOPE_UNKNOWN",
		1: "ENVELOPE_WRAPPER_COMMAND",
		2: "ENVELOPE_WRAPPER_RESPONSE",
	}
	EnvelopeType_value = map[string]int32{
		"ENVELOPE_UNKNOWN":          0,
		"ENVELOPE_WRAPPER_COMMAND":  1,
		"ENVELOPE_WRAPPER_RESPONSE": 2,
	}
)

func (x EnvelopeType) Enum() *EnvelopeType {
	p := new(EnvelopeType)
	*p = x
	return p
}

func (x EnvelopeType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EnvelopeType) Descriptor() protoreflect.EnumDescriptor {
	return file_rushhour_v1_envelopeType_proto_enumTypes[0].Descriptor()
}

func (EnvelopeType) Type() protoreflect.EnumType {
	return &file_rushhour_v1_envelopeType_proto_enumTypes[0]
}

func (x EnvelopeType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EnvelopeType.Descriptor instead.
func (EnvelopeType) EnumDescriptor() ([]byte, []int) {
	return file_rushhour_v1_envelopeType_proto_rawDescGZIP(), []int{0}
}

var File_rushhour_v1_envelopeType_proto protoreflect.FileDescriptor

const file_rushhour_v1_envelopeType_proto_rawDesc = "" +
	"\n" +
	"\x1erushhour/v1/envelopeType.proto\x12\vrushhour.v1*a\n" +
	"\fEnvelopeType\x12\x14\n" +
	"\x10ENVELOPE_UNKNOWN\x10\x00\x12\x1c\n" +
	"\x18ENVELOPE_WRAPPER_COMMAND\x10\x01\x12\x1d\n" +
	"\x19ENVELOPE_WRAPPER_RESPONSE\x10\x02BGZEsynapse-its.com/protobuf-schemas/protomessages/rushhour/v1;rushhourv1b\x06proto3"

var (
	file_rushhour_v1_envelopeType_proto_rawDescOnce sync.Once
	file_rushhour_v1_envelopeType_proto_rawDescData []byte
)

func file_rushhour_v1_envelopeType_proto_rawDescGZIP() []byte {
	file_rushhour_v1_envelopeType_proto_rawDescOnce.Do(func() {
		file_rushhour_v1_envelopeType_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_rushhour_v1_envelopeType_proto_rawDesc), len(file_rushhour_v1_envelopeType_proto_rawDesc)))
	})
	return file_rushhour_v1_envelopeType_proto_rawDescData
}

var file_rushhour_v1_envelopeType_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_rushhour_v1_envelopeType_proto_goTypes = []any{
	(EnvelopeType)(0), // 0: rushhour.v1.EnvelopeType
}
var file_rushhour_v1_envelopeType_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_rushhour_v1_envelopeType_proto_init() }
func file_rushhour_v1_envelopeType_proto_init() {
	if File_rushhour_v1_envelopeType_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_rushhour_v1_envelopeType_proto_rawDesc), len(file_rushhour_v1_envelopeType_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_rushhour_v1_envelopeType_proto_goTypes,
		DependencyIndexes: file_rushhour_v1_envelopeType_proto_depIdxs,
		EnumInfos:         file_rushhour_v1_envelopeType_proto_enumTypes,
	}.Build()
	File_rushhour_v1_envelopeType_proto = out.File
	file_rushhour_v1_envelopeType_proto_goTypes = nil
	file_rushhour_v1_envelopeType_proto_depIdxs = nil
}
