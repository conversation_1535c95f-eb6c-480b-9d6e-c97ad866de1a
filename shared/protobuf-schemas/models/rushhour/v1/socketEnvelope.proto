syntax = "proto3";

package rushhour.v1;

import "rushhour/v1/envelopeType.proto";
import "rushhour/v1/originType.proto";

option go_package = "synapse-its.com/protobuf-schemas/protomessages/rushhour/v1;rushhourv1";

// SocketEnvelope contains metadata for all Socket.IO messages
message SocketEnvelope {
  EnvelopeType type = 1;
  uint32 request_id = 2;
  string user_id = 3;
  string device_id = 4;
  string organization_id = 5;
  OriginType origin = 6;
  bytes payload = 7;
}